The generated ODPI-C documentation is at
https://oracle.github.io/odpi/doc/index.html

This directory contains the documentation source. It is written using reST
(re-Structured Text) format source files which are processed using Sphinx and
turned into HTML, PDF or ePub documents. If you wish to build these yourself,
you need to install Sphinx. Sphinx is available on many Linux distributions as
a pre-built package. You can also install Sphinx on all platforms using the
Python package manager "pip". For more information on Sphinx, please visit this
page:

http://www.sphinx-doc.org

Once Sphinx is installed, the supplied Makefile can be used to build the
different targets. The generated ODPI-C documentation found on GitHub can be
built using the command "make html".
