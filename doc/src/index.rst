.. ODPI-C documentation main file.

Welcome to ODPI-C's |release| documentation!
============================================

**ODPI-C** is a C library that simplifies the use of common Oracle Call
Interface (OCI_) features for drivers and applications.

The ODPI-C project home page can be found
`here <https://oracle.github.io/odpi>`__ and the source code can be found
`here <https://github.com/oracle/odpi>`__.

.. _OCI: http://www.oracle.com/technetwork/database/features/oci/index.html

.. toctree::
   :numbered:
   :maxdepth: 2

   user_guide/introduction.rst
   user_guide/installation.rst
   Debugging<user_guide/debugging.rst>
   Data Types<user_guide/data_types.rst>
   Round-Trips<user_guide/round_trips.rst>
   Enumerations<enums/index.rst>
   Structures<structs/index.rst>
   Unions<unions/index.rst>
   Functions<functions/index.rst>
   Release Notes<releasenotes.rst>
   Licenses<license.rst>
