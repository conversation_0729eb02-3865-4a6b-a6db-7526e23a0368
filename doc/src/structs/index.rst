*****************
ODPI-C Structures
*****************

This chapter details the structures available in the ODPI-C library.

.. toctree::
    :maxdepth: 1
    :hidden:

    dpiAccessToken<dpiAccessToken.rst>
    dpiAnnotation<dpiAnnotation.rst>
    dpiAppContext<dpiAppContext.rst>
    dpiBytes<dpiBytes.rst>
    dpiCommonCreateParams<dpiCommonCreateParams.rst>
    dpiConnCreateParams<dpiConnCreateParams.rst>
    dpiConnInfo<dpiConnInfo.rst>
    dpiContextCreateParams<dpiContextCreateParams.rst>
    dpiData<dpiData.rst>
    dpiDataTypeInfo<dpiDataTypeInfo.rst>
    dpiEncodingInfo<dpiEncodingInfo.rst>
    dpiErrorInfo<dpiErrorInfo.rst>
    dpiIntervalDS<dpiIntervalDS.rst>
    dpiIntervalYM<dpiIntervalYM.rst>
    dpiJsonArray<dpiJsonArray.rst>
    dpiJsonNode<dpiJsonNode.rst>
    dpiJsonObject<dpiJsonObject.rst>
    dpiMsgRecipient<dpiMsgRecipient.rst>
    dpiObjectAttrInfo<dpiObjectAttrInfo.rst>
    dpiObjectTypeInfo<dpiObjectTypeInfo.rst>
    dpiPoolCreateParams<dpiPoolCreateParams.rst>
    dpiQueryInfo<dpiQueryInfo.rst>
    dpiSessionlessTransactionId<dpiSessionlessTransactionId.rst>
    dpiShardingKeyColumn<dpiShardingKeyColumn.rst>
    dpiSodaOperOptions<dpiSodaOperOptions.rst>
    dpiStmtInfo<dpiStmtInfo.rst>
    dpiStringList<dpiStringList.rst>
    dpiSubscrCreateParams<dpiSubscrCreateParams.rst>
    dpiSubscrMessage<dpiSubscrMessage.rst>
    dpiSubscrMessageQuery<dpiSubscrMessageQuery.rst>
    dpiSubscrMessageRow<dpiSubscrMessageRow.rst>
    dpiSubscrMessageTable<dpiSubscrMessageTable.rst>
    dpiTimestamp<dpiTimestamp.rst>
    dpiVectorInfo<dpiVectorInfo.rst>
    dpiVersionInfo<dpiVersionInfo.rst>
    dpiXid<dpiXid.rst>
