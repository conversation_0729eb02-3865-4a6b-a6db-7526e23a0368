*******************
ODPI-C Enumerations
*******************

This chapter details the enumerations available in the ODPI-C library.

.. toctree::
    :maxdepth: 1
    :hidden:

    dpiAuthMode<dpiAuthMode.rst>
    dpiConnCloseMode<dpiConnCloseMode.rst>
    dpiCreateMode<dpiCreateMode.rst>
    dpiDeqMode<dpiDeqMode.rst>
    dpiDeqNavigation<dpiDeqNavigation.rst>
    dpiEventType<dpiEventType.rst>
    dpiExecMode<dpiExecMode.rst>
    dpiFetchMode<dpiFetchMode.rst>
    dpiJsonOptions<dpiJsonOptions.rst>
    dpiMessageDeliveryMode<dpiMessageDeliveryMode.rst>
    dpiMessageState<dpiMessageState.rst>
    dpiNativeTypeNum<dpiNativeTypeNum.rst>
    dpiOpCode<dpiOpCode.rst>
    dpiOracleTypeNum<dpiOracleTypeNum.rst>
    dpiPoolCloseMode<dpiPoolCloseMode.rst>
    dpiPoolGetMode<dpiPoolGetMode.rst>
    dpiPurity<dpiPurity.rst>
    dpiServerType<dpiServerType.rst>
    dpiShutdownMode<dpiShutdownMode.rst>
    dpiSodaFlags<dpiSodaFlags.rst>
    dpiStartupMode<dpiStartupMode.rst>
    dpiStatementType<dpiStatementType.rst>
    dpiSubscrGroupingClass<dpiSubscrGroupingClass.rst>
    dpiSubscrGroupingType<dpiSubscrGroupingType.rst>
    dpiSubscrNamespace<dpiSubscrNamespace.rst>
    dpiSubscrProtocol<dpiSubscrProtocol.rst>
    dpiSubscrQOS<dpiSubscrQOS.rst>
    dpiTpcBeginFlags<dpiTpcBeginFlags.rst>
    dpiTpcEndFlags<dpiTpcEndFlags.rst>
    dpiVectorFlags<dpiVectorFlags.rst>
    dpiVectorFormat<dpiVectorFormat.rst>
    dpiVisibility<dpiVisibility.rst>
