****************
ODPI-C Functions
****************

This chapter details the functions available in the ODPI-C library.

.. toctree::
    :maxdepth: 1
    :hidden:

    Connection Functions<dpiConn.rst>
    Context Functions<dpiContext.rst>
    Data Functions<dpiData.rst>
    Dequeue Options Functions<dpiDeqOptions.rst>
    Enqueue Options Functions<dpiEnqOptions.rst>
    JSON Functions<dpiJson.rst>
    LOB Functions<dpiLob.rst>
    Message Properties Functions<dpiMsgProps.rst>
    Object Functions<dpiObject.rst>
    Object Attribute Functions<dpiObjectAttr.rst>
    Object Type Functions<dpiObjectType.rst>
    Pool Functions<dpiPool.rst>
    Queue Functions<dpiQueue.rst>
    Rowid Functions<dpiRowid.rst>
    SODA Collection Functions<dpiSodaColl.rst>
    SODA Collection Cursor Functions<dpiSodaCollCursor.rst>
    SODA Database Functions<dpiSodaDb.rst>
    SODA Document Functions<dpiSodaDoc.rst>
    SODA Document Cursor Functions<dpiSodaDocCursor.rst>
    Statement Functions<dpiStmt.rst>
    Subscription Functions<dpiSubscr.rst>
    Variable Functions<dpiVar.rst>
    Vector Functions<dpiVector.rst>
