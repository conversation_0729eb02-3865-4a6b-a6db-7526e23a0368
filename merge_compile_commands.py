#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import sys

def merge_compile_commands():
    # Merge two compile_commands.json files
    files = [
        'daemon_logon_ftk/build/compile_commands.json',
        'libsrc/libkskyb/build/compile_commands.json'
    ]
    
    merged = []
    
    for file_path in files:
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
                merged.extend(data)
                print(f"Added {len(data)} entries from {file_path}")
        except FileNotFoundError:
            print(f"Warning: {file_path} not found")
        except json.JSONDecodeError:
            print(f"Error: Invalid JSON in {file_path}")

    # Save merged result to project root
    with open('compile_commands.json', 'w') as f:
        json.dump(merged, f, indent=2)

    print(f"Merged compile_commands.json created with {len(merged)} entries")

if __name__ == "__main__":
    merge_compile_commands()
