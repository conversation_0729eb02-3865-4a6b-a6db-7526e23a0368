# ODPI-C

Oracle Database Programming Interface for C (ODPI-C) is an open source library
of C code that simplifies access to Oracle Database for applications written in
C or C++.  It is a wrapper over [Oracle Call Interface
(OCI)](http://www.oracle.com/technetwork/database/features/oci/index.html) that
makes applications and language interfaces easier to develop.

ODPI-C supports basic and advanced features of Oracle Database and
Oracle Client.  See the [homepage](https://oracle.github.io/odpi/) for
a list.

## Installation

See [ODPI-C Installation](https://oracle.github.io/odpi/doc/installation.html).

## Documentation

See the [ODPI-C Documentation](https://oracle.github.io/odpi/doc/index.html) and
[Release Notes](https://oracle.github.io/odpi/doc/releasenotes.html).

## Samples

See [/samples](https://github.com/oracle/odpi/tree/main/samples).

## Help

Please report bugs and ask questions using [GitHub issues](https://github.com/oracle/odpi/issues).

## Tests

See [/test](https://github.com/oracle/odpi/tree/main/test).

## Contributing

See [CONTRIBUTING](https://github.com/oracle/odpi/blob/main/CONTRIBUTING.md).

## Drivers Using ODPI-C

Oracle Drivers:
* [python-oracledb](https://oracle.github.io/python-oracledb) Python interface (previously known as cx_Oracle).
* [node-oracledb](https://oracle.github.io/node-oracledb) Node.js module.

Third-party Drivers:
* [godror](https://github.com/godror/godror) Go Driver.
* [odpic-raw](https://github.com/leptonyu/odpic-raw)  Haskell Raw Bindings.
* [oracle-simple](https://github.com/haskell-oracle/oracle-simple) Haskell driver.
* [ruby-ODPI ](https://github.com/kubo/ruby-odpi) Ruby Interface.
* [rust-oracle ](https://github.com/kubo/rust-oracle) Driver for Rust.
* [Oracle.jl](https://github.com/felipenoris/Oracle.jl) Driver for Julia.
* [oranif](https://github.com/KonnexionsGmbH/oranif) Driver for Erlang.
* [nimodpi](https://github.com/mikra01/nimodpi) Driver for Nim.

## Security

Please consult the [security guide](./SECURITY.md) for our responsible security
vulnerability disclosure process.

## License

Copyright (c) 2016, 2025, Oracle and/or its affiliates.

This software is dual-licensed to you under the Universal Permissive License
(UPL) 1.0 as shown at https://oss.oracle.com/licenses/upl and Apache License
2.0 as shown at http://www.apache.org/licenses/LICENSE-2.0. You may choose
either license.

If you elect to accept the software under the Apache License, Version 2.0,
the following applies:

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

   https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
