/*
 * SocketTCP.cpp
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#include "SocketTCP.h"
#include "myException.h"
#include <strings.h>
#include <iostream>
#include <unistd.h>
using namespace std;

const int MSG_HEADER_LEN = 12;

namespace KSKYB
{

CSocketTCP::CSocketTCP(int nPortNumb)
{
	m_nPortNumb = nPortNumb;
	m_nBlocking = 1;

	try {
		if ((m_nSocketId = socket(AF_INET, SOCK_STREAM, 0)) == -1)
			throw new myException(0, "CSocketTCP::CSocketTCP() Socket()");
	}
	catch (myException* excp) {
		excp->response();
		delete excp;
		return;
	}

	m_ClientAddr.sin_family = AF_INET;
	m_ClientAddr.sin_addr.s_addr = htonl(INADDR_ANY);
	m_ClientAddr.sin_port = htons(m_nPortNumb);
}

CSocketTCP::~CSocketTCP()
{
	close(m_nSocketId);
}

void CSocketTCP::setDebug(int debugToggle)
{
	try {
		if (setsockopt(m_nSocketId, SOL_SOCKET, SO_DEBUG, (char *)&debugToggle, sizeof(debugToggle)) == -1)
			throw new myException(0, "CSocketTCP::setDebug()");
	}
	catch (myException* excp) {
		excp->response();
		delete excp;
	}
}

void CSocketTCP::setReuseAddr(int reuseToggle)
{
	try {
		if (setsockopt(m_nSocketId, SOL_SOCKET, SO_REUSEADDR, (char *)&reuseToggle, sizeof(reuseToggle)) == -1)
			throw myException(0, "CSocketTCP::setReuseAddr()");
	}
	catch (myException* excp) {
		excp->response();
		delete excp;
	}
}

void CSocketTCP::setKeepAlive(int aliveToggle)
{
	try {
		if (setsockopt(m_nSocketId, SOL_SOCKET, SO_KEEPALIVE, (char *)&aliveToggle, sizeof(aliveToggle)) == -1)
			throw new myException(0, "CSocketTCP::setKeepAlive()");
	}
	catch (myException* excp) {
		excp->response();
		delete excp;
	}
}

void CSocketTCP::setLingerOnOff(bool lingerOn)
{
	struct linger lingerOption;

	if (lingerOn)
		lingerOption.l_onoff = 1;
	else
		lingerOption.l_onoff = 0;

	try {
		if (setsockopt(m_nSocketId, SOL_SOCKET, SO_LINGER, (char *)&lingerOption, sizeof(struct linger)) == -1)
			throw myException(0, "CSocketTCP::setLingerOnOff()");
	}
	catch (myException* excp) {
		excp->response();
		delete excp;
	}
}

void CSocketTCP::setLingerSeconds(int seconds)
{
	struct linger lingerOption;

	if (seconds > 0) {
		lingerOption.l_linger = seconds;
		lingerOption.l_onoff = 1;
	}
	else
		lingerOption.l_onoff = 0;

	try {
		if (setsockopt(m_nSocketId, SOL_SOCKET, SO_LINGER, (char *)&lingerOption, sizeof(struct linger)) == -1)
			throw myException(0, "CSocketTCP::setLingerSeconds()");
	}
	catch (myException* excp) {
		excp->response();
		delete excp;
	}
}

void CSocketTCP::setLingerSeconds(int onoff, int seconds)
{
	struct linger lingerOption;
	lingerOption.l_onoff = onoff;
	lingerOption.l_linger = seconds;

	try {
		if (setsockopt(m_nSocketId, SOL_SOCKET, SO_LINGER, (char *)&lingerOption, sizeof(struct linger)) == -1)
			throw myException(0, "CSocketTCP::setLingerSeconds()");
	}
	catch (myException* excp) {
		excp->response();
		delete excp;
	}
}

void CSocketTCP::setSocketBlocking(int blockingToggle)
{
	m_nBlocking = blockingToggle;
	try {
		int flags;
		int sresp = 0;
		flags = fcntl(m_nSocketId, F_GETFL, 0);
		if (blockingToggle == 1)
			sresp = fcntl(m_nSocketId, F_SETFL, flags|O_NONBLOCK);
		else
			sresp = fcntl(m_nSocketId, F_SETFL, flags&(~O_NONBLOCK));
		
		if (sresp == -1)
			throw new myException(0, "CSocketTCP::setSocketBlocking()");
	}
	catch (myException* excp) {
		excp->response();
		delete excp;
	}
}

void CSocketTCP::setSendBufSize(int sendBufSize)
{
	try {
		if (setsockopt(m_nSocketId, SOL_SOCKET, SO_SNDBUF, (char *)&sendBufSize, sizeof(sendBufSize)) == -1)
			throw new myException(0, "CSocketTCP::setSendBufSize()");
	}
	catch (myException* excp) {
		excp->response();
		delete excp;
	}
}

void CSocketTCP::setReceiveBufSize(int receiveBufSize)
{
	try {
		if (setsockopt(m_nSocketId, SOL_SOCKET, SO_RCVBUF, (char *)&receiveBufSize, sizeof(receiveBufSize)) == -1)
			throw new myException(0, "CSocketTCP::setReceiveBufSize()");
	}
	catch (myException* excp) {
		excp->response();
		delete excp;
	}
}

int CSocketTCP::sendMessage(char* message, int nSize)
{
	int numBytes;
	try {
		if (numBytes = send(m_nSocketId, message, nSize, 0) <= 0)
			throw new myException(0, "CSocketTCP::sendMessage() Send");
	}
	catch (myException* excp) {
		excp->response();
		delete excp;
		return -1;
	}
	return numBytes;
}

int CSocketTCP::recieveMessage(char* message)
{
	int numBytes = 0;
	try {
		numBytes = recv(m_nSocketId, message, MSG_HEADER_LEN, 0);
		if (numBytes <= 0)
			throw new myException(0, "CSocketTCP::recieveMessage() Recv HDR");
		
		HEADER_KTC *pHeader = (HEADER_KTC*)message;
		
		numBytes = recv(m_nSocketId, message + MSG_HEADER_LEN, ntohl(pHeader->msgLeng), 0);
//		cout<<"ntohl(pHeader->msgLeng):"<<ntohl(pHeader->msgLeng)<<endl;
		if (numBytes <= 0)
			throw new myException(0, "CSocketTCP::recieveMessage() Recv BDY");
	}
	catch (myException* excp) {
		excp->response();
		delete excp;
		return -1;
	}
	return numBytes;
}


int CSocketTCP::checkSelect(int nSpanTime)
{
	struct timeval outtime;
	fd_set readfds;
	fd_set tempfds;
	FD_ZERO(&readfds);
	FD_SET(m_nSocketId, &readfds);

	tempfds = readfds;
	outtime.tv_sec = 0;
	outtime.tv_usec = nSpanTime;
	return select(m_nSocketId + 1, (fd_set*)&tempfds, (fd_set*)0, (fd_set*)0, &outtime);
}

int CSocketTCP::checkSelect(int nSec, int nUsec)
{
	struct timeval outtime;
	fd_set readfds;
	fd_set tempfds;
	FD_ZERO(&readfds);
	FD_SET(m_nSocketId, &readfds);

	tempfds = readfds;
	outtime.tv_sec = nSec;
	outtime.tv_usec = nUsec;
	return select(m_nSocketId + 1, (fd_set*)&tempfds, (fd_set*)0, (fd_set*)0, &outtime);
}

void CSocketTCP::bindSocket()
{
	try {
		if (bind(m_nSocketId, (struct sockaddr *)&m_ClientAddr, sizeof(struct sockaddr_in)) == -1)
			throw new myException(0, "CSocketTCP::bindSocket() Bind");
	}
	catch (myException* excp) {
		excp->response();
		delete excp;
	}
}

void CSocketTCP::listenToClient(int numPorts = 5)
{
	try {
		if (listen(m_nSocketId, numPorts) == -1)
			throw new myException(0, "CSocketTCP::listenToClient() Listen");
	}
	catch (myException* excp) {
		excp->response();
		delete excp;
	}
}

CSocketTCP* CSocketTCP::acceptClient()
{
	int newSocket;
	struct sockaddr_in clientAddress;
	int clientAddressLen = sizeof(clientAddress);

	try {
		if ((newSocket = accept(m_nSocketId, (struct sockaddr *)&clientAddress, (socklen_t*)&clientAddressLen)) == -1)
			throw new myException(0, "CSocketTCP::acceptClient() Accept");
	}
	catch (myException* excp) {
		excp->response();
		delete excp;
		return NULL;
	}

	CSocketTCP* retSocket = new CSocketTCP();
	retSocket->setSocketId(newSocket);
	return retSocket;
}

void CSocketTCP::connectToServer(char* szServerAddr)
{
	struct sockaddr_in serverAddress;
	bzero((char*)&serverAddress, sizeof(serverAddress));
	serverAddress.sin_family = AF_INET;
	serverAddress.sin_addr.s_addr = inet_addr(szServerAddr);
	serverAddress.sin_port = htons((short)m_nPortNumb);

	try {
		if (connect(m_nSocketId, (struct sockaddr *)&serverAddress, sizeof(serverAddress)) < 0)
			throw new myException(0, "CSocketTCP::connectToServer() Connect");
	}
	catch (myException* excp) {
		excp->response();
		delete excp;
		return;
	}
	setSendBufSize(49152);
	setReceiveBufSize(49152);
}

void CSocketTCP::SocketClose()
{
	if (m_nSocketId) {
		shutdown(m_nSocketId, 2);
	}
}

void CSocketTCP::Wait_A_Moment(int nSec, int nUsc)
{
	struct timeval subtime;
	subtime.tv_sec = nSec;
	subtime.tv_usec = nUsc;
	select(0,(fd_set*)0,(fd_set*)0,(fd_set*)0,&subtime);
}

}
