#ifndef _AES_ENCRYPTION_H_
#define _AES_ENCRYPTION_H_

#include <string>
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/aes.h>

class AESEncryption {
public:
    AESEncryption();
    ~AESEncryption();
    
    // Load encryption key from file
    bool loadKeyFromFile(const std::string& keyFilePath);
    
    // Set encryption key directly (32 hex characters for 128-bit key)
    bool setKey(const std::string& hexKey);
    
    // Encrypt data using AES-128-CTR and encode with base64
    std::string encryptAndEncode(const std::string& plaintext);
    
    // Decrypt base64 encoded data using AES-128-CTR
    std::string decodeAndDecrypt(const std::string& base64Ciphertext);
    
private:
    unsigned char key[16];  // 128-bit key
    bool keyLoaded;
    
    // Helper functions
    bool hexStringToBytes(const std::string& hex, unsigned char* bytes, size_t maxLen);
    std::string bytesToBase64(const unsigned char* data, size_t len);
    bool base64ToBytes(const std::string& base64, unsigned char** data, size_t* len);
};

#endif // _AES_ENCRYPTION_H_
