#include "Encrypt.h"

// OpenSSL 버전별 호환성 처리
#if defined(ROCKY_LINUX_9) || defined(OPENSSL_3X)
    // OpenSSL 3.x에서는 EVP API 사용
    #include <openssl/evp.h>
    #include <openssl/rand.h>

    // OpenSSL 3.x용 AES CTR 암호화 함수
    void openssl3_aes_ctr128_encrypt(const unsigned char *in, unsigned char *out,
                                   size_t length, const unsigned char *key,
                                   unsigned char ivec[AES_BLOCK_SIZE],
                                   unsigned char ecount_buf[AES_BLOCK_SIZE],
                                   unsigned int *num) {
        EVP_CIPHER_CTX *ctx = EVP_CIPHER_CTX_new();
        if (!ctx) return;

        // AES-128-CTR 모드 초기화 (key는 16바이트 고정)
        EVP_EncryptInit_ex(ctx, EVP_aes_128_ctr(), NULL, key, ivec);

        int outlen;
        EVP_EncryptUpdate(ctx, out, &outlen, in, length);

        EVP_CIPHER_CTX_free(ctx);
    }

    #define AES_ctr128_encrypt openssl3_aes_ctr128_encrypt

#elif defined(CENTOS_7X)
    // CentOS 7.x (OpenSSL 1.0.2) - modes.h 사용, AES_ctr128_encrypt 함수 사용
#elif defined(CENTOS_6X) || defined(OPENSSL_10X)
    // CentOS 6.x (OpenSSL 1.0.1) - AES_ctr128_encrypt가 다른 위치에 있을 수 있음
    // 필요한 함수 선언을 직접 추가
    #ifndef AES_ctr128_encrypt
    extern "C" {
        void AES_ctr128_encrypt(const unsigned char *in, unsigned char *out,
                               size_t length, const AES_KEY *key,
                               unsigned char ivec[AES_BLOCK_SIZE],
                               unsigned char ecount_buf[AES_BLOCK_SIZE],
                               unsigned int *num);
    }
    #endif
#endif

void Encrypt::set_key()
{
	memset(iv, 0x00, sizeof(iv));
	strcpy((char*)ckey, "GodWithSejongLee");
}

void Encrypt::set_key(const char *key)
{
	string in_key = key;
	string sha_key;

	sha_key = sha256(in_key);

	//memset(iv, 0x00, sizeof(iv));

	// SHA256 해시값(16진수 문자열)을 바이트로 변환하여 IV 설정
    for(int i = 0; i < 16; i++) {
        char hex[3] = {0,};
        hex[0] = sha_key[i*2];
        hex[1] = sha_key[i*2+1];
        unsigned int x;
        sscanf(hex, "%02x", &x);
        iv[i] = (unsigned char)x;
    }

	//memcpy(iv, sha_key.c_str(), 16);
	strcpy((char*)ckey, "GodWithSejongLee");
}

void Encrypt::set_key_from_config(const char *config_key)
{
	string in_key = config_key;
	string sha_key;

	sha_key = sha256(in_key);

	// SHA256 해시값(16진수 문자열)을 바이트로 변환하여 IV 설정
    for(int i = 0; i < 16; i++) {
        char hex[3] = {0,};
        hex[0] = sha_key[i*2];
        hex[1] = sha_key[i*2+1];
        unsigned int x;
        sscanf(hex, "%02x", &x);
        iv[i] = (unsigned char)x;
    }

	// 설정에서 받은 키를 직접 사용
	strncpy((char*)ckey, config_key, 15);
	ckey[15] = '\0';  // null terminator 보장
}

void Encrypt::init_ctr(struct ctr_state *state, const unsigned char *iv)
{
	state->num = 0;
    memset(state->ecount, 0, AES_BLOCK_SIZE);
    memset(state->ivec+16 , 0, 16);
    memcpy(state->ivec, iv, 16);
}

// encrypt == decrypt(내용은 같다)
void Encrypt::encrypt(unsigned char *indata, unsigned char *outdata, int bytes_read)
{
	int i=0;
	int mod_len=0;

#if defined(ROCKY_LINUX_9) || defined(OPENSSL_3X)
	// OpenSSL 3.x에서는 AES_KEY 구조체 대신 직접 키 사용
#else
	AES_set_encrypt_key(ckey, KEY_SIZE, &ase_key);
#endif

	if( bytes_read < BYTES_SIZE){
		struct ctr_state state;
		init_ctr(&state, iv);

#if defined(ROCKY_LINUX_9) || defined(OPENSSL_3X)
		openssl3_aes_ctr128_encrypt(indata, outdata, bytes_read, ckey, state.ivec, state.ecount, &state.num);
#else
		AES_ctr128_encrypt(indata, outdata, bytes_read, &ase_key, state.ivec, state.ecount, &state.num);
#endif
		return;
	}
	// loop block size  = [ BYTES_SIZE ]
	for(i=BYTES_SIZE; i <= bytes_read ;i+=BYTES_SIZE){
		struct ctr_state state;
		init_ctr(&state, iv);
#if defined(ROCKY_LINUX_9) || defined(OPENSSL_3X)
		openssl3_aes_ctr128_encrypt(indata, outdata, BYTES_SIZE, ckey, state.ivec, state.ecount, &state.num);
#else
		AES_ctr128_encrypt(indata, outdata, BYTES_SIZE, &ase_key, state.ivec, state.ecount, &state.num);
#endif
		indata+=BYTES_SIZE;
		outdata+=BYTES_SIZE;
	}

	mod_len = bytes_read % BYTES_SIZE;
	if( mod_len != 0 ){
		struct ctr_state state;
		init_ctr(&state, iv);
#if defined(ROCKY_LINUX_9) || defined(OPENSSL_3X)
		openssl3_aes_ctr128_encrypt(indata, outdata, mod_len, ckey, state.ivec, state.ecount, &state.num);
#else
		AES_ctr128_encrypt(indata, outdata, mod_len, &ase_key, state.ivec, state.ecount, &state.num);
#endif
	}
}

void Encrypt::decrypt(unsigned char *indata, unsigned char *outdata, int bytes_read)
{
	int i=0;
	int mod_len=0;

#if defined(ROCKY_LINUX_9) || defined(OPENSSL_3X)
	// OpenSSL 3.x에서는 AES_KEY 구조체 대신 직접 키 사용
#else
	AES_set_encrypt_key(ckey, KEY_SIZE, &ase_key);
#endif

	if( bytes_read < BYTES_SIZE)
	{
		struct ctr_state state;
		init_ctr(&state, iv);
#if defined(ROCKY_LINUX_9) || defined(OPENSSL_3X)
		openssl3_aes_ctr128_encrypt(indata, outdata, bytes_read, ckey, state.ivec, state.ecount, &state.num);
#else
		AES_ctr128_encrypt(indata, outdata, bytes_read, &ase_key, state.ivec, state.ecount, &state.num);
#endif
		return;
	}

	// loop block size  = [ BYTES_SIZE ]
	for(i=BYTES_SIZE; i <= bytes_read ;i+=BYTES_SIZE)
	{
		struct ctr_state state;
		init_ctr(&state, iv);
#if defined(ROCKY_LINUX_9) || defined(OPENSSL_3X)
		openssl3_aes_ctr128_encrypt(indata, outdata, BYTES_SIZE, ckey, state.ivec, state.ecount, &state.num);
#else
		AES_ctr128_encrypt(indata, outdata, BYTES_SIZE, &ase_key, state.ivec, state.ecount, &state.num);
#endif
		indata+=BYTES_SIZE;
		outdata+=BYTES_SIZE;
	}

	mod_len = bytes_read % BYTES_SIZE;
	if( mod_len != 0 )
	{
		struct ctr_state state;
		init_ctr(&state, iv);
#if defined(ROCKY_LINUX_9) || defined(OPENSSL_3X)
		openssl3_aes_ctr128_encrypt(indata, outdata, mod_len, ckey, state.ivec, state.ecount, &state.num);
#else
		AES_ctr128_encrypt(indata, outdata, mod_len, &ase_key, state.ivec, state.ecount, &state.num);
#endif
	}

}

std::string Encrypt::sha256(const std::string& input) {
    // SHA-256 결과를 저장할 배열
    unsigned char hash[SHA256_DIGEST_LENGTH];

    // SHA-256 컨텍스트 초기화 및 데이터 처리
    SHA256_CTX sha256;
    SHA256_Init(&sha256);
    SHA256_Update(&sha256, input.c_str(), input.size());
    SHA256_Final(hash, &sha256);

    // 결과를 16진수 문자열로 변환
    std::stringstream ss;
    for (int i = 0; i < SHA256_DIGEST_LENGTH; ++i) {
        ss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(hash[i]);
    }
    return ss.str();
}

