/*
 * myException.h
 *
 *  Created on: 2009. 9. 2.
 *      Author: Administrator
 */

#include "myException.h"
#include <iostream>

using namespace std;

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <stdarg.h>

myException::myException(int errCode, const string& errMsg)
{
	initVars();
	errorCode = errCode;
	if (errMsg[0])
		errorMsg.append(errMsg);
}

myException::myException(int	errCode,	char	 lev,	const char*	fmt, ...)
{
	char strTemp[512];
	memset(strTemp, 0x00, sizeof(strTemp));
	
	cout << "Exception Occurred::[" << errorCode << "][" << errorMsg << "]" << endl;
	va_list	arg;

	this->errorCode	= errCode;
	this->ErrLevel	= lev;
	
	cout << "Exception Occurred::[" << errorCode << "][" << errorMsg << "]" << endl;
	va_start(arg, fmt);
	vsprintf(strTemp, fmt, arg);
	va_end(arg);
	
	cout << "Exception Occurred::[" << errorCode << "][" << errorMsg << "]" << endl;
	errorMsg.append(strTemp);
	cout << "Exception Occurred::[" << errorCode << "][" << errorMsg << "]" << endl;
	
}

void myException::initVars()
{
	errorCode = 0;
	errorMsg = "";
}

void myException::response()
{
	cout << "Exception Occurred::[" << errorCode << "][" << errorMsg << "]" << endl;
	cout.flush();
}
