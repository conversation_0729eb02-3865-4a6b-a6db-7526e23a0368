#include "AESEncryption.h"
#include "base64.h"
#include <fstream>
#include <iostream>
#include <cstring>
#include <cstdlib>
#include <algorithm>
#include <cctype>

AESEncryption::AESEncryption() : keyLoaded(false) {
    memset(key, 0, sizeof(key));
}

AESEncryption::~AESEncryption() {
    // Clear sensitive data
    memset(key, 0, sizeof(key));
}

bool AESEncryption::loadKeyFromFile(const std::string& keyFilePath) {
    std::ifstream file(keyFilePath.c_str());
    if (!file.is_open()) {
        std::cerr << "Failed to open key file: " << keyFilePath << std::endl;
        return false;
    }
    
    std::string hexKey;
    std::getline(file, hexKey);
    file.close();
    
    // Remove any whitespace
    hexKey.erase(std::remove_if(hexKey.begin(), hexKey.end(), ::isspace), hexKey.end());
    
    return setKey(hexKey);
}

bool AESEncryption::setKey(const std::string& hexKey) {
    if (hexKey.length() != 32) {
        std::cerr << "Key must be exactly 32 hex characters (128 bits)" << std::endl;
        return false;
    }
    
    if (!hexStringToBytes(hexKey, key, 16)) {
        std::cerr << "Invalid hex key format" << std::endl;
        return false;
    }
    
    keyLoaded = true;
    return true;
}

std::string AESEncryption::encryptAndEncode(const std::string& plaintext) {
    if (!keyLoaded) {
        std::cerr << "Encryption key not loaded" << std::endl;
        return "";
    }

    // Generate random IV (16 bytes for AES)
    unsigned char iv[16];
    if (RAND_bytes(iv, 16) != 1) {
        std::cerr << "Failed to generate random IV" << std::endl;
        return "";
    }

    // Create and initialize the context
    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
        std::cerr << "Failed to create cipher context" << std::endl;
        return "";
    }

    // Initialize encryption operation with AES-128-CTR
    if (EVP_EncryptInit_ex(ctx, EVP_aes_128_ctr(), NULL, key, iv) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        std::cerr << "Failed to initialize encryption" << std::endl;
        return "";
    }

    // Prepare input data
    const unsigned char* input = reinterpret_cast<const unsigned char*>(plaintext.c_str());
    int inputLen = plaintext.length();

    // Allocate output buffer
    unsigned char* output = new unsigned char[inputLen];
    int outLen;

    // Encrypt the data
    if (EVP_EncryptUpdate(ctx, output, &outLen, input, inputLen) != 1) {
        delete[] output;
        EVP_CIPHER_CTX_free(ctx);
        std::cerr << "Failed to encrypt data" << std::endl;
        return "";
    }

    // Finalize encryption (not needed for CTR mode, but good practice)
    int finalLen;
    if (EVP_EncryptFinal_ex(ctx, output + outLen, &finalLen) != 1) {
        delete[] output;
        EVP_CIPHER_CTX_free(ctx);
        std::cerr << "Failed to finalize encryption" << std::endl;
        return "";
    }

    EVP_CIPHER_CTX_free(ctx);

    // Combine IV + encrypted data
    size_t totalLen = 16 + outLen + finalLen;
    unsigned char* combined = new unsigned char[totalLen];
    memcpy(combined, iv, 16);
    memcpy(combined + 16, output, outLen + finalLen);

    // Encode to base64
    std::string result = bytesToBase64(combined, totalLen);

    // Clean up
    delete[] output;
    delete[] combined;

    return result;
}

std::string AESEncryption::decodeAndDecrypt(const std::string& base64Ciphertext) {
    if (!keyLoaded) {
        std::cerr << "Encryption key not loaded" << std::endl;
        return "";
    }

    // Decode from base64
    unsigned char* combined;
    size_t combinedLen;
    if (!base64ToBytes(base64Ciphertext, &combined, &combinedLen)) {
        std::cerr << "Failed to decode base64" << std::endl;
        return "";
    }

    if (combinedLen < 16) {
        free(combined);
        std::cerr << "Invalid ciphertext length" << std::endl;
        return "";
    }

    // Extract IV and encrypted data
    unsigned char* iv = combined;
    unsigned char* ciphertext = combined + 16;
    int ciphertextLen = combinedLen - 16;

    // Create and initialize the context
    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
        free(combined);
        std::cerr << "Failed to create cipher context" << std::endl;
        return "";
    }

    // Initialize decryption operation with AES-128-CTR
    if (EVP_DecryptInit_ex(ctx, EVP_aes_128_ctr(), NULL, key, iv) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        free(combined);
        std::cerr << "Failed to initialize decryption" << std::endl;
        return "";
    }

    // Allocate output buffer
    unsigned char* output = new unsigned char[ciphertextLen + 1];
    int outLen;

    // Decrypt the data
    if (EVP_DecryptUpdate(ctx, output, &outLen, ciphertext, ciphertextLen) != 1) {
        delete[] output;
        EVP_CIPHER_CTX_free(ctx);
        free(combined);
        std::cerr << "Failed to decrypt data" << std::endl;
        return "";
    }

    // Finalize decryption
    int finalLen;
    if (EVP_DecryptFinal_ex(ctx, output + outLen, &finalLen) != 1) {
        delete[] output;
        EVP_CIPHER_CTX_free(ctx);
        free(combined);
        std::cerr << "Failed to finalize decryption" << std::endl;
        return "";
    }

    EVP_CIPHER_CTX_free(ctx);

    // Null terminate
    output[outLen + finalLen] = '\0';

    std::string result(reinterpret_cast<char*>(output));

    // Clean up
    delete[] output;
    free(combined);

    return result;
}

bool AESEncryption::hexStringToBytes(const std::string& hex, unsigned char* bytes, size_t maxLen) {
    if (hex.length() % 2 != 0 || hex.length() / 2 > maxLen) {
        return false;
    }
    
    for (size_t i = 0; i < hex.length(); i += 2) {
        std::string byteString = hex.substr(i, 2);
        char* endPtr;
        unsigned long byte = strtoul(byteString.c_str(), &endPtr, 16);
        
        if (*endPtr != '\0' || byte > 255) {
            return false;
        }
        
        bytes[i / 2] = static_cast<unsigned char>(byte);
    }
    
    return true;
}

std::string AESEncryption::bytesToBase64(const unsigned char* data, size_t len) {
    int encodedLen = Base64encode_len(len);
    char* encoded = new char[encodedLen];
    
    int actualLen = Base64encode(encoded, reinterpret_cast<const char*>(data), len);
    
    std::string result(encoded, actualLen - 1); // -1 to exclude null terminator
    delete[] encoded;
    
    return result;
}

bool AESEncryption::base64ToBytes(const std::string& base64, unsigned char** data, size_t* len) {
    // Estimate decoded length
    size_t estimatedLen = (base64.length() * 3) / 4;
    char* decoded = new char[estimatedLen + 1];
    
    int actualLen = Base64decode(decoded, base64.c_str());
    if (actualLen < 0) {
        delete[] decoded;
        return false;
    }
    
    *data = reinterpret_cast<unsigned char*>(malloc(actualLen));
    if (*data == NULL) {
        delete[] decoded;
        return false;
    }
    
    memcpy(*data, decoded, actualLen);
    *len = actualLen;
    
    delete[] decoded;
    return true;
}
