/*
 * Properties.cpp
 *
 *  Created on: 2009. 8. 21
 *      Author: 임동규
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include "Properties.h"

namespace KSKYB {

CProperties::CProperties()
{
	first = NULL;
}

CProperties::~CProperties()
{
	Q_Entry *entries;
	if (first != NULL) {
		for (; first; first = entries) {
			entries = first->next;
			free(first->name);
			free(first->value);
			free(first);
		}
	}
}

void CProperties::load(char* szFile)
{
	char *sp;
	if ((sp = readFile(szFile, NULL)) == NULL)
		return;
	first = decodeEntry(sp);
	free(sp);
}

char* CProperties::readFile(char *szFile, int *size)
{
	FILE *fp;
	struct stat fstat;
	char *sp, *tmp;
	int c, i;

	if (size != NULL)
		*size = 0;
	if (stat(szFile, &fstat) < 0)
		return NULL;
	if ((fp = fopen(szFile, "rb")) == NULL)
		return NULL;

	sp = (char *)malloc(fstat.st_size + 1);
	for (tmp = sp, i = 0; (c = fgetc(fp)) != EOF; tmp++, i++)
		*tmp = (char)c;
	*tmp = '\0';

	fclose(fp);
	if (size != NULL)
		*size = i;
	return sp;
}

Q_Entry* CProperties::decodeEntry(char *szEntry)
{
	Q_Entry *first, *entries, *back;
	char *org, *buf, *offset;
	int eos;

	if (szEntry == NULL)
		return NULL;

	first = entries = back = NULL;

	if ((org = strdup(szEntry)) == NULL)
		printf("qsDecoder(): Memory allocation fail.");

	for (buf = offset = org, eos = 0; eos == 0;) {
		for (buf = offset; *offset != '\n' && *offset != '\0'; offset++)
			;
		if (*offset == '\0')
			eos = 1;
		else
			*offset = '\0', offset++;
		removeSpace(buf);
		if ((buf[0] == '#') || (buf[0] == '\0'))
			continue;
		back = entries;
		entries = (Q_Entry *)malloc(sizeof(Q_Entry));
		if (back != NULL)
			back->next = entries;
		if (first == NULL)
			first = entries;

		entries->value = strdup(buf);
		entries->name = makeWord(entries->value, '=');
		entries->next = NULL;

		removeSpace(entries->name);
		removeSpace(entries->value);
	}
	free(org);
	return first;
}

char* CProperties::removeSpace(char *str)
{
	int i, j;
	if (!str)
		return NULL;
	for (j = 0; str[j] == ' ' || str[j] == 9 || str[j] == '\r' || str[j] == '\n'; j++)
		;
	for (i = 0; str[j] != '\0'; i++, j++)
		str[i] = str[j];
	for (i--; (i >= 0) && (str[i] == ' ' || str[i] == 9 || str[i] == '\r' || str[i] == '\n'); i--)
		;
	str[i + 1] = '\0';
	return str;
}

char* CProperties::makeWord(char* str, char stop)
{
	char *word;
	int len, i;
	for (len = 0; ((str[len] != stop) && (str[len])); len++)
		;
	word = (char *)malloc(sizeof(char) * (len + 1));
	for (i = 0; i < len; i++)
		word[i] = str[i];
	word[i] = '\0';
	if (str[len])
		len++;
	for (i = len; str[i]; i++)
		str[i - len] = str[i];
	str[i - len] = '\0';
	return (word);
}

char* CProperties::getProperty(const char* szKey)
{
	Q_Entry *entries;
	for (entries = first; entries; entries = entries->next) {
		if (!strcmp(szKey, entries->name))
			return (entries->value);
	}
	return NULL;
}

int CProperties::getPropertyInt(const char* szKey)
{
	char *str;
	str = getProperty(szKey);
	if (str == NULL)
		return 0;
	return atoi(str);
}

}

