/******************************************************************************
 * File Name : myException.hpp
 * Creator   : LSY
 * Function  : 예외처리용 Message Class.
 * Referance : 
 * Compiler  : IBM-AIX xlC (version 5)
 * Remark    : 
 * ----------------------------------------------------------------------------
 * Version  Date       Edited By Description
 * ----------------------------------------------------------------------------
 * 0.00.01  2002/09/11 comm		 예외처리용 Class 신규생성
 ******************************************************************************/

#ifndef __MYEXCEPTION_H__
#define __MYEXCEPTION_H__

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <stdarg.h>

const int ERRMSG_LEN	= 128;

/*
 * myException Class Member Variable Description
 *	m_nErrCode  : 오류 코드
 *	m_cErrLevel : 오류 Level
 *	m_strErrMsg	: 오류에 대한 System Message
 */
 
class myException
{
public:
	int		m_nErrCode;
	char	m_cErrLevel;
	char	m_strErrMsg	[ERRMSG_LEN];

	/*
	 * 2 Argument Constructor.
	 *	Error Code, Error Level, Error Message가 포함된다.
	 */
	
myException(int 	errcd,		char	*emsg)
{
		memset(this->m_strErrMsg,	0x00, ERRMSG_LEN);

		this->m_nErrCode	= errcd;
		strcpy(this->m_strErrMsg, emsg);
}

	/*
	 * 3 Argument Constructor.
	 *	Error Code, Error Level, Error Message가 포함된다.
	 */
	myException(
		int 	errcd,
		char	lev,
		char	*emsg)
	{
		memset(this->m_strErrMsg,	0x00, ERRMSG_LEN);

		this->m_nErrCode	= errcd;
		this->m_cErrLevel	= lev;
		strcpy(this->m_strErrMsg, emsg);
	}

	/*
	 * 4 Argument Constructor.
	 *	Error Code, Error Level, Format String, ...
	 */
	myException(
		int			errcd,
		char		lev,
		const char*	fmt,
		...)
	{
		va_list	arg;

		memset(this->m_strErrMsg,	0x00, ERRMSG_LEN);

		this->m_nErrCode	= errcd;
		this->m_cErrLevel	= lev;

		va_start(arg, fmt);
		vsprintf(this->m_strErrMsg, fmt, arg);
		va_end(arg);
	}

	~myException()
	{
	}

	/*
	 * Error Message를 화면에 출력한다.
	 */
	void
	PrintErrMsg()
	{
		fprintf(stderr, "[%07d:%c]\n\t%s\n", this->m_nErrCode,
												  this->m_cErrLevel,
												  this->m_strErrMsg);
	}

};

#endif

/*
가변 인자 리스트 
> 
printf나 scanf처럼 함수의 인자 갯수가 정해져 있지 않는 함수는 어떻게 선언하죠? 

> 
갯수가 정해져 있지 않은 인자들을 `가변 인자 리스트'8라고 합니다. 이러한 인자들은 ...를 써서 선언합니다. 예를 들어 printf와 scanf는 다음과 같은 형태로 선언됩니다: 

int printf (const char *format, ...);
int scanf (const char *format, ...);



> 
근데, 이러한 가변 인자 리스트를 쓰는 함수를 만들려면 어떻게 하죠? 
> 
가변 인자를 쓰기 위해서는 헤더파일 /SFNstdarg.h에서 지원하는 다음과 같은 세가지 매크로 함수를 써야 합니다: 

void va_start (va_list ap, last); 
type va_arg (va_list ap, type); 
void va_end (va_list ap); 
먼저 가변 인자를 처리하는 부분을 시작하기 전에 va_start를 불러 주고, 다 처리한 다음에는 va_end를 불러 줍니다. 실제 처리는 va_arg를 써서 합니다. 위 세가지 함수가 모두 va_list 타입의 인자를 요구하기 때문에, 이 타입의 변수도 선언해야 합니다. 따라서 가변 인자를 처리하는 부분은 대개 다음과 같은 형태를 띄게 됩니다: 


va_list argptr;

va_start (argptr, XXX);
/* va_arg를 써서 작업함 */;
/*
va_end (argptr);

이 때, XXX는 가변 인자가 아닌 가장 마지막 인자 이름을 써 줍니다. 즉, 가변 인자를 처리하는 함수는 적어도 하나 이상의 (가변적이 아닌)고정된 인자가 있어야 합니다. 이외에도 가변 인자를 처리하는 함수를 만들려면 몇 가지 규칙을 지켜야 합니다. 


앞에서 언급한 것처럼 가변 인자를 처리하는 함수는 하나 이상의 고정된 인자가 있어야 합니다. 즉 `void foo (...)'와 같은 함수는 만들 수 없습니다. 

가변 인자를 처리하는 함수는 위의 고정된 인자를 통해서 몇 개의 가변 인자가 전달되는 지 알 수 있어야 하며, 각각의 가변 인자가 실제로 어떠한 타입인지도 알 수 있어야 합니다. 
예를 들어 FAQ 16.116.1을 보면 printf는 `format'이라는 고정 인자를 받아 들입니다. 즉, printf는 이 인자를 통해 가변 인자들의 갯수와 타입을 알아내는 것입니다. 그래서 


printf ("%d: %s\n", num, str);

printf는 첫번째 인자를 통해, 두 개의 가변 인자가 들어오는 것을 알아내며, 이 가변 인자의 타입은 차례로 정수(int), 문자열(char *)인 것을 알아냅니다. 


*/