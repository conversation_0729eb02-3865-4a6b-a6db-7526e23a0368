# Database Configuration for daemon_sky_ftk

이 프로젝트는 Pro*C 컴파일 시 데이터베이스 연결 정보가 필요합니다. 보안을 위해 데이터베이스 정보를 하드코딩하지 않고 다음 두 가지 방법 중 하나를 사용할 수 있습니다.

## Makefile 빌드

### 방법 1: 환경변수 사용 (권장 - CI/CD 환경)

빌드 전에 다음 환경변수를 설정하세요:

```bash
export DBSTRING=your_database_string
export DBID=your_database_id
export DBPASS=your_database_password

cd mak
make
```

### 방법 2: Makefile 직접 수정 (로컬 개발)

`mak/Makefile` 파일에서 다음 라인을 수정:
```makefile
DBSTRING=your_actual_database_string
DBID=your_actual_database_id
DBPASS=your_actual_database_password
```

## CMake 빌드

### 방법 1: 환경변수 사용 (권장 - CI/CD 환경)

```bash
export DBSTRING=your_database_string
export DBID=your_database_id
export DBPASS=your_database_password

mkdir -p build
cd build
cmake ..
make
```

### 방법 2: 설정 파일 사용 (권장 - 로컬 개발)

1. 템플릿 파일을 복사하여 설정 파일을 생성:
```bash
cp db_config.cmake.template db_config.cmake
```

2. `db_config.cmake` 파일을 편집하여 실제 데이터베이스 정보를 입력:
```cmake
set(DBSTRING "actual_database_string")
set(DBID "actual_database_id")
set(DBPASS "actual_database_password")
```

3. CMake 빌드 실행:
```bash
mkdir -p build
cd build
cmake ..
make
```

## 우선순위

환경변수가 설정 파일보다 우선순위가 높습니다:
1. 환경변수 (최우선)
2. 설정 파일 (db_config.cmake)
3. 둘 다 없으면 빌드 오류

## 주의사항

- 설정 파일들은 `.gitignore`에 포함되어 있어 버전 관리에서 제외됩니다.
- 실제 데이터베이스 정보를 포함한 파일을 절대 커밋하지 마세요.
- 환경변수나 설정 파일 중 어느 것도 설정되지 않으면 빌드 시 오류가 발생합니다.

## 빌드 타겟

### Makefile 타겟:
- `make all`: telco_sky_new_r 빌드
- `make install`: telco_sky_new_r_tmp를 telco_atk_sky로 이름 변경
- `make clean`: 빌드 파일 정리

### CMake 타겟:
- `make telco_sky_new`: 메인 실행파일 빌드
- `make install_atk_sky`: telco_sky_new를 telco_atk_sky로 복사
- `make install_mms_sky`: telco_sky_new를 telco_mms_sky로 복사
- `make makefile_build_daemon_sky`: 원본 Makefile로 빌드
- `make makefile_clean_daemon_sky`: 원본 Makefile로 정리

## Oracle 환경 요구사항

- Oracle Instant Client 21c
- Pro*C 컴파일러
- 적절한 ORACLE_HOME 환경변수 설정

## 실행파일

빌드 완료 후 `bin/` 디렉토리에 다음 실행파일이 생성됩니다:
- `telco_sky_new`: 메인 실행파일 (CMake 빌드)
- `telco_atk_sky` 또는 `telco_mms_sky`: Makefile 빌드 결과

## 라이브러리 의존성

이 프로젝트는 다음 라이브러리들을 사용합니다:
- libkskyb (libksbase64, libkssocket, libksconfig, libksthread)
- orapp
- Oracle client libraries (clntsh)
- OpenSSL (crypto, ssl)
- System libraries (pthread, dl, nsl)

## 디렉토리 구조

```
daemon_sky_ftk/
├── CMakeLists.txt          # CMake 빌드 설정
├── README.md               # 이 파일
├── db_config.cmake.template # 데이터베이스 설정 템플릿
├── bin/                    # 실행파일 출력 디렉토리
├── cfg/                    # 설정 파일들
├── inc/                    # 헤더 파일들
├── lib/                    # 라이브러리 소스 파일들
├── mak/                    # 원본 Makefile
├── obj/                    # 오브젝트 파일들
└── src/                    # 메인 소스 파일들
```
