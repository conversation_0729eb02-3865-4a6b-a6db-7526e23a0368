# TBL_FTALK_MSG 테이블 SQL 스크립트

이 폴더에는 `daemon_logon_ftk/src/senderFtalkProDB.cpp`의 `CSenderDbMMSMSG_FTALKUP` 구조체에서 사용되는 필드들을 지원하기 위한 `NEOFTK.TBL_FTALK_MSG` 테이블 관련 SQL 스크립트들이 포함되어 있습니다.

## 파일 목록

### 1. TBL_FTALK_MSG_CREATE.sql
- **용도**: 새로운 `TBL_FTALK_MSG` 테이블을 처음부터 생성
- **사용 시기**: 테이블이 존재하지 않는 경우
- **포함 내용**:
  - 테이블 생성 (기존 필드 + 새로운 필드)
  - 인덱스 생성
  - 제약조건 설정
  - 코멘트 추가

### 2. TBL_FTALK_MSG_ALTER.sql
- **용도**: 기존 `TBL_FTALK_MSG` 테이블에 새로운 필드들 추가
- **사용 시기**: 테이블이 이미 존재하고 데이터가 있는 경우
- **포함 내용**:
  - 새로운 필드들 추가 (ALTER TABLE)
  - 새로운 필드들에 대한 인덱스 생성
  - 코멘트 추가

## 추가된 필드들

`daemon_logon_ftk/src/senderFtalkProDB.cpp`의 `setMMSMSG2DB_FTKUP` 함수에서 사용되는 `CSenderDbMMSMSG_FTALKUP` 구조체의 필드들을 지원하기 위해 다음 필드들이 추가되었습니다:

| 테이블 필드명 | 데이터 타입 | C++ 구조체 필드 | 설명 |
|---------------|-------------|-----------------|------|
| TRAN_PRIORITY | NUMBER | nPriority | 우선순위 |
| TRAN_MMSID | NUMBER(19) | nMMSId | MMS ID (long long) |
| TRAN_ENCODING | VARCHAR2(20) | szEncoding[20+1] | 인코딩 정보 |
| TRAN_CHAT_BUBBLE_TYPE | VARCHAR2(30) | szChatBubbleType[30+1] | 채팅 버블 타입 |
| TRAN_TARGETING | CHAR(1) | szTargeting[1+1] | 타겟팅 정보 |
| TRAN_APP_USER_ID | VARCHAR2(20) | szAppUserId[20+1] | 앱 사용자 ID |
| TRAN_PUSH_ALARM | CHAR(1) | szPushAlarm[1+1] | 푸시 알람 설정 |
| TRAN_MESSAGE_VARIABLE | VARCHAR2(2000) | szMessageVariable[2000+1] | 메시지 변수 |
| TRAN_BUTTON_VARIABLE | VARCHAR2(2000) | szButtonVariable[2000+1] | 버튼 변수 |
| TRAN_COUPON_VARIABLE | VARCHAR2(1000) | szCouponVariable[1000+1] | 쿠폰 변수 |
| TRAN_IMAGE_VARIABLE | VARCHAR2(1000) | szImageVariable[1000+1] | 이미지 변수 |
| TRAN_VIDEO_VARIABLE | VARCHAR2(1000) | szVideoVariable[1000+1] | 비디오 변수 |
| TRAN_COMMERCE_VARIABLE | VARCHAR2(1000) | szCommerceVariable[1000+1] | 커머스 변수 |
| TRAN_CAROUSEL_VARIABLE | VARCHAR2(2000) | szCarouselVariable[2000+1] | 캐러셀 변수 |

## 기존 매핑된 필드들

다음 필드들은 이미 기존 테이블에 존재하여 매핑됩니다:

| C++ 구조체 필드 | 테이블 필드명 | 설명 |
|-----------------|---------------|------|
| szSenderKey | TRAN_SENDER_KEY | 발신자 키 |
| szDstAddr | TRAN_PHONE | 수신자 전화번호 |
| szTmplCd | TRAN_TMPL_CD | 템플릿 코드 |
| szResMethod | TRAN_METHOD | 응답 방법 |
| szTimeout | TRAN_TIMEOUT | 타임아웃 |
| szReserve | RESERVED | 예약 필드 |

## 사용 방법

### 새로운 테이블 생성
```sql
@TBL_FTALK_MSG_CREATE.sql
```

### 기존 테이블 업데이트
```sql
@TBL_FTALK_MSG_ALTER.sql
```

## 주의사항

1. **백업**: 기존 테이블을 수정하기 전에 반드시 백업을 수행하세요.
2. **권한**: 테이블 생성/수정 권한이 필요합니다.
3. **테이블스페이스**: `NEOFTK_DATA_COMMON` 테이블스페이스가 존재해야 합니다.
4. **인덱스**: 새로운 필드들에 대한 인덱스가 자동으로 생성됩니다.

## 관련 파일

- `daemon_logon_ftk/src/senderFtalkProDB.cpp` - 이 테이블을 사용하는 C++ 소스 코드
- `daemon_logon_ftk/inc/senderDbInfo.h` - `CSenderDbMMSMSG_FTALKUP` 구조체 정의