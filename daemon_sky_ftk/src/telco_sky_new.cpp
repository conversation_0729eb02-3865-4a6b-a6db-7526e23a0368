//============================================================================
// Name        : telco_sky_new.cpp
// Author      : KskyB Inc.
// Version     :
// Copyright   : Copyright@KSKYB
// Description : Hello World in C++, Ansi-style
//============================================================================

#include <stdio.h>
#include <iostream>
using namespace std;
#include <string.h> 
#include <signal.h>
#include <errno.h>
#include <pthread.h>

#include "Properties.h"
#include "SocketTCP.h"
#include "PacketCtrlSKY.h"
#include "myException.h"
#include "DatabaseORA.h"

#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>

#include <code_info.h>
#include <message_info.h>
#include <ml_ctrlsub.h>

#define MAX_TCP_BUF 512

typedef struct _THREAD_PARAM
{
	int sockfd;
	pthread_t tid;
	sql_context ctx;
	time_t sThisT, sLastT;
	time_t rThisT, rLastT;
	vector<string> vtBuff;
	char buff[MAX_TCP_BUF];
	char logMsg[256];
} ThreadParam;

//string sSndBuff;

KSKYB::CProperties g_prop;
KSKYB::CDatabaseORA g_oracle;
int activeProcess = true;
char PROCESS_NO[ 7], PROCESS_NAME[36];
struct _message_info message_info;
struct _shm_info *shm_info;

// Global encryption settings
string g_encryptionMode = "plain_text";
string g_keyFilePath = "";

void* procSend(void* param);
void* procRept(void* param);
int CheckThreadStatus(ThreadParam** param, int nCnt);
int BindGateway(ThreadParam* tp, int nType, CPacketCtrlSKY& packetSKY, KSKYB::CSocketTCP& sockInst);
int ClassifyResponse(int nMsgtype, ThreadParam* tp, CPacketCtrlSKY& packetSKY, KSKYB::CSocketTCP& sockInst);

void Init_Server();
void CloseProcess(int sig);
void mnt(char *buf, int st, int err);
void log(char *buf, int st, int err);

int main(int argc, char* argv[])
{
	int idx;
	
	g_prop.load(argv[1]);
	int nThreadCnt = g_prop.getPropertyInt("gw.tcnt");
	int nThreadTyp = g_prop.getPropertyInt("gw.type");

	// Load encryption settings from config
	char* encryptMode = g_prop.getProperty("encrypt.transmission");
	if (encryptMode != NULL) {
		g_encryptionMode = string(encryptMode);
	}

	char* keyFile = g_prop.getProperty("encrypt.keyfile");
	if (keyFile != NULL) {
		g_keyFilePath = string(keyFile);
	} else {
		g_keyFilePath = "cfg/aes_encryption.key";  // Default fallback
	}

	cout << "Encryption mode: " << g_encryptionMode << ", Key file: " << g_keyFilePath << endl;

	//M1. SERVER 초기화
	Init_Server();
	
	//M2. thread 갯수와 타입
	cout << "TREAD CNT:[" << nThreadCnt << "], TYP:[" << nThreadTyp << "]" << endl;
	
	//M2. process 초기화
	if (ml_sub_init(PROCESS_NO, PROCESS_NAME, (char*)0, (char*)0)<0) {
		cout << "ml_sub_init Error." << endl;
		return 0;
	}
	
	//M3. 오라클 thread 사용 가능하게 세팅
	if (g_oracle.setEnableThreads()<0)
		return 0;
	ThreadParam pParam[nThreadCnt];
	if (nThreadTyp == 0 || nThreadTyp == 1) {
		for (idx = 0; idx < nThreadCnt; idx++) {
			memset(&pParam[idx], 0x00, sizeof(ThreadParam));
			pParam[idx].sockfd = idx;
			
			//M4. oracle thread 초기화
			if (g_oracle.initThread(pParam[idx].ctx)<0 || pParam[idx].ctx==NULL) {
				cout << "g_oracle.initThread Error" << endl;
				continue;
			}
			
			//M5. thread 생성 procRept와 procSend 둘 중 하나 실행
			pthread_create(&pParam[idx].tid, NULL, nThreadTyp ? procRept : procSend, &pParam[idx]);
		}
		
		//M6. thread 상태 체크
		CheckThreadStatus((ThreadParam**)pParam, nThreadCnt);
	}
	ml_sub_end();
	return 0;
}

int CheckThreadStatus(ThreadParam** param, int nThreadCnt)
{
	char logMsg[256];
	int idx, status, nAlive;
	KSKYB::CSocketTCP sockInst;
	ThreadParam tpSub[nThreadCnt];
	memcpy(tpSub, param, sizeof(ThreadParam) * nThreadCnt);

	while (true) {
		sockInst.Wait_A_Moment(0, 10);
		for (idx = 0; idx < nThreadCnt; idx++) {
			if (pthread_kill(tpSub[idx].tid, 0) != 0) {
				pthread_join(tpSub[idx].tid, (void**)&status);
				activeProcess = false;
				break;
			}
		}
		if (activeProcess == false) break;
	}
	for (idx = 0; idx < nThreadCnt; idx++) {
		if (pthread_kill(tpSub[idx].tid, 0) != 0) {
			pthread_join(tpSub[idx].tid, (void**)&status);
			g_oracle.freeThread(tpSub[idx].ctx);
			sprintf(logMsg, "[I]pWorkThread::[%d] Closed..", idx);
			mnt(logMsg, 0, 0);
		}
	}
	return 0;
}

void* procSend(void* param)
{

	string sSndBuff;

	ThreadParam* tp = (ThreadParam*)param;
	cout << "START THREAD[" << tp->sockfd << "]" << endl;
	
	int msgSize = 0, nRet = 0;
	int mMsgSeq = 0;
	int	iLine; /**< 전송라인 */
	int nThreadCnt=0;
	
	vector<SNDSKY> send_sky;

	CPacketCtrlSKY packetSKY;

	// Set encryption settings from global config
	packetSKY.setEncryptionSettings(g_encryptionMode, g_keyFilePath);
	
	//PS0. SOCKET INIT
	KSKYB::CSocketTCP sockInst(g_prop.getPropertyInt("gw.port"));
	sockInst.connectToServer(g_prop.getProperty("gw.addr"));
	nThreadCnt = g_prop.getPropertyInt("gw.tcnt");
	iLine = g_prop.getPropertyInt("gw.line");
	sockInst.setLingerSeconds(1, 0);
	cout << "connect To Server Ok!![" << tp->sockfd << "]" << endl;
	sprintf(tp->logMsg, "[%s:%d]::[I]connect To Server Ok!!", PROCESS_NAME, tp->sockfd);
	mnt(tp->logMsg, 0, 0);
	
	//PS1. ORACLE연결
	if (g_oracle.connectToOracle(tp->ctx, g_prop.getProperty("db.uid"), g_prop.getProperty("db.dsn")) < 0) {
		cout << "connect To Oracle Fail!![" << tp->sockfd << "]" << endl;
		mnt("[C]connect To Oracle Fail!!!!", 0, 0);
		activeProcess = false;
	}
	sprintf(tp->logMsg, "[%s:%d]::[I]connect To Oracle Ok!! TID[%d]", PROCESS_NAME, tp->sockfd);
	mnt(tp->logMsg, 0, 0);
	
	send_sky.reserve(100);

	try {
		//PS2. BIND G/W SERVER연결
		if (BindGateway(tp, 0, packetSKY, sockInst) < 0)
			throw new myException(0, "[C]BindGateway Error...");
		time(&tp->sLastT);

		while (activeProcess) {
			//sockInst.Wait_A_Moment(0, 1000);
			sockInst.Wait_A_Moment(10, 1000);
			
			// PS6. GET SND DATA
			//if ((mMsgSeq = g_oracle.getSendData(tp->ctx, g_prop.getPropertyInt("gw.quid") + (g_prop.getPropertyInt("gw.serid")*100), tp->vtBuff)) > 0) {
			//
#if (DEBUG >= 5)
	sprintf(tp->logMsg, "[%s:%d]::[I] getSendData before", PROCESS_NAME, tp->sockfd);
	log(tp->logMsg, 0, 0);
#endif

			if ((mMsgSeq = g_oracle.getSendData(tp->ctx, tp->sockfd, nThreadCnt, iLine, send_sky)) > 0) {

#if (DEBUG >= 5)
	sprintf(tp->logMsg, "[%s:%d]::[I]shs getSendData ret [%d]", PROCESS_NAME, tp->sockfd, mMsgSeq);
	log(tp->logMsg, 0, 0);
	sprintf(tp->logMsg, "[%s:%d]::[I]shs send_sky size[%d]", PROCESS_NAME, tp->sockfd, send_sky.size());
	log(tp->logMsg, 0, 0);
#endif
				
				if(send_sky.size() > 0)
				{
					for(int ii = 0; ii < send_sky.size(); ii++)
					{
						//sprintf(tp->logMsg, "[%s:%d]::[I]shs send_sky size[%d]", PROCESS_NAME, tp->sockfd, send_sky.size());
						//  		log(tp->logMsg, 0, 0);
              		
						if (!activeProcess)
						{
							sprintf(tp->logMsg, "[%s:%d]::[I]activeProcess false[%d][%d]", PROCESS_NAME, tp->sockfd, send_sky.size(), ii);
								log(tp->logMsg, 0, 0);
						  
						  time(&tp->sLastT);
						  break;
						}
						/*sprintf(tp->logMsg, "[%s:%d]::[I]SND[%lld][%s][%s][%s][%s]", PROCESS_NAME, tp->sockfd, mMsgSeq, 
							string(*(itrData + 1)).c_str(), string(*(itrData + 2)).c_str(), 
							string(*(itrData + 4)).c_str(), string(*(itrData + 5)).c_str());
								
						log(tp->logMsg, 0, 0);*/
						
						if (g_oracle.setWaitUpdate(tp->ctx, (char*)"6", send_sky.at(ii)) < 0)
						{
							sprintf(tp->logMsg, "[%s:%d]::[I]setWaitUpdate 6 false[%d][%d]", PROCESS_NAME, tp->sockfd, send_sky.size(), ii);
							mnt(tp->logMsg, 0, 0);
						  throw new myException(0, "[C]g_oracle.setWaitUpdate Error...1");
						}
			  		
						sSndBuff="";
			  		
						
						//PS7. tp->buff에 전송 내용 프로토콜 규약에 맞게 세팅
						msgSize = packetSKY.getMsg_DELIVER_REQ(sSndBuff, send_sky.at(ii));
						//tp->vtBuff.clear();
						
						sprintf(tp->logMsg, "[%s:%d]::DELIVER_REQ::[%s][%s][%s][%s][%s]\n[%s][%s]", 
								PROCESS_NAME, tp->sockfd, send_sky.at(ii).s_tran_pr, 
								send_sky.at(ii).s_tran_tmpl_cd, send_sky.at(ii).s_tran_title, 
								send_sky.at(ii).s_tran_price, send_sky.at(ii).s_tran_curtype,
								send_sky.at(ii).s_rep_flag, send_sky.at(ii).s_rep_msg);
								log(tp->logMsg, 0, 0);
						
						//PS8. 소켓 SEND
						if (sockInst.sendMessage((char*)sSndBuff.c_str(), msgSize) < 0){
							if (g_oracle.setWaitUpdate(tp->ctx, (char*)"1", send_sky.at(ii)) < 0)
        			{
        				mnt(tp->logMsg, 0, 0);
        			  throw new myException(0, "[C]g_oracle.setWaitUpdate sendMessage Error...");
        			}
        		
							throw new myException(0, "[C]getMsg_DELIVER_REQ sendMessage Error...");
						}
						
						memset(tp->buff, 0x00, sizeof(tp->buff));
						
						//PS9. 소켓 전송한 메세지 수신
						if (sockInst.recieveMessage(tp->buff) < 0){
							if (g_oracle.setWaitUpdate(tp->ctx, (char*)"1", send_sky.at(ii)) < 0)
        			{
        				mnt(tp->logMsg, 0, 0);
        			  throw new myException(0, "[C]g_oracle.setWaitUpdate recieveMessage Error...");
        			}
        			
							throw new myException(0, "[C]SND recieveMessage Error...");
						}	
						//PS10. 메세지 코드에 따라 처리	
						if (ClassifyResponse(CPacketCtrlSKY::TYPE_DELIVER_ACK, tp, packetSKY, sockInst) < 0)
						{
							if (g_oracle.setWaitUpdate(tp->ctx, (char*)"1", send_sky.at(ii)) < 0)
        			{
        				mnt(tp->logMsg, 0, 0);
        			  throw new myException(0, "[C]g_oracle.setWaitUpdate ClassifyResponse Error...");
        			}
							throw new myException(0, "[C]ClassifyResponse Error..."); 
						}
						
						if(send_sky.size()-1 == ii)
						{
							send_sky.clear();
							break;	
						}
					}
				}	
			}
			else if (mMsgSeq < 0) {
					sprintf(tp->logMsg, "[%s:%d]::[C]mMsgSeq: %d", PROCESS_NAME, tp->sockfd, mMsgSeq);
					mnt(tp->logMsg, 0, 0);
					
					activeProcess = false;
					continue;
					
					
			}

			time(&tp->sThisT);
			if (difftime(tp->sThisT,tp->sLastT)>=60) {
				memset(tp->buff, 0x00, sizeof(tp->buff));
				
				//PS11. PING내용을 프로토콜 규약에 맞게 버프에 넣는다.
				msgSize = packetSKY.getMsg_PING_REQ(tp->buff);
				/*
				#ifdef DEBUG		
      		sprintf(tp->logMsg, "[DEBUG]SND_PING :\n[\n%s\n][%d]", tp->buff, strlen(tp->buff));
      		log(tp->logMsg, 0, 0);
				#endif
				*/
				
				//PS12. PING을 보낸다.
				if (sockInst.sendMessage(tp->buff, msgSize) < 0)
					throw new myException(0, "[C]getMsg_PING_REQ sendMessage Error...");

				sprintf(tp->logMsg, "[%s:%d]::[I]SND[PING]", PROCESS_NAME, tp->sockfd);
				log(tp->logMsg, 0, 0);

				time(&tp->sLastT);
				
				//PS13. 소켓 변경이 있는지 체크 3초
				nRet = sockInst.checkSelect(3,100);
				if (nRet > 0) {
					memset(tp->buff, 0x00, sizeof(tp->buff));
					
					//PS4. 소켓 내용 받아오기 
					if (sockInst.recieveMessage(tp->buff) < 0)
						throw new myException(0, "[C]SND PONG recieveMessage Error...");
					
					/*
					#ifdef DEBUG		
      			sprintf(tp->logMsg, "[DEBUG]RES_PONG :\n[\n%s\n][%d]", tp->buff, strlen(tp->buff));
      			log(tp->logMsg, 0, 0);
					#endif
					*/
					
					//PS5. 메세지 코드에 따라 처리	
					if (ClassifyResponse(CPacketCtrlSKY::TYPE_PONG, tp, packetSKY, sockInst) < 0)
						throw new myException(0, "[C]ClassifyResponse Error...");
				}
				else if (nRet < 0)
					throw new myException(0, "[C]Error Occurred(sockInst.checkSelect)....");
				
			}
		}
	}
	catch (myException* excp) {
		mnt((char*)(excp->getErrMsg().c_str()), 0, 0);
		delete excp;
	}

endProcess:
	g_oracle.closeFromOracle(tp->ctx);
	sockInst.SocketClose();
	activeProcess = false;
	cout << "OUT THREAD[" << tp->sockfd << "]" << endl;
	return NULL;
}

void* procRept(void* param)
{
	ThreadParam* tp = (ThreadParam*)param;
	cout << "START THREAD[" << tp->sockfd << "]" << endl;

	int msgSize = 0, nRet = 0;
	CPacketCtrlSKY packetSKY;

	// Set encryption settings from global config
	packetSKY.setEncryptionSettings(g_encryptionMode, g_keyFilePath);
	
	//PR0. SOCKET INIT
	KSKYB::CSocketTCP sockInst(g_prop.getPropertyInt("gw.port"));
	sockInst.connectToServer(g_prop.getProperty("gw.addr"));
	sockInst.setLingerSeconds(1, 0);
	cout << "connectToServer Ok!![" << tp->sockfd << "]" << endl;
	
	//PR1. ORACLE 연결
	if (g_oracle.connectToOracle(tp->ctx, g_prop.getProperty("db.uid"), g_prop.getProperty("db.dsn")) < 0) {
		cout << "connectToOracle Fail!![" << tp->sockfd << "]" << endl;
		activeProcess = false;
	}
	
	try {
		//PR2. BIND G/W SERVER연결
		if (BindGateway(tp, 1, packetSKY, sockInst) < 0)
			throw new myException(0, "[C]BindGateway Error...");
		while (activeProcess) {
			sockInst.Wait_A_Moment(0, 1000);
			//nRet = sockInst.checkSelect(100);
			
			//PR3. 소켓 변경이 있는지 체크 60초
			nRet = sockInst.checkSelect(60, 100);
			if (nRet > 0) {
				memset(tp->buff, 0x00, sizeof(tp->buff));
				
				//PR4. 소켓 내용 받아오기 
				if (sockInst.recieveMessage(tp->buff) < 0)
					throw new myException(0, "[C]RPT recieveMessage Error...");
/*					
#ifdef DEBUG
				sprintf(tp->logMsg, "TID[%d]::ReptRecv[%s]", tp->sockfd, tp->buff);
				log(tp->logMsg, 0, 0);
#endif
*/
				//PR5. 메세지 코드에 따라 처리	
				if (ClassifyResponse(CPacketCtrlSKY::TYPE_REPORT, tp, packetSKY, sockInst) < 0)
					throw new myException(0, "[C]ClassifyResponse Error...");
			}
			else if (nRet == 0)
				throw new myException(0, "[C]Error NOT RECV 60Sec Heart beat ");
			else if (nRet < 0)
				throw new myException(0, "[C]Error Occurred(sockInst.checkSelect)....");
		}
	}
	catch (myException* excp) {
		mnt((char*)(excp->getErrMsg().c_str()), 0, 0);
		delete excp;
	}

	g_oracle.closeFromOracle(tp->ctx);
	sockInst.SocketClose();
	activeProcess = false;
	cout << "OUT THREAD[" << tp->sockfd << "]" << endl;
	return NULL;
}


void Init_Server()
{
	setpgrp();
	cout << "Process Running.. Please wait 3 seconds." << endl;
	sleep(3);
	signal(SIGHUP, CloseProcess);
	signal(SIGCLD, SIG_IGN);
	signal(SIGPIPE, SIG_IGN);
	signal(SIGTERM, CloseProcess);
	signal(SIGINT, CloseProcess);
	signal(SIGQUIT, CloseProcess);
	signal(SIGKILL, CloseProcess);
	signal(SIGSTOP, CloseProcess);
	signal(SIGUSR1, CloseProcess);
}


int BindGateway(ThreadParam* tp, int nType, CPacketCtrlSKY& packetSKY, KSKYB::CSocketTCP& sockInst)
{
	int msgSize = 0;
	char szSID[10], szPWD[10];
 	try {
		sprintf(szSID, "gw.sid%d", tp->sockfd + 1);
		sprintf(szPWD, "gw.pwd%d", tp->sockfd + 1);
		memset(tp->buff, 0x00, sizeof(tp->buff));
		msgSize = packetSKY.getMsg_BIND_REQ(tp->buff, g_prop.getProperty(szSID), g_prop.getProperty(szPWD), nType);

		#ifdef DEBUG		
      sprintf(tp->logMsg, "[DEBUG]BIND_REQ :\n[\n%s\n][%d]", tp->buff, strlen(tp->buff));
      log(tp->logMsg, 0, 0);
		#endif

		if (sockInst.sendMessage(tp->buff, msgSize) < 0)
			throw new myException(0, "[C]BindGateway::sendMessage Error...");

		memset(tp->buff, 0x00, sizeof(tp->buff));
		if (sockInst.recieveMessage(tp->buff) < 0)
			throw new myException(0, "[C]BindGateway::recieveMessage Error...");
		
		#ifdef DEBUG		
      sprintf(tp->logMsg, "[DEBUG]BIND_ACK :\n[\n%s\n][%d]", tp->buff, strlen(tp->buff));
      log(tp->logMsg, 0, 0);
		#endif
		
		if (ClassifyResponse(CPacketCtrlSKY::TYPE_BIND_ACK, tp, packetSKY, sockInst) < 0)
			throw new myException(0, "[C]ClassifyResponse Error...")    ;
	}
	catch (myException* excp) {
		mnt((char*)(excp->getErrMsg().c_str()), 0, 0);
		delete excp;
		return -1;
	}
	return 1;
}

int ClassifyResponse(int nMsgType, ThreadParam* tp, CPacketCtrlSKY& packetSKY, KSKYB::CSocketTCP& sockInst)
{
        int msgSize = 0;
        int telcoid = 0;
        try {
                switch (nMsgType) {
                case CPacketCtrlSKY::TYPE_BIND_ACK: {
                        if (packetSKY.getData_BndAck(tp->buff, tp->vtBuff) < 0)
                                throw new myException(0, "[W]CPacketCtrlSKY::getData_BndAck Error...");
                        vector<string>::iterator itrData;
                        itrData = tp->vtBuff.begin();
                        sprintf(tp->logMsg, "[%s:%d]::[I]BND_ACK[%s] DESC[%s]", PROCESS_NAME, tp->sockfd, string(*itrData).c_str(), string(*(itrData + 1)).c_str());
                        
                        log(tp->logMsg, 0, 0);

                        if (atoi(string(*itrData).c_str()) != 100)
                                throw new myException(0, "[W]CPacketCtrlSKY::Bind Result Value Error...");
                        
                        	sprintf(tp->logMsg, "[%s:%d]::[I]bind    To Server Ok!!", PROCESS_NAME, tp->sockfd);
									mnt(tp->logMsg, 0, 0);
	
                        tp->vtBuff.clear();
                        break;
                }
                case CPacketCtrlSKY::TYPE_PING:
//                      sprintf(tp->logMsg, "TID[%d]::PING", tp->sockfd);
//                      mnt(tp->logMsg, 0, 0);
//                        msgSize = packetSKY.getMsg_PING_RES(tp->buff);
//                       if (sockInst.sendMessage(tp->buff, msgSize) < 0)
//                                throw new myException(0, "sendMessage Error...");
                        break;
                case CPacketCtrlSKY::TYPE_PONG:
					  sprintf(tp->logMsg, "[%s:%d]::[I]RES[PONG]", PROCESS_NAME, tp->sockfd);
					  log(tp->logMsg, 0, 0);
                        break;
                case CPacketCtrlSKY::TYPE_DELIVER_ACK: {
                	    //CR1.SEND ACK tp->buff에서 tp->vtBuff(VECTOR)로 넣기
                	    
                	    if (packetSKY.getData_SndAck(tp->buff, tp->vtBuff) < 0)
                                throw new myException(0, "[W]CPacketCtrlSKY::getData_SndAck Error...");
                        
                        vector<string>::iterator itrData;
                        itrData = tp->vtBuff.begin();
                        sprintf(tp->logMsg, "[%s:%d]::[I]ACK[%s][%s][%s]", PROCESS_NAME, tp->sockfd, string(*itrData).c_str(), string(*(itrData + 1)).c_str(), string(*(itrData + 2)).c_str());
                        log(tp->logMsg, 0, 0);
                        //CR2. ACK 테이블에 데이타 입력
                        if (g_oracle.setSndAckData(tp->ctx, tp->vtBuff) < 0)
                                throw new myException(0, "CDatabaseORA::setSndAckData Error...");
                        
                        tp->vtBuff.clear();
                        break;
                }
                case CPacketCtrlSKY::TYPE_REPORT: {
                	    //CR3. REPORT tp->buff에서 tp->vtBuff(VECTOR)로 넣기
                	       	
                	   	int nRet = packetSKY.getData_Report(tp->buff, tp->vtBuff);
                    		
						vector<string>::iterator itrData;
						itrData = tp->vtBuff.begin();
						
						if(nRet == 1){
							sprintf(tp->logMsg, "[%s:%d]::[I]RPT[%s][%s][%s][%s][%s]", 
											PROCESS_NAME, tp->sockfd, string(*itrData).c_str(), 
											string(*(itrData + 1)).c_str(), string(*(itrData + 2)).c_str(), 
											string(*(itrData + 3)).c_str(), string(*(itrData + 4)).c_str());
							log(tp->logMsg, 0, 0);
							
							//telcoid = atoi(g_prop.getProperty("gw.telco"));
							
							//CR4. REPORT 테이블에 데이타 입력
							if (g_oracle.setReportData(tp->ctx, tp->vtBuff) < 0)
								throw new myException(0, "[W]CDatabaseORA::setReportData Error...");
							
							memset(tp->buff, 0x00, sizeof(tp->buff));
							msgSize = packetSKY.getMsg_REPORT_ACK(tp->buff, string(*itrData));
							if (sockInst.sendMessage(tp->buff, msgSize) < 0)
								throw new myException(0, "[W]getMsg_REPORT_ACK sendMessage Error...");
							
							sprintf(tp->logMsg, "[%s:%d]::[I]RPTACK[%s]", PROCESS_NAME, tp->sockfd, string(*itrData).c_str());
							log(tp->logMsg, 0, 0);
						}
						else if(nRet == 2) {
							sprintf(tp->logMsg, "[%s:%d]::[I]PING[%s]", PROCESS_NAME, tp->sockfd, string(*itrData).c_str());
							log(tp->logMsg, 0, 0);
							
							memset(tp->buff, 0x00, sizeof(tp->buff));
							msgSize = packetSKY.getMsg_PING_RES    (tp->buff, string(*itrData));
							if (sockInst.sendMessage(tp->buff, msgSize) < 0)
								throw new myException(0, "[W]getMsg_PING_RES sendMessage Error...");
						}
						else if(nRet == 3) {
							tp->vtBuff.clear();
							throw new myException(0, "[W]CPacketCtrlSKY::getData_Report unknown MsgType...");
						}
						else{
										tp->vtBuff.clear();
							throw new myException(0, "[W]CPacketCtrlSKY::getData_Report Error...");
						}
				
						tp->vtBuff.clear();
						break;
                }
                default:
                        break;
                }
        }
        catch (myException* excp) {
                mnt((char*)(excp->getErrMsg().c_str()), 0, 0);
                delete excp;
                return -1;
        }
        return 1;
}

void CloseProcess(int sig)
{
	cout << "CloseProcess Start & Exit [" << sig << "]" << endl;
	activeProcess = false;
}

void log(char *buf, int st, int err)
{
	if (ml_sub_send_log(buf, strlen(buf), 3, st, err) <= 0) {
		printf("%s ml_sub_send_log error. %d %s\n", PROCESS_NAME, errno, strerror(errno));
	}
}

void mnt(char *buf, int st, int err)
{
	if (ml_sub_send_moni(buf, strlen(buf), 3, st, err) <= 0) {
		printf("%s ml_sub_send_moni error. %d %s\n", PROCESS_NAME, errno, strerror(errno));
	}
}
