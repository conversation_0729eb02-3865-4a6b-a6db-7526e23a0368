       UID    PID   PPID   C      STIME TT                 TIME CMD
         0      1      0   0     7월13 ?              00:01:26 /usr/lib/systemd/systemd rhgb --switched-root --system --deserialize 31
         0      2      0   0     7월13 ?              00:00:01 [kthreadd]
         0      3      2   0     7월13 ?              00:00:00 [pool_workqueue_]
         0      4      2   0     7월13 ?              00:00:00 [kworker/R-rcu_g]
         0      5      2   0     7월13 ?              00:00:00 [kworker/R-sync_]
         0      6      2   0     7월13 ?              00:00:00 [kworker/R-slub_]
         0      7      2   0     7월13 ?              00:00:00 [kworker/R-netns]
         0     11      2   0     7월13 ?              00:00:00 [kworker/R-mm_pe]
         0     13      2   0     7월13 ?              00:00:00 [rcu_tasks_kthre]
         0     14      2   0     7월13 ?              00:00:00 [rcu_tasks_rude_]
         0     15      2   0     7월13 ?              00:00:00 [rcu_tasks_trace]
         0     16      2   0     7월13 ?              00:03:37 [ksoftirqd/0]
         0     17      2   0     7월13 ?              00:10:46 [rcu_preempt]
         0     18      2   0     7월13 ?              00:00:00 [rcu_exp_par_gp_]
         0     19      2   0     7월13 ?              00:00:02 [rcu_exp_gp_kthr]
         0     20      2   0     7월13 ?              00:00:01 [migration/0]
         0     21      2   0     7월13 ?              00:00:00 [idle_inject/0]
         0     23      2   0     7월13 ?              00:00:00 [cpuhp/0]
         0     24      2   0     7월13 ?              00:00:00 [cpuhp/1]
         0     25      2   0     7월13 ?              00:00:00 [idle_inject/1]
         0     26      2   0     7월13 ?              00:00:02 [migration/1]
         0     27      2   0     7월13 ?              00:03:39 [ksoftirqd/1]
         0     30      2   0     7월13 ?              00:00:00 [cpuhp/2]
         0     31      2   0     7월13 ?              00:00:00 [idle_inject/2]
         0     32      2   0     7월13 ?              00:00:02 [migration/2]
         0     33      2   0     7월13 ?              00:03:44 [ksoftirqd/2]
         0     36      2   0     7월13 ?              00:00:00 [cpuhp/3]
         0     37      2   0     7월13 ?              00:00:00 [idle_inject/3]
         0     38      2   0     7월13 ?              00:00:04 [migration/3]
         0     39      2   0     7월13 ?              00:04:04 [ksoftirqd/3]
         0     45      2   0     7월13 ?              00:00:00 [kdevtmpfs]
         0     46      2   0     7월13 ?              00:00:00 [kworker/R-inet_]
         0     47      2   0     7월13 ?              00:00:08 [kauditd]
         0     48      2   0     7월13 ?              00:00:03 [khungtaskd]
         0     49      2   0     7월13 ?              00:00:00 [oom_reaper]
         0     50      2   0     7월13 ?              00:00:00 [kworker/R-write]
         0     51      2   0     7월13 ?              00:03:57 [kcompactd0]
         0     52      2   0     7월13 ?              00:00:00 [ksmd]
         0     53      2   0     7월13 ?              00:01:40 [khugepaged]
         0     54      2   0     7월13 ?              00:00:00 [kworker/R-crypt]
         0     55      2   0     7월13 ?              00:00:00 [kworker/R-kinte]
         0     56      2   0     7월13 ?              00:00:00 [kworker/R-kbloc]
         0     57      2   0     7월13 ?              00:00:00 [irq/9-acpi]
         0     59      2   0     7월13 ?              00:00:00 [kworker/R-tpm_d]
         0     60      2   0     7월13 ?              00:00:00 [kworker/R-md]
         0     61      2   0     7월13 ?              00:00:00 [kworker/R-md_bi]
         0     62      2   0     7월13 ?              00:00:00 [kworker/R-edac-]
         0     63      2   0     7월13 ?              00:00:00 [watchdogd]
         0     66      2   0     7월13 ?              00:03:06 [kswapd0]
         0     67      2   0     7월13 ?              00:00:00 [kworker/R-kthro]
         0     71      2   0     7월13 ?              00:00:00 [irq/24-pciehp]
         0     72      2   0     7월13 ?              00:00:00 [irq/25-pciehp]
         0     73      2   0     7월13 ?              00:00:00 [irq/26-pciehp]
         0     74      2   0     7월13 ?              00:00:00 [irq/27-pciehp]
         0     75      2   0     7월13 ?              00:00:00 [irq/28-pciehp]
         0     76      2   0     7월13 ?              00:00:00 [irq/29-pciehp]
         0     77      2   0     7월13 ?              00:00:00 [irq/30-pciehp]
         0     78      2   0     7월13 ?              00:00:00 [irq/31-pciehp]
         0     79      2   0     7월13 ?              00:00:00 [irq/32-pciehp]
         0     80      2   0     7월13 ?              00:00:00 [irq/33-pciehp]
         0     81      2   0     7월13 ?              00:00:00 [irq/34-pciehp]
         0     82      2   0     7월13 ?              00:00:00 [irq/35-pciehp]
         0     83      2   0     7월13 ?              00:00:00 [irq/36-pciehp]
         0     84      2   0     7월13 ?              00:00:00 [irq/37-pciehp]
         0     85      2   0     7월13 ?              00:00:00 [irq/38-pciehp]
         0     86      2   0     7월13 ?              00:00:00 [irq/39-pciehp]
         0     87      2   0     7월13 ?              00:00:00 [irq/40-pciehp]
         0     88      2   0     7월13 ?              00:00:00 [irq/41-pciehp]
         0     89      2   0     7월13 ?              00:00:00 [irq/42-pciehp]
         0     90      2   0     7월13 ?              00:00:00 [irq/43-pciehp]
         0     91      2   0     7월13 ?              00:00:00 [irq/44-pciehp]
         0     92      2   0     7월13 ?              00:00:00 [irq/45-pciehp]
         0     93      2   0     7월13 ?              00:00:00 [irq/46-pciehp]
         0     94      2   0     7월13 ?              00:00:00 [irq/47-pciehp]
         0     95      2   0     7월13 ?              00:00:00 [irq/48-pciehp]
         0     96      2   0     7월13 ?              00:00:00 [irq/49-pciehp]
         0     97      2   0     7월13 ?              00:00:00 [irq/50-pciehp]
         0     98      2   0     7월13 ?              00:00:00 [irq/51-pciehp]
         0     99      2   0     7월13 ?              00:00:00 [irq/52-pciehp]
         0    100      2   0     7월13 ?              00:00:00 [irq/53-pciehp]
         0    101      2   0     7월13 ?              00:00:00 [irq/54-pciehp]
         0    102      2   0     7월13 ?              00:00:00 [irq/55-pciehp]
         0    104      2   0     7월13 ?              00:00:00 [kworker/R-acpi_]
         0    105      2   0     7월13 ?              00:00:00 [kworker/R-kmpat]
         0    106      2   0     7월13 ?              00:00:00 [kworker/R-kalua]
         0    108      2   0     7월13 ?              00:00:00 [kworker/R-mld]
         0    109      2   0     7월13 ?              00:00:00 [kworker/R-ipv6_]
         0    119      2   0     7월13 ?              00:00:00 [kworker/R-kstrp]
         0    453      2   0     7월13 ?              00:00:00 [kworker/R-ata_s]
         0    460      2   0     7월13 ?              00:00:00 [scsi_eh_0]
         0    462      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    464      2   0     7월13 ?              00:00:00 [scsi_eh_1]
         0    467      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    469      2   0     7월13 ?              00:00:00 [scsi_eh_2]
         0    470      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    471      2   0     7월13 ?              00:00:00 [scsi_eh_3]
         0    472      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    473      2   0     7월13 ?              00:00:00 [scsi_eh_4]
         0    474      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    475      2   0     7월13 ?              00:00:00 [scsi_eh_5]
         0    476      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    477      2   0     7월13 ?              00:00:00 [scsi_eh_6]
         0    478      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    479      2   0     7월13 ?              00:00:00 [scsi_eh_7]
         0    480      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    481      2   0     7월13 ?              00:00:00 [scsi_eh_8]
         0    482      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    483      2   0     7월13 ?              00:00:00 [scsi_eh_9]
         0    484      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    486      2   0     7월13 ?              00:00:00 [scsi_eh_10]
         0    487      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    488      2   0     7월13 ?              00:00:00 [scsi_eh_11]
         0    489      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    490      2   0     7월13 ?              00:00:00 [scsi_eh_12]
         0    491      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    492      2   0     7월13 ?              00:00:00 [scsi_eh_13]
         0    493      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    494      2   0     7월13 ?              00:00:00 [scsi_eh_14]
         0    495      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    497      2   0     7월13 ?              00:00:00 [scsi_eh_15]
         0    498      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    499      2   0     7월13 ?              00:00:00 [scsi_eh_16]
         0    500      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    501      2   0     7월13 ?              00:00:00 [scsi_eh_17]
         0    502      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    503      2   0     7월13 ?              00:00:00 [scsi_eh_18]
         0    504      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    505      2   0     7월13 ?              00:00:00 [scsi_eh_19]
         0    506      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    507      2   0     7월13 ?              00:00:00 [scsi_eh_20]
         0    508      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    509      2   0     7월13 ?              00:00:00 [scsi_eh_21]
         0    510      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    511      2   0     7월13 ?              00:00:00 [scsi_eh_22]
         0    512      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    513      2   0     7월13 ?              00:00:00 [scsi_eh_23]
         0    514      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    515      2   0     7월13 ?              00:00:00 [scsi_eh_24]
         0    516      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    517      2   0     7월13 ?              00:00:00 [scsi_eh_25]
         0    518      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    519      2   0     7월13 ?              00:00:00 [scsi_eh_26]
         0    520      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    521      2   0     7월13 ?              00:00:00 [scsi_eh_27]
         0    522      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    523      2   0     7월13 ?              00:00:00 [scsi_eh_28]
         0    524      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    525      2   0     7월13 ?              00:00:00 [scsi_eh_29]
         0    526      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    527      2   0     7월13 ?              00:00:00 [scsi_eh_30]
         0    528      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    529      2   0     7월13 ?              00:00:00 [scsi_eh_31]
         0    530      2   0     7월13 ?              00:00:00 [kworker/R-scsi_]
         0    542      2   0     7월13 ?              00:00:00 [irq/16-vmwgfx]
         0    549      2   0     7월13 ?              00:00:00 [kworker/R-ttm]
         0    643      2   0     7월13 ?              00:00:00 [kworker/R-kdmfl]
         0    650      2   0     7월13 ?              00:00:00 [kworker/R-kdmfl]
         0    668      2   0     7월13 ?              00:00:00 [kworker/R-xfsal]
         0    669      2   0     7월13 ?              00:00:00 [kworker/R-xfs_m]
         0    670      2   0     7월13 ?              00:00:00 [kworker/R-xfs-b]
         0    671      2   0     7월13 ?              00:00:00 [kworker/R-xfs-c]
         0    672      2   0     7월13 ?              00:00:00 [kworker/R-xfs-r]
         0    673      2   0     7월13 ?              00:00:00 [kworker/R-xfs-b]
         0    674      2   0     7월13 ?              00:00:00 [kworker/R-xfs-i]
         0    675      2   0     7월13 ?              00:00:00 [kworker/R-xfs-l]
         0    676      2   0     7월13 ?              00:00:00 [kworker/R-xfs-c]
         0    677      2   0     7월13 ?              00:02:43 [xfsaild/dm-0]
         0    762      1   0     7월13 ?              00:01:34 /usr/lib/systemd/systemd-journald
         0    775      1   0     7월13 ?              00:00:00 vmware-vmblock-fuse /run/vmblock-fuse -o rw,subtype=vmware-vmblock,default_permissions,allow_other,dev,suid
         0    781      1   0     7월13 ?              00:00:08 /usr/lib/systemd/systemd-udevd
         0    839      2   0     7월13 ?              00:00:00 [kworker/R-kdmfl]
         0    845      2   0     7월13 ?              00:01:08 [irq/57-vmw_vmci]
         0    846      2   0     7월13 ?              00:00:00 [irq/58-vmw_vmci]
         0    847      2   0     7월13 ?              00:00:01 [irq/59-vmw_vmci]
         0    883      2   0     7월13 ?              00:00:00 [kworker/R-xfs-b]
         0    884      2   0     7월13 ?              00:00:00 [kworker/R-xfs-b]
         0    885      2   0     7월13 ?              00:00:00 [kworker/R-xfs-c]
         0    886      2   0     7월13 ?              00:00:00 [kworker/R-xfs-r]
         0    887      2   0     7월13 ?              00:00:00 [kworker/R-xfs-c]
         0    888      2   0     7월13 ?              00:00:00 [kworker/R-xfs-b]
         0    889      2   0     7월13 ?              00:00:00 [kworker/R-xfs-r]
         0    890      2   0     7월13 ?              00:00:00 [kworker/R-xfs-b]
         0    891      2   0     7월13 ?              00:00:00 [kworker/R-xfs-i]
         0    892      2   0     7월13 ?              00:00:00 [kworker/R-xfs-i]
         0    893      2   0     7월13 ?              00:00:00 [kworker/R-xfs-l]
         0    894      2   0     7월13 ?              00:00:00 [kworker/R-xfs-c]
         0    895      2   0     7월13 ?              00:00:00 [kworker/R-xfs-l]
         0    896      2   0     7월13 ?              00:02:37 [xfsaild/dm-2]
         0    897      2   0     7월13 ?              00:00:00 [kworker/R-xfs-c]
         0    898      2   0     7월13 ?              00:00:00 [xfsaild/sda1]
        32    926      1   0     7월13 ?              00:00:00 /usr/bin/rpcbind -w -f
         0    927      1   0     7월13 ?              00:00:59 /sbin/auditd
         0    929    927   0     7월13 ?              00:00:11 /usr/sbin/sedispatch
        81    956      1   0     7월13 ?              00:00:00 /usr/bin/dbus-broker-launch --scope system --audit
        81    957    956   0     7월13 ?              00:01:08 dbus-broker --log 4 --controller 9 --machine-id 308606182478447e8f4b6749aaf4a58a --max-bytes 536870912 --max-fds 4096 --max-matches 131072 --audit
         0    958      1   0     7월13 ?              00:01:23 /usr/sbin/NetworkManager --no-daemon
        70    959      1   0     7월13 ?              00:00:45 avahi-daemon: running [linux.local]
         0    963      1   0     7월13 ?              00:00:43 /usr/sbin/irqbalance
       992    965      1   0     7월13 ?              00:00:01 /usr/bin/lsmd -d
       998    968      1   0     7월13 ?              00:00:56 /usr/lib/polkit-1/polkitd --no-debug
         0    969      1   0     7월13 ?              00:00:00 /usr/libexec/power-profiles-daemon
       172    970      1   0     7월13 ?              00:00:10 /usr/libexec/rtkit-daemon
         0    971      1   0     7월13 ?              00:00:07 /usr/libexec/accounts-daemon
         0    972      1   0     7월13 ?              00:00:00 /usr/libexec/switcheroo-control
         0    975      1   0     7월13 ?              00:00:10 /usr/lib/systemd/systemd-logind
         0    977      1   0     7월13 ?              00:00:01 /usr/libexec/udisks2/udisksd
         0    978      1   0     7월13 ?              00:00:00 /usr/libexec/upowerd
        70    982    959   0     7월13 ?              00:00:00 avahi-daemon: chroot helper
       988    989      1   0     7월13 ?              00:00:21 /usr/sbin/chronyd -F 2
         0   1022      1   0     7월13 ?              00:00:00 /usr/sbin/ModemManager
         0   1043      1   0     7월13 ?              00:00:00 /usr/sbin/cupsd -l
         0   1048      1   0     7월13 ?              00:00:00 sshd: /usr/sbin/sshd -D [listener] 0 of 10-100 startups
         0   1057      1   0     7월13 ?              00:13:12 /usr/bin/containerd
         0   1086      1   0     7월13 ?              00:00:00 /usr/sbin/gssproxy -D
         0   1090      1   0     7월13 ?              00:01:29 /usr/sbin/rsyslogd -n
         0   1099      1   0     7월13 ?              00:00:00 /usr/sbin/xrdp-sesman --nodaemon
       979   1112      1   0     7월13 ?              00:00:00 /usr/sbin/xrdp --nodaemon
         0   1198      2   0     7월13 ?              00:00:00 [kworker/R-rpcio]
         0   1199      2   0     7월13 ?              00:00:00 [kworker/R-xprti]
         0   1203      1   0     7월13 ?              00:00:00 /usr/sbin/atd -f
         0   1204      1   0     7월13 ?              00:00:03 /usr/sbin/crond -n
         0   1205      1   0     7월13 ?              00:00:00 /usr/sbin/gdm
         0   1352      1   0     7월13 ?              00:03:42 /usr/bin/dockerd -H fd:// --containerd=/run/containerd/containerd.sock
         0   2131      1   0     7월13 ?              00:00:03 /usr/sbin/wpa_supplicant -c /etc/wpa_supplicant/wpa_supplicant.conf -u -s
       984   2343      1   0     7월13 ?              00:00:00 /usr/libexec/colord
         0   2452   1205   0     7월13 ?              00:00:00 gdm-session-worker [pam/gdm-password]
      1000   2473      1   0     7월13 ?              00:00:28 /usr/lib/systemd/systemd --user
      1000   2475   2473   0     7월13 ?              00:00:00 (sd-pam)
      1000   2491      1   0     7월13 ?              00:00:03 /usr/bin/gnome-keyring-daemon --daemonize --login
      1000   2495   2452   0     7월13 tty2           00:00:00 /usr/libexec/gdm-x-session --register-session --run-script gnome-session
      1000   2498   2495   2     7월13 tty2           09:57:40 /usr/libexec/Xorg vt2 -displayfd 3 -auth /run/user/1000/gdm/Xauthority -nolisten tcp -background none -noreset -keeptty -novtswitch -verbose 3
      1000   2509   2473   0     7월13 ?              00:00:01 /usr/bin/dbus-broker-launch --scope user
      1000   2510   2509   0     7월13 ?              00:00:19 dbus-broker --log 4 --controller 10 --machine-id 308606182478447e8f4b6749aaf4a58a --max-bytes 100000000000000 --max-fds 25000000000000 --max-matches 5000000000
      1000   2512   2495   0     7월13 tty2           00:00:00 /usr/libexec/gnome-session-binary
      1000   2561   2473   0     7월13 ?              00:00:00 /usr/libexec/at-spi-bus-launcher
      1000   2566   2561   0     7월13 ?              00:00:00 /usr/bin/dbus-broker-launch --config-file=/usr/share/defaults/at-spi2/accessibility.conf --scope user
      1000   2567   2566   0     7월13 ?              00:00:07 dbus-broker --log 4 --controller 9 --machine-id 308606182478447e8f4b6749aaf4a58a --max-bytes 100000000000000 --max-fds 6400000 --max-matches 5000000000
      1000   2591   2473   0     7월13 ?              00:00:00 /usr/libexec/gnome-session-ctl --monitor
      1000   2593   2473   0     7월13 ?              00:00:00 /usr/libexec/gnome-session-binary --systemd-service --session=gnome
      1000   2610   2473   2     7월13 ?              10:25:04 /usr/bin/gnome-shell
      1000   2626   2473   0     7월13 ?              00:00:04 /usr/libexec/gvfsd
      1000   2631   2473   0     7월13 ?              00:00:00 /usr/libexec/gvfsd-fuse /run/user/1000/gvfs -f
      1000   2645   2610   0     7월13 ?              00:09:30 ibus-daemon --panel disable --xim
      1000   2649   2645   0     7월13 ?              00:00:00 /usr/libexec/ibus-dconf
      1000   2650   2645   0     7월13 ?              00:01:39 /usr/libexec/ibus-extension-gtk3
      1000   2652   2473   0     7월13 ?              00:02:35 /usr/libexec/ibus-x11 --kill-daemon
      1000   2655   2473   0     7월13 ?              00:00:02 /usr/libexec/ibus-portal
      1000   2660   2473   0     7월13 ?              00:00:00 /usr/libexec/xdg-permission-store
      1000   2675   2473   0     7월13 ?              00:00:38 /usr/libexec/at-spi2-registryd --use-gnome-session
      1000   2678   2473   0     7월13 ?              00:00:00 /usr/libexec/evolution-source-registry
      1000   2681   2473   0     7월13 ?              00:00:02 /usr/bin/pipewire
      1000   2685   2473   0     7월13 ?              00:00:00 /usr/bin/wireplumber
      1000   2686   2473   0     7월13 ?              00:00:04 /usr/bin/pipewire-pulse
      1000   2701   2473   0     7월13 ?              00:00:00 /usr/libexec/goa-daemon
      1000   2705   2473   0     7월13 ?              00:00:00 /usr/libexec/dconf-service
      1000   2713   2473   0     7월13 ?              00:00:00 /usr/libexec/evolution-calendar-factory
      1000   2731   2473   0     7월13 ?              00:01:16 /usr/libexec/goa-identity-service
      1000   2732   2473   0     7월13 ?              00:00:00 /usr/libexec/gvfs-udisks2-volume-monitor
      1000   2753   2473   0     7월13 ?              00:00:00 /usr/libexec/gvfs-mtp-volume-monitor
      1000   2757   2473   0     7월13 ?              00:00:00 /usr/libexec/evolution-addressbook-factory
         0   2760      1   0     7월13 ?              00:01:38 /usr/libexec/sssd/sssd_kcm --uid 0 --gid 0 --logger=files
      1000   2765   2473   0     7월13 ?              00:00:00 /usr/libexec/gvfs-goa-volume-monitor
      1000   2774   2473   0     7월13 ?              00:00:00 /usr/libexec/gvfs-gphoto2-volume-monitor
      1000   2788   2473   0     7월13 ?              00:00:00 /usr/bin/gjs /usr/share/gnome-shell/org.gnome.Shell.Notifications
      1000   2803   2473   0     7월13 ?              00:00:00 /usr/libexec/gsd-a11y-settings
      1000   2804   2473   0     7월13 ?              00:00:17 /usr/libexec/gsd-color
      1000   2805   2473   0     7월13 ?              00:00:00 /usr/libexec/gsd-datetime
      1000   2807   2473   0     7월13 ?              00:00:28 /usr/libexec/gsd-housekeeping
      1000   2809   2473   0     7월13 ?              00:00:07 /usr/libexec/gsd-keyboard
      1000   2810   2473   0     7월13 ?              00:00:09 /usr/libexec/gsd-media-keys
      1000   2811   2473   0     7월13 ?              00:00:11 /usr/libexec/gsd-power
      1000   2812   2473   0     7월13 ?              00:00:00 /usr/libexec/gsd-print-notifications
      1000   2815   2473   0     7월13 ?              00:00:00 /usr/libexec/gsd-rfkill
      1000   2818   2473   0     7월13 ?              00:00:00 /usr/libexec/gsd-screensaver-proxy
      1000   2820   2473   0     7월13 ?              00:00:00 /usr/libexec/gsd-sharing
      1000   2826   2473   0     7월13 ?              00:01:02 /usr/libexec/gsd-smartcard
      1000   2833   2473   0     7월13 ?              00:00:00 /usr/libexec/gsd-sound
      1000   2838   2473   0     7월13 ?              00:00:00 /usr/libexec/gsd-usb-protection
      1000   2841   2473   0     7월13 ?              00:00:08 /usr/libexec/gsd-wacom
      1000   2843   2593   0     7월13 ?              00:08:53 /usr/bin/gnome-software --gapplication-service
      1000   2847   2473   0     7월13 ?              00:00:34 /usr/libexec/gsd-xsettings
      1000   2871   2593   0     7월13 ?              00:00:00 /usr/libexec/gsd-disk-utility-notify
      1000   2874   2593   0     7월13 ?              00:00:12 /usr/libexec/evolution-data-server/evolution-alarm-notify
      1000   2876   2473   0     7월13 ?              00:26:32 /usr/bin/vmtoolsd -n vmusr --blockFd 3
      1000   2886   2593   0     7월13 ?              01:52:54 /home/<USER>/.local/share/JetBrains/Toolbox/bin/jetbrains-toolbox --minimize
      1000   2900   2593   0     7월13 ?              00:00:00 /usr/bin/python3 /usr/libexec/gnome-tweak-tool-lid-inhibitor
      1000   2952   2473   0     7월13 ?              00:00:00 /usr/bin/gjs /usr/share/gnome-shell/org.gnome.ScreenSaver
      1000   3000   2473   0     7월13 ?              00:00:00 /usr/libexec/gsd-printer
      1000   3041   2645   0     7월13 ?              00:02:06 /usr/libexec/ibus-engine-hangul --ibus
      1000   3066   2473   0     7월13 ?              00:00:15 /usr/libexec/xdg-desktop-portal
      1000   3090   2473   0     7월13 ?              00:00:01 /usr/libexec/xdg-document-portal
         0   3097   3090   0     7월13 ?              00:00:00 fusermount -o rw,nosuid,nodev,fsname=portal,auto_unmount,subtype=portal -- /run/user/1000/doc
      1000   3105   2473   0     7월13 ?              00:01:37 /usr/libexec/xdg-desktop-portal-gnome
      1000   3177   2473   0     7월13 ?              00:01:09 /usr/libexec/xdg-desktop-portal-gtk
      1000   3198   2473   0     7월13 ?              00:00:00 /usr/libexec/gvfsd-metadata
         0   3206      1   0     7월13 ?              00:00:37 /usr/libexec/fwupd/fwupd
         0  28974      1   0     7월13 ?              00:01:17 /bin/bash /usr/local/bin/vm-monitor.sh
      1000  34587   2473   0     7월13 ?              00:30:13 /usr/libexec/gnome-terminal-server
      1000  34623  34587   0     7월13 pts/3          00:00:03 zsh
      1000  39919   2626   0     7월13 ?              00:00:00 /usr/libexec/gvfsd-http --spawner :1.21 /org/gtk/gvfs/exec_spaw/0
         0  72768      2   0     7월13 ?              00:00:00 [kworker/R-tls-s]
      1000 118138   2473   0     7월13 ?              00:04:35 /usr/bin/nautilus --gapplication-service
      1000 118142   2626   0     7월13 ?              00:00:00 /usr/libexec/gvfsd-trash --spawner :1.21 /org/gtk/gvfs/exec_spaw/1
      1000 118251   2626   0     7월13 ?              00:00:00 /usr/libexec/gvfsd-network --spawner :1.21 /org/gtk/gvfs/exec_spaw/2
      1000 118583   2626   0     7월13 ?              00:00:17 /usr/libexec/gvfsd-dnssd --spawner :1.21 /org/gtk/gvfs/exec_spaw/4
      1000 134736   2473   0     7월19 ?              00:00:00 /usr/libexec/bluetooth/obexd
      1000 442998 1465022  0     7월26 ?              00:00:00 /usr/share/gitkraken/resources/app.asar.unpacked/node_modules/@axosoft/node-spawn-server/target/release/node-spawn-server
      1000 443000 1465022  0     7월26 ?              00:00:16 /usr/share/gitkraken/gitkraken /usr/share/gitkraken/resources/app.asar/src/sharedModules/cli/pty-host/pty-host.bundle.js
         0 542141      1   0     7월19 ?              00:00:00 /usr/bin/VGAuthService -s
         0 542142      1   0     7월19 ?              00:19:11 /usr/bin/vmtoolsd
      1000 550966   2610   0     7월19 ?              00:02:16 /usr/bin/python3 -s /usr/bin/terminator
      1000 550999 550966   0     7월19 pts/16         00:00:09 /usr/bin/zsh
         0 575727   1099   0     7월14 ?              00:00:00 xrdp-sesexec
      1000 575731 575727   0     7월14 ?              00:00:26 Xvnc :10 -auth .Xauthority -geometry 1920x1080 -depth 16 -rfbunixpath /run/xrdp/1000/xrdp_display_10 -rfbunixmode 432 -SecurityTypes None -bs -nolisten tcp -localhost -dpi 96
      1000 575735 575727   0     7월14 ?              00:00:03 xfce4-session
      1000 575736 575727   0     7월14 ?              00:00:00 /usr/sbin/xrdp-chansrv
      1000 575839 575735   0     7월14 ?              00:00:03 /usr/bin/ssh-agent /home/<USER>/.xsession
      1000 575862      1   0     7월14 ?              00:00:21 /bin/gpg-agent --sh --daemon
      1000 575863 575735   0     7월14 ?              00:00:17 xfwm4
      1000 575878 575735   0     7월14 ?              00:00:24 xfsettingsd
      1000 575881 575735   0     7월14 ?              00:00:57 xfce4-panel
      1000 575885 575735   0     7월14 ?              00:00:01 Thunar --daemon
      1000 575890 575735   0     7월14 ?              00:01:14 xfdesktop
      1000 575896 575735   0     7월14 ?              00:00:00 /usr/libexec/geoclue-2.0/demos/agent
      1000 575902 575881   0     7월14 ?              00:00:01 /usr/lib64/xfce4/panel/wrapper-2.0 /usr/lib64/xfce4/panel/plugins/libsystray.so 6 12582924 systray 상태 표시줄 플러그인 상태 알림 항목(프로그램 표시)과 구형 시스템 트레이 항목을 나타냅니다
      1000 575903 575735   0     7월14 ?              00:01:45 /usr/libexec/tracker-miner-fs-3
      1000 575906 575881   0     7월14 ?              00:00:02 /usr/lib64/xfce4/panel/wrapper-2.0 /usr/lib64/xfce4/panel/plugins/libpulseaudio-plugin.so 8 12582925 pulseaudio PulseAudio 플러그인 PulseAudio 사운드 시스템의 오디오 음량을 조절합니다
      1000 575909 575881   0     7월14 ?              00:00:01 /usr/lib64/xfce4/panel/wrapper-2.0 /usr/lib64/xfce4/panel/plugins/libxfce4powermanager.so 9 12582926 power-manager-plugin 전원 관리자 플러그인 장치 배터리 수준을 표시하고 디스플레이 밝기를 조절합니다
      1000 575911 575735   0     7월14 ?              00:00:00 /usr/libexec/xfce-polkit
      1000 575912 575735   0     7월14 ?              00:00:47 xfce4-screensaver
      1000 575922 575735   0     7월14 ?              00:00:00 nm-applet
      1000 575944 575735   0     7월14 ?              00:00:48 xfce4-power-manager
      1000 575955 575881   0     7월14 ?              00:00:01 /usr/lib64/xfce4/panel/wrapper-2.0 /usr/lib64/xfce4/panel/plugins/libactions.so 14 12582927 actions 동작 단추 로그아웃, 잠금 또는 다른 시스템 동작을 수행합니다
      1000 801694  34587   0     7월14 pts/6          00:00:07 zsh
         0 1206526  1048   0     7월28 ?              00:00:00 sshd: jk [priv]
      1000 1206530 1206526  0    7월28 ?              00:00:00 sshd: jk@pts/13
      1000 1206531 1206530  0    7월28 pts/13         00:00:00 -zsh
         0 1206873  1048   0     7월28 ?              00:00:00 sshd: jk [priv]
      1000 1206878 1206873  0    7월28 ?              00:00:00 sshd: jk@notty
      1000 1206886 1206878  0    7월28 ?              00:00:00 /usr/libexec/openssh/sftp-server
         0 1207282  1048   0     7월28 ?              00:00:00 sshd: jk [priv]
      1000 1207289 1207282  0    7월28 ?              00:00:00 sshd: jk@pts/14
      1000 1207301 1207289  0    7월28 pts/14         00:00:00 -zsh
         0 1207640  1048   0     7월28 ?              00:00:00 sshd: jk [priv]
      1000 1207645 1207640  0    7월28 ?              00:00:00 sshd: jk@notty
      1000 1207653 1207645  0    7월28 ?              00:00:00 /usr/libexec/openssh/sftp-server
         0 1210491  1048   0     7월28 ?              00:00:00 sshd: jk [priv]
         0 1210495  1048   0     7월28 ?              00:00:00 sshd: jk [priv]
      1000 1210497 1210491  0    7월28 ?              00:00:00 sshd: jk@pts/15
      1000 1210504 1210497  0    7월28 pts/15         00:00:00 -zsh
      1000 1210560 1210495  0    7월28 ?              00:00:00 sshd: jk@notty
      1000 1210609 1210560  0    7월28 ?              00:00:00 /usr/libexec/openssh/sftp-server
         0 1211056  1048   0     7월28 ?              00:00:00 sshd: jk [priv]
      1000 1211061 1211056  0    7월28 ?              00:00:00 sshd: jk@pts/17
      1000 1211069 1211061  0    7월28 pts/17         00:00:00 -zsh
         0 1211413  1048   0     7월28 ?              00:00:00 sshd: jk [priv]
      1000 1211418 1211413  0    7월28 ?              00:00:00 sshd: jk@notty
      1000 1211426 1211418  0    7월28 ?              00:00:00 /usr/libexec/openssh/sftp-server
      1000 1242068 550966  0     7월28 pts/19         00:00:10 zsh
      1000 1464845  2610   0     7월21 ?              00:53:56 /usr/share/gitkraken/gitkraken
      1000 1464849 1464845  0    7월21 ?              00:00:00 /usr/share/gitkraken/gitkraken --type=zygote --no-zygote-sandbox
      1000 1464850 1464845  0    7월21 ?              00:00:00 /usr/share/gitkraken/gitkraken --type=zygote
      1000 1464853 1464850  0    7월21 ?              00:00:00 /usr/share/gitkraken/gitkraken --type=zygote
      1000 1464919 1464849  0    7월21 ?              00:02:51 /usr/share/gitkraken/gitkraken --type=gpu-process --enable-crash-reporter=9dcda294-7094-41c6-9dd7-1b647ae707c2,no_channel --user-data-dir=/home/<USER>/.config/GitKraken --gpu-preferences=UAAAAAAAAAAgAAAEAAAAAAAAAAAAAAAAAABgAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAABAAAAAAAAAAEAAAAAAAAAAIAAAAAAAAAAgAAAAAAAAA --shared-files --field-trial-handle=3,i,15397929452161627622,11274715349547781877,262144 --disable-features=SpareRendererForSitePerProcess --variations-seed-version
      1000 1464942  2473   0     7월21 ?              00:00:00 /usr/share/gitkraken/chrome_crashpad_handler --monitor-self-annotation=ptype=crashpad-handler --no-rate-limit --database=/home/<USER>/.config/GitKraken/CrashReports --url=https://f.a.k/e --annotation=_productName=GitKraken --annotation=_version=11.2.1 --annotation=lsb-release=Rocky Linux 9.6 (Blue Onyx) --annotation=plat=Linux --annotation=prod=Electron --annotation=ver=34.5.2 --initial-client-fd=69 --shared-client-connection
      1000 1464951 1464845  0    7월21 ?              00:02:16 /usr/share/gitkraken/gitkraken --type=utility --utility-sub-type=network.mojom.NetworkService --lang=ko --service-sandbox-type=none --crashpad-handler-pid=1464942 --enable-crash-reporter=9dcda294-7094-41c6-9dd7-1b647ae707c2,no_channel --user-data-dir=/home/<USER>/.config/GitKraken --secure-schemes=sentry-ipc --bypasscsp-schemes=sentry-ipc --cors-schemes=sentry-ipc --fetch-schemes=sentry-ipc --shared-files=v8_context_snapshot_data:100 --field-trial-handle=3,i,15397929452161627622,11274715349547781877,262144 --disable-features=SpareRendererForSitePerProcess --variations-seed-version
      1000 1464973 1464845  0    7월21 ?              00:00:07 /usr/share/gitkraken/gitkraken --type=renderer --crashpad-handler-pid=1464942 --enable-crash-reporter=9dcda294-7094-41c6-9dd7-1b647ae707c2,no_channel --user-data-dir=/home/<USER>/.config/GitKraken --secure-schemes=sentry-ipc --bypasscsp-schemes=sentry-ipc --cors-schemes=sentry-ipc --fetch-schemes=sentry-ipc --app-path=/usr/share/gitkraken/resources/app.asar --no-sandbox --no-zygote --node-integration-in-worker --disable-gpu-compositing --lang=ko --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=5 --time-ticks-at-unix-epoch=-1752357014905132 --launch-time-ticks=628768031125 --shared-files=v8_context_snapshot_data:100 --field-trial-handle=3,i,15397929452161627622,11274715349547781877,262144 --disable-features=SpareRendererForSitePerProcess --variations-seed-version
      1000 1465022 1464845  1    7월21 ?              04:19:47 /usr/share/gitkraken/gitkraken --type=renderer --crashpad-handler-pid=1464942 --enable-crash-reporter=9dcda294-7094-41c6-9dd7-1b647ae707c2,no_channel --user-data-dir=/home/<USER>/.config/GitKraken --secure-schemes=sentry-ipc --bypasscsp-schemes=sentry-ipc --cors-schemes=sentry-ipc --fetch-schemes=sentry-ipc --app-path=/usr/share/gitkraken/resources/app.asar --no-sandbox --no-zygote --node-integration-in-worker --disable-gpu-compositing --lang=ko --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=6 --time-ticks-at-unix-epoch=-1752357014905132 --launch-time-ticks=628768621463 --shared-files=v8_context_snapshot_data:100 --field-trial-handle=3,i,15397929452161627622,11274715349547781877,262144 --disable-features=SpareRendererForSitePerProcess --variations-seed-version
      1000 1465066 1465022  0    7월21 ?              00:00:00 [node-spawn-serv] <defunct>
      1000 1465075 1465022  0    7월21 ?              00:00:24 [gitkraken] <defunct>
      1000 1566455 550966  0     7월28 pts/20         00:00:05 /usr/bin/zsh
         0 1682756     2   0     7월28 ?              00:00:00 [kworker/u17:1]
      1000 1998284 34587   0     7월29 pts/5          00:00:01 zsh
      1000 2020305  2473   0     7월29 pts/20         00:00:00 248_KSKYB_MON____FTK
      1000 2020316  2473   0     7월29 pts/20         00:00:00 248_KSKYB_LOG____FTK /data/log_ftk/
      1000 2093043 2587969  7    7월29 pts/0          01:42:43 htop
      1000 2141063  2473   0     7월22 ?              00:00:00 /usr/libexec/flatpak-session-helper
      1000 2141067  2473   0     7월22 ?              00:00:00 server --sh -n /run/user/1000/.flatpak-helper/pkcs11-flatpak-2141063 --provider p11-kit-trust.so pkcs11:model=p11-kit-trust?write-protected=yes
         0 2155968     2   0     7월29 ?              00:00:00 [kworker/3:2H-kblockd]
         0 2177354     2   0     7월29 ?              00:00:00 [kworker/R-ib-co]
         0 2177355     2   0     7월29 ?              00:00:00 [kworker/R-ib-co]
         0 2177356     2   0     7월29 ?              00:00:00 [kworker/R-ib_mc]
         0 2177357     2   0     7월29 ?              00:00:00 [kworker/R-ib_nl]
      1001 2179418  2473   0     7월29 ?              00:00:10 db_pmon_FREE
      1001 2179422  2473   0     7월29 ?              00:00:01 db_clmn_FREE
      1001 2179426  2473   0     7월29 ?              00:00:17 db_psp0_FREE
      1001 2179430  2473   0     7월29 ?              00:00:20 db_vktm_FREE
      1001 2179436  2473   0     7월29 ?              00:00:05 db_gen0_FREE
      1001 2179442  2473   0     7월29 ?              00:00:03 db_mman_FREE
      1001 2179448  2473   0     7월29 ?              00:00:02 db_gen2_FREE
      1001 2179450  2473   0     7월29 ?              00:00:03 db_diag_FREE
      1001 2179454  2473   0     7월29 ?              00:00:02 db_ofsd_FREE
      1001 2179456  2473   0     7월29 ?              00:00:04 db_gwpd_FREE
      1001 2179458  2473   0     7월29 ?              00:00:17 db_dbrm_FREE
      1001 2179460  2473   0     7월29 ?              00:02:32 db_vkrm_FREE
      1001 2179462  2473   0     7월29 ?              00:00:08 db_pman_FREE
      1001 2179468  2473   0     7월29 ?              00:01:00 db_dia0_FREE
      1001 2179470  2473   0     7월29 ?              00:00:12 db_dbw0_FREE
      1001 2179474  2473   0     7월29 ?              00:00:28 db_lgwr_FREE
      1001 2179476  2473   0     7월29 ?              00:00:25 db_ckpt_FREE
      1001 2179478  2473   0     7월29 ?              00:00:01 db_smon_FREE
      1001 2179484  2473   0     7월29 ?              00:00:05 db_smco_FREE
      1001 2179487  2473   0     7월29 ?              00:00:00 db_reco_FREE
      1001 2179491  2473   0     7월29 ?              00:00:03 db_lreg_FREE
      1001 2179495  2473   0     7월29 ?              00:00:03 db_pxmn_FREE
      1001 2179500  2473   0     7월29 ?              00:00:25 db_mmon_FREE
      1001 2179504  2473   0     7월29 ?              00:00:21 db_mmnl_FREE
      1001 2179508  2473   0     7월29 ?              00:01:05 db_bg00_FREE
      1001 2179520  2473   0     7월29 ?              00:00:24 db_bg01_FREE
      1001 2179528  2473   0     7월29 ?              00:00:17 db_bg02_FREE
      1001 2179538  2473   0     7월29 ?              00:00:01 db_d000_FREE
      1001 2179540  2473   0     7월29 ?              00:00:00 db_s000_FREE
      1001 2179542  2473   0     7월29 ?              00:00:01 db_tmon_FREE
      1001 2179544  2473   0     7월29 ?              00:00:02 db_rcbg_FREE
      1001 2179547  2473   0     7월29 ?              00:00:01 db_tt00_FREE
      1001 2179549  2473   0     7월29 ?              00:00:02 db_tt01_FREE
      1001 2179562  2473   0     7월29 ?              00:00:02 db_p000_FREE
      1001 2179571  2473   0     7월29 ?              00:06:15 db_cjq0_FREE
      1001 2179795  2473   0     7월29 ?              00:00:00 db_aqpc_FREE
      1001 2179828  2473   0     7월29 ?              00:01:26 db_m001_FREE
      1001 2179832  2473   0     7월29 ?              00:01:20 db_m002_FREE
      1001 2179834  2473   0     7월29 ?              00:01:25 db_m003_FREE
      1001 2179836  2473   0     7월29 ?              00:01:28 db_m004_FREE
      1001 2179840  2473   0     7월29 ?              00:01:20 db_m005_FREE
      1001 2179877  2473   0     7월29 ?              00:00:01 db_qm02_FREE
      1001 2179883  2473   0     7월29 ?              00:00:01 db_q003_FREE
      1000 2254684  2473   0     7월29 ?              00:04:36 /opt/vivaldi/vivaldi-bin https://ai.google.dev/pricing
      1000 2254691 2254684  0    7월29 ?              00:00:00 cat
      1000 2254692 2254684  0    7월29 ?              00:00:00 cat
      1000 2254702  2473   0     7월29 ?              00:00:00 /opt/vivaldi/chrome_crashpad_handler --monitor-self --monitor-self-annotation=ptype=crashpad-handler --database=/home/<USER>/.config/vivaldi/Crash Reports --url=https://crash.vivaldi.com/submit --annotation=channel=stable --annotation=lsb-release=Rocky Linux 9.6 (Blue Onyx) --annotation=plat=Linux --annotation=prod=Chrome_Linux --annotation=ver=7.5.3735.54 --initial-client-fd=5 --shared-client-connection
      1000 2254704  2473   0     7월29 ?              00:00:00 /opt/vivaldi/chrome_crashpad_handler --no-periodic-tasks --no-rate-limit --monitor-self-annotation=ptype=crashpad-handler --database=/home/<USER>/.config/vivaldi/Crash Reports --url=https://crash.vivaldi.com/submit --annotation=channel=stable --annotation=lsb-release=Rocky Linux 9.6 (Blue Onyx) --annotation=plat=Linux --annotation=prod=Chrome_Linux --annotation=ver=7.5.3735.54 --initial-client-fd=4 --shared-client-connection
      1000 2254710 2254684  0    7월29 ?              00:00:00 /opt/vivaldi/vivaldi-bin --type=zygote --no-zygote-sandbox --crashpad-handler-pid=2254702 --enable-crash-reporter=,stable --change-stack-guard-on-fork=enable
      1000 2254711 2254684  0    7월29 ?              00:00:00 /opt/vivaldi/vivaldi-bin --type=zygote --crashpad-handler-pid=2254702 --enable-crash-reporter=,stable --change-stack-guard-on-fork=enable
      1000 2254713 2254711  0    7월29 ?              00:00:02 /opt/vivaldi/vivaldi-bin --type=zygote --crashpad-handler-pid=2254702 --enable-crash-reporter=,stable --change-stack-guard-on-fork=enable
      1000 2254737 2254710  0    7월29 ?              00:00:26 /opt/vivaldi/vivaldi-bin --type=gpu-process --crashpad-handler-pid=2254702 --enable-crash-reporter=,stable --change-stack-guard-on-fork=enable --gpu-preferences=UAAAAAAAAAAgAAAEAAAAAAAAAAAAAAAAAABgAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAABAAAAAAAAAAEAAAAAAAAAAIAAAAAAAAAAgAAAAAAAAA --running-vivaldi --shared-files --field-trial-handle=3,i,1262746119410881846,1209867511062030097,262144 --variations-seed-version
      1000 2254743 2254684  0    7월29 ?              00:00:59 /opt/vivaldi/vivaldi-bin --type=utility --utility-sub-type=network.mojom.NetworkService --lang=ko --running-vivaldi --service-sandbox-type=none --crashpad-handler-pid=2254702 --enable-crash-reporter=,stable --change-stack-guard-on-fork=enable --shared-files=v8_context_snapshot_data:100 --field-trial-handle=3,i,1262746119410881846,1209867511062030097,262144 --variations-seed-version
      1000 2254764 2254713  0    7월29 ?              00:00:05 /opt/vivaldi/vivaldi-bin --type=utility --utility-sub-type=storage.mojom.StorageService --lang=ko --running-vivaldi --service-sandbox-type=utility --crashpad-handler-pid=2254702 --enable-crash-reporter=,stable --change-stack-guard-on-fork=enable --shared-files=v8_context_snapshot_data:100 --field-trial-handle=3,i,1262746119410881846,1209867511062030097,262144 --variations-seed-version
      1000 2254775 2254713  0    7월29 ?              00:01:09 /opt/vivaldi/vivaldi-bin --type=renderer --crashpad-handler-pid=2254702 --enable-crash-reporter=,stable --extension-process --change-stack-guard-on-fork=enable --lang=ko --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=5 --time-ticks-at-unix-epoch=-1752395935470365 --running-vivaldi --launch-time-ticks=1377580012148 --shared-files=v8_context_snapshot_data:100 --field-trial-handle=3,i,1262746119410881846,1209867511062030097,262144 --variations-seed-version
      1000 2254786 2254713  0    7월29 ?              00:00:31 /opt/vivaldi/vivaldi-bin --type=renderer --crashpad-handler-pid=2254702 --enable-crash-reporter=,stable --extension-process --change-stack-guard-on-fork=enable --lang=ko --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=8 --time-ticks-at-unix-epoch=-1752395935470365 --running-vivaldi --launch-time-ticks=1377580142278 --shared-files=v8_context_snapshot_data:100 --field-trial-handle=3,i,1262746119410881846,1209867511062030097,262144 --variations-seed-version
      1000 2254818 2254713  0    7월29 ?              00:00:07 /opt/vivaldi/vivaldi-bin --type=renderer --crashpad-handler-pid=2254702 --enable-crash-reporter=,stable --extension-process --change-stack-guard-on-fork=enable --lang=ko --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=11 --time-ticks-at-unix-epoch=-1752395935470365 --running-vivaldi --launch-time-ticks=1377580196302 --shared-files=v8_context_snapshot_data:100 --field-trial-handle=3,i,1262746119410881846,1209867511062030097,262144 --variations-seed-version
      1000 2254843 2254713  0    7월29 ?              00:01:35 /opt/vivaldi/vivaldi-bin --type=renderer --crashpad-handler-pid=2254702 --enable-crash-reporter=,stable --change-stack-guard-on-fork=enable --lang=ko --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=13 --time-ticks-at-unix-epoch=-1752395935470365 --running-vivaldi --launch-time-ticks=1377580612712 --shared-files=v8_context_snapshot_data:100 --field-trial-handle=3,i,1262746119410881846,1209867511062030097,262144 --variations-seed-version
      1000 2255046 2254713  0    7월29 ?              00:00:00 /opt/vivaldi/vivaldi-bin --type=renderer --crashpad-handler-pid=2254702 --enable-crash-reporter=,stable --change-stack-guard-on-fork=enable --disable-gpu-compositing --lang=ko --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=41 --time-ticks-at-unix-epoch=-1752395935470365 --running-vivaldi --launch-time-ticks=1377582158150 --shared-files=v8_context_snapshot_data:100 --field-trial-handle=3,i,1262746119410881846,1209867511062030097,262144 --variations-seed-version
      1001 2255180  2473   0     7월29 ?              00:01:10 db_m000_FREE
      1000 2260827 550966  0     7월29 pts/18         00:00:00 /usr/bin/zsh
      1000 2276504  2610   0     7월29 ?              00:02:50 /usr/share/code/code
      1000 2276508 2276504  0    7월29 ?              00:00:00 /usr/share/code/code --type=zygote --no-zygote-sandbox
      1000 2276509 2276504  0    7월29 ?              00:00:00 /usr/share/code/code --type=zygote
      1000 2276511 2276509  0    7월29 ?              00:00:00 /usr/share/code/code --type=zygote
      1000 2276530  2473   0     7월29 ?              00:00:00 /usr/share/code/chrome_crashpad_handler --monitor-self-annotation=ptype=crashpad-handler --no-rate-limit --database=/home/<USER>/.config/Code/Crashpad --url=appcenter://code?aid=fba07a4d-84bd-4fc8-a125-9640fc8ce171&uid=b6c6adb7-1389-4d48-a93f-05e91b6ef6bc&iid=b6c6adb7-1389-4d48-a93f-05e91b6ef6bc&sid=b6c6adb7-1389-4d48-a93f-05e91b6ef6bc --annotation=_companyName=Microsoft --annotation=_productName=VSCode --annotation=_version=1.102.2 --annotation=lsb-release=Rocky Linux 9.6 (Blue Onyx) --annotation=plat=Linux --annotation=prod=Electron --annotation=ver=35.6.0 --initial-client-fd=46 --shared-client-connection
      1000 2276548 2276508  0    7월29 ?              00:08:15 /usr/share/code/code --type=zygote --no-zygote-sandbox
      1000 2276553 2276504  0    7월29 ?              00:00:12 /proc/self/exe --type=utility --utility-sub-type=network.mojom.NetworkService --lang=ko --service-sandbox-type=none --crashpad-handler-pid=2276530 --enable-crash-reporter=5c16ce45-4e38-4560-8c0b-319d2a5593f5,no_channel --user-data-dir=/home/<USER>/.config/Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --shared-files=v8_context_snapshot_data:100 --field-trial-handle=3,i,17498396749260447394,15283404168183613496,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync --disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess --variations-seed-version
      1000 2276576 2276511  0    7월29 ?              00:05:48 /usr/share/code/code --type=zygote
      1000 2276810 2276504  0    7월29 ?              00:03:01 /proc/self/exe --type=utility --utility-sub-type=node.mojom.NodeService --lang=ko --service-sandbox-type=none --crashpad-handler-pid=2276530 --enable-crash-reporter=5c16ce45-4e38-4560-8c0b-319d2a5593f5,no_channel --user-data-dir=/home/<USER>/.config/Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --shared-files=v8_context_snapshot_data:100 --field-trial-handle=3,i,17498396749260447394,15283404168183613496,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync --disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess --variations-seed-version
      1000 2276847 2276504  0    7월29 ?              00:00:35 /proc/self/exe --type=utility --utility-sub-type=node.mojom.NodeService --lang=ko --service-sandbox-type=none --crashpad-handler-pid=2276530 --enable-crash-reporter=5c16ce45-4e38-4560-8c0b-319d2a5593f5,no_channel --user-data-dir=/home/<USER>/.config/Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --shared-files=v8_context_snapshot_data:100 --field-trial-handle=3,i,17498396749260447394,15283404168183613496,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync --disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess --variations-seed-version
      1000 2276848 2276504  0    7월29 ?              00:00:18 /proc/self/exe --type=utility --utility-sub-type=node.mojom.NodeService --lang=ko --service-sandbox-type=none --crashpad-handler-pid=2276530 --enable-crash-reporter=5c16ce45-4e38-4560-8c0b-319d2a5593f5,no_channel --user-data-dir=/home/<USER>/.config/Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --shared-files=v8_context_snapshot_data:100 --field-trial-handle=3,i,17498396749260447394,15283404168183613496,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync --disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess --variations-seed-version
      1000 2276908 2276810  0    7월29 pts/7          00:00:00 /usr/bin/zsh -i
      1000 2276931 2276810  0    7월29 pts/12         00:00:00 /usr/bin/zsh -i
      1000 2277312 2276504  0    7월29 ?              00:00:05 /proc/self/exe --type=utility --utility-sub-type=audio.mojom.AudioService --lang=ko --service-sandbox-type=none --crashpad-handler-pid=2276530 --enable-crash-reporter=5c16ce45-4e38-4560-8c0b-319d2a5593f5,no_channel --user-data-dir=/home/<USER>/.config/Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --shared-files=v8_context_snapshot_data:100 --field-trial-handle=3,i,17498396749260447394,15283404168183613496,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync --disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess --variations-seed-version
      1000 2281222 2276810  0    7월29 pts/23         00:00:01 /usr/bin/zsh -i
      1001 2301322  2473   0     7월29 ?              00:00:01 /opt/oracle/product/23ai/dbhomeFree/bin/tnslsnr LISTENER -inherit
      1000 2304038 2276810  0    7월29 pts/1          00:00:00 /usr/bin/zsh -i
         0 2305169 2304038  0    7월29 pts/1          00:00:00 su - oracle
      1001 2305187 2305169  0    7월29 pts/1          00:00:00 -bash
      1000 2305784 2276810  0    7월29 pts/2          00:00:00 /usr/bin/zsh -i
      1001 2307258  2473   3     7월29 ?              00:33:03 db_mz00_FREE
      1001 2312609  2473   0     7월29 ?              00:00:18 oracleFREE (LOCAL=NO)
         0 2344339     2   0     7월29 ?              00:00:00 [kworker/2:1H-kblockd]
      1000 2383301 2305784  0    7월29 pts/2          00:00:01 sqlplus                      as sysdba @alter_user.sql
      1001 2383303  2473   0     7월29 ?              00:00:00 oracleFREE (LOCAL=NO)
      1000 2383640 2276810  0    7월29 pts/4          00:00:00 /usr/bin/zsh -i
      1000 2386217 2383640  0    7월29 pts/4          00:00:00 sqlplus                      @test_procedure.sql
      1001 2386219  2473   0     7월29 ?              00:00:00 oracleFREE (LOCAL=NO)
      1000 2386626 2276810  0    7월29 pts/8          00:00:00 /usr/bin/zsh -i
      1000 2387020 2386626  0    7월29 pts/8          00:00:00 sqlplus                      @find_procedure.sql
      1001 2387022  2473   0     7월29 ?              00:00:00 oracleFREE (LOCAL=NO)
      1000 2387440 2276810  0    7월29 pts/21         00:00:00 /usr/bin/zsh -i
      1000 2387833 2387440  0    7월29 pts/21         00:00:00 sqlplus                      @test_procedure.sql
      1001 2387835  2473   0     7월29 ?              00:00:00 oracleFREE (LOCAL=NO)
      1000 2388108 2276810  0    7월29 pts/25         00:00:00 /usr/bin/zsh -i
      1000 2439262 2276504  2     00:24 ?              00:17:43 /proc/self/exe --type=utility --utility-sub-type=node.mojom.NodeService --lang=ko --service-sandbox-type=none --dns-result-order=ipv4first --experimental-network-inspection --inspect-port=0 --crashpad-handler-pid=2276530 --enable-crash-reporter=5c16ce45-4e38-4560-8c0b-319d2a5593f5,no_channel --user-data-dir=/home/<USER>/.config/Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --shared-files=v8_context_snapshot_data:100 --field-trial-handle=3,i,17498396749260447394,15283404168183613496,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync --disable-features=CalculateNativeWinOcclusion,SpareRendererForSitePerProcess --variations-seed-version
      1000 2439288 2439262  0     00:24 ?              00:00:02 /usr/share/code/code /usr/share/code/resources/app/extensions/json-language-features/server/dist/node/jsonServerMain --node-ipc --clientProcessId=2439262
      1000 2439309 2276511  1     00:24 ?              00:13:31 /usr/share/code/code --type=zygote
      1000 2439342 2439262  0     00:24 ?              00:00:00 npm exec @jetbrains/mcp-proxy
      1000 2439353 2276511  0     00:24 ?              00:00:34 /usr/share/code/code --type=zygote
      1000 2439380 2439342  0     00:24 ?              00:00:19 node /home/<USER>/.npm/_npx/216f9f7f9928524d/node_modules/.bin/mcp-jetbrains-proxy
         0 2449676     2   0      00:51 ?              00:00:00 [kworker/1:1H-kblockd]
         0 2477160     2   0      01:50 ?              00:00:00 [kworker/0:2H-kblockd]
      1000 2477312  2473  11      01:51 ?              01:18:49 /home/<USER>/.cache/JetBrains/RemoteDev/dist/dd14f6f94bd7f_CLion-2025.1.3/bin/clion
      1000 2477408 2477312  2     01:51 ?              00:14:04 /home/<USER>/.cache/JetBrains/RemoteDev/dist/dd14f6f94bd7f_CLion-2025.1.3/plugins/clion-radler/DotFiles/linux-x64/Rider.Backend --runtimeconfig /home/<USER>/.cache/JetBrains/RemoteDev/dist/dd14f6f94bd7f_CLion-2025.1.3/plugins/clion-radler/DotFiles/Rider.Backend.netcore.runtimeconfig.json --Port=15947
      1000 2477430 2477312  0     01:51 ?              00:00:02 /home/<USER>/.cache/JetBrains/RemoteDev/dist/dd14f6f94bd7f_CLion-2025.1.3/bin/fsnotifier
      1000 2477633 2477312  0     01:51 ?              00:00:02 /home/<USER>/.cache/JetBrains/RemoteDev/dist/dd14f6f94bd7f_CLion-2025.1.3/bin/clang/linux/x64/bin/clangd --clion-mode=clion-main -update-debounce=0 -index=false -include-ineligible-results -clang-tidy=0 -resource-dir=/home/<USER>/.cache/JetBrains/RemoteDev/dist/dd14f6f94bd7f_CLion-2025.1.3/bin/clang/linux/x64 -keep-asts=30 -ranking-model=heuristics -clion-extra-completion-preamble -clion-keep-obsolete-ast=false -header-extensions=h;h;cuh;
      1000 2477657 2477312  0     01:51 ?              00:00:19 /home/<USER>/.augmentcode/intellij/global/sidecar/node/v22.14.0/node /tmp/augment-sidecar2630781534586657825/index.js --log-file /home/<USER>/.cache/JetBrains/CLion2025.1/log/augment-sidecar.log --stdio
      1000 2477658 2477312  1     01:51 ?              00:07:12 /home/<USER>/.cache/JetBrains/RemoteDev/dist/dd14f6f94bd7f_CLion-2025.1.3/jbr/lib/cef_server --pipe=/tmp/cef_server_pipe_2477312_01_16_53_147 --logfile=/home/<USER>/.cache/JetBrains/CLion2025.1/log/jcef_2477312.log --loglevel=100 --params=/tmp/cef_server_params.txt
      1000 2477668 2477658  0     01:51 ?              00:00:00 /home/<USER>/.cache/JetBrains/RemoteDev/dist/dd14f6f94bd7f_CLion-2025.1.3/jbr/lib/cef_server --type=zygote --no-zygote-sandbox --no-sandbox --force-device-scale-factor=1.0 --log-severity=disable --lang=en-US --user-data-dir=/home/<USER>/.cache/JetBrains/CLion2025.1/jcef_cache --log-file=/home/<USER>/.cache/JetBrains/CLion2025.1/log/jcef_2477312.log
      1000 2477669 2477658  0     01:51 ?              00:00:00 /home/<USER>/.cache/JetBrains/RemoteDev/dist/dd14f6f94bd7f_CLion-2025.1.3/jbr/lib/cef_server --type=zygote --no-sandbox --force-device-scale-factor=1.0 --log-severity=disable --lang=en-US --user-data-dir=/home/<USER>/.cache/JetBrains/CLion2025.1/jcef_cache --log-file=/home/<USER>/.cache/JetBrains/CLion2025.1/log/jcef_2477312.log
      1000 2477692 2477668  4     01:51 ?              00:28:23 /home/<USER>/.cache/JetBrains/RemoteDev/dist/dd14f6f94bd7f_CLion-2025.1.3/jbr/lib/cef_server --type=gpu-process --no-sandbox --log-severity=disable --lang=en-US --user-data-dir=/home/<USER>/.cache/JetBrains/CLion2025.1/jcef_cache --gpu-preferences=WAAAAAAAAAAgAAAEAAAAAAAAAAAAAAAAAABgAAAAAAA4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAGAAAAAAAAAAYAAAAAAAAAAgAAAAAAAAACAAAAAAAAAAIAAAAAAAAAA== --log-file=/home/<USER>/.cache/JetBrains/CLion2025.1/log/jcef_2477312.log --shared-files --field-trial-handle=0,i,3807120877446238674,5375143654226584839,262144 --disable-features=SpareRendererForSitePerProcess --variations-seed-version
      1000 2477708 2477658  0     01:51 ?              00:00:00 /home/<USER>/.cache/JetBrains/RemoteDev/dist/dd14f6f94bd7f_CLion-2025.1.3/jbr/lib/cef_server --type=utility --utility-sub-type=network.mojom.NetworkService --lang=ko --service-sandbox-type=none --no-sandbox --log-severity=disable --lang=en-US --user-data-dir=/home/<USER>/.cache/JetBrains/CLion2025.1/jcef_cache --log-file=/home/<USER>/.cache/JetBrains/CLion2025.1/log/jcef_2477312.log --shared-files=v8_context_snapshot_data:100 --field-trial-handle=0,i,3807120877446238674,5375143654226584839,262144 --disable-features=SpareRendererForSitePerProcess --variations-seed-version
      1000 2477727 2477669  0     01:51 ?              00:00:00 /home/<USER>/.cache/JetBrains/RemoteDev/dist/dd14f6f94bd7f_CLion-2025.1.3/jbr/lib/cef_server --type=utility --utility-sub-type=storage.mojom.StorageService --lang=ko --service-sandbox-type=utility --no-sandbox --log-severity=disable --lang=en-US --user-data-dir=/home/<USER>/.cache/JetBrains/CLion2025.1/jcef_cache --log-file=/home/<USER>/.cache/JetBrains/CLion2025.1/log/jcef_2477312.log --shared-files=v8_context_snapshot_data:100 --field-trial-handle=0,i,3807120877446238674,5375143654226584839,262144 --disable-features=SpareRendererForSitePerProcess --variations-seed-version
      1000 2477762 2477669 16     01:51 ?              01:46:29 /home/<USER>/.cache/JetBrains/RemoteDev/dist/dd14f6f94bd7f_CLion-2025.1.3/jbr/lib/cef_server --type=renderer --log-severity=disable --user-data-dir=/home/<USER>/.cache/JetBrains/CLion2025.1/jcef_cache --no-sandbox --autoplay-policy=no-user-gesture-required --force-device-scale-factor=1.0 --log-file=/home/<USER>/.cache/JetBrains/CLion2025.1/log/jcef_2477312.log --disable-gpu-compositing --lang=ko --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=5 --time-ticks-at-unix-epoch=-1752397531572855 --launch-time-ticks=1408282420326 --shared-files=v8_context_snapshot_data:100 --field-trial-handle=0,i,3807120877446238674,5375143654226584839,262144 --disable-features=SpareRendererForSitePerProcess --variations-seed-version
      1000 2478232 2477312  0     01:51 ?              00:00:05 /home/<USER>/.cache/JetBrains/CLion2025.1/semantic-search/server/2.4.166/embeddings-server --model-path /home/<USER>/.cache/JetBrains/CLion2025.1/semantic-search/models/0.0.5/small/dan_100k_optimized.onnx --vocab-path /home/<USER>/.cache/JetBrains/CLion2025.1/semantic-search/models/0.0.5/small/bert-base-uncased.txt --storage-root /home/<USER>/.cache/JetBrains/CLion2025.1/semantic-search/indices/0.1.0 --vector-length 128 --index-size-limit 1000000 --batch-size 32 --models-pool-size 1 --max-sequence-length 64 --quantization i8 --metric-kind cos --n-threads 2 --working-threads-limit 4 --batch-insertion-threads 4 --port 0
      1000 2480816  2473   0     7월22 ?              00:00:00 /usr/libexec/gnome-shell-calendar-server
      1000 2481913  2473   0     7월22 ?              00:00:01 /usr/bin/gedit --gapplication-service
      1000 2496241 2477657  0     02:28 ?              00:00:00 npm exec @modelcontextprotocol/server-filesystem /home/<USER>
      1000 2496242 2477657  0     02:28 ?              00:00:00 npm exec @playwright/mcp@latest
      1000 2496248 2477657  0     02:28 ?              00:00:00 npm exec @modelcontextprotocol/server-sequential-thinking
      1000 2496254 2477657  0     02:28 ?              00:00:02 npm exec @upstash/context7-mcp@latest
      1000 2496293 2496248  0     02:28 ?              00:00:00 node /home/<USER>/.npm/_npx/de2bd410102f5eda/node_modules/.bin/mcp-server-sequential-thinking
      1000 2496304 2496242  0     02:28 ?              00:00:00 node /home/<USER>/.npm/_npx/9833c18b2d85bc59/node_modules/.bin/mcp-server-playwright
      1000 2496305 2496241  0     02:28 ?              00:00:00 node /home/<USER>/.npm/_npx/a3241bba59c344f5/node_modules/.bin/mcp-server-filesystem /home/<USER>
      1000 2496342 2496254  0     02:28 ?              00:00:00 node /home/<USER>/.npm/_npx/c35ab75beed40a3c/node_modules/.bin/context7-mcp
      1001 2508274  2473   0      03:05 ?              00:00:00 db_q001_FREE
      1000 2523823  2610   0     7월22 ?              00:00:28 xfce4-appfinder
      1000 2560334  2645   0     7월16 ?              00:00:00 /usr/libexec/ibus-engine-simple
      1000 2587969 34587   0     7월16 pts/0          00:00:01 zsh
         0 2631864     2   0      09:21 ?              00:00:00 [kworker/u17:2]
      1000 2635244 2477312  0     09:29 pts/9          00:00:00 /usr/bin/zsh -i
      1000 2635587 2477312  0     09:29 pts/10         00:00:00 /usr/bin/zsh -i
      1000 2643695 1998284  0     09:49 pts/5          00:00:00 tail -100f 111_1_FTK_MON_LOGONDB____.20250730
      1000 2653977 2276810  0     10:14 pts/11         00:00:00 /usr/bin/zsh -i
      1000 2672158  2473   0      10:51 pts/19         00:00:00 111_1_FTK_MON_SESSION____
      1000 2672162  2473   0      10:52 pts/19         00:00:00 111_1_FTK_MON_LOGONDB____
      1000 2672268  2473   0      10:52 pts/19         00:00:00 111_1_FTK_MON_SENDERDB___
      1000 2672278  2473   0      10:52 pts/19         00:00:00 111_1_FTK_MON_REPORTDB___
      1000 2672304  2473   0      10:52 pts/19         00:00:00 111_1_FTK_MON_ADMIN______
      1000 2672308  2473   0      10:52 pts/19         00:00:01 111_1_FTK_SESSION_43000__ /home/<USER>/CLionProjects/ftalk_up/cfg/kko_ftk/logonSession_ftk_43.conf
      1000 2672341  2473   1      10:52 pts/19         00:01:47 111_1_FTK_ADMIN__________ /home/<USER>/CLionProjects/ftalk_up/cfg/kko_ftk/adminProcess.conf
      1000 2675761 550966  0      10:59 pts/26         00:00:00 /usr/bin/zsh
      1000 2677626 2675761  0     10:59 pts/26         00:00:00 /usr/local/bin/vim makefile
      1000 2677628 2677626  0     10:59 ?              00:00:01 node /home/<USER>/.vim/plugged/augment.vim/dist/server.js --stdio
      1000 2677909 550966  0      11:00 pts/27         00:00:00 /usr/bin/zsh
         0 2683958     2   0      11:05 ?              00:00:00 [kworker/3:1H-kblockd]
      1000 2687133 1242068  0     11:11 pts/19         00:00:00 zsh -l
         0 2687763     2   0      11:11 ?              00:00:00 [kworker/1:0-cgroup_destroy]
      1000 2690326 34587   0      11:14 pts/28         00:00:00 zsh
      1000 2697233 2687133  0     11:34 pts/19         00:00:00 zsh
      1000 2699283 550966  0      11:35 pts/29         00:00:01 /usr/bin/zsh
         0 2702204     2   0      11:39 ?              00:00:00 [kworker/1:2H-kblockd]
         0 2708551     2   0      11:53 ?              00:00:00 [kworker/u16:3-flush-253:2]
         0 2711186     2   0      12:01 ?              00:00:00 [kworker/0:2-cgroup_destroy]
         0 2714261     2   0      12:11 ?              00:00:00 [kworker/0:0H-kblockd]
         0 2715157     2   0      12:14 ?              00:00:00 [kworker/2:0-ata_sff]
         0 2719568     2   0      12:28 ?              00:00:00 [kworker/u16:1-flush-253:0]
         0 2723407     2   0      12:40 ?              00:00:00 [kworker/3:2-events]
         0 2723675     2   0      12:41 ?              00:00:00 [kworker/3:3]
         0 2723698     2   0      12:41 ?              00:00:00 [kworker/2:0H-kblockd]
         0 2723758     2   0      12:41 ?              00:00:00 [kworker/1:1-mm_percpu_wq]
         0 2724340     2   0      12:43 ?              00:00:00 [kworker/u16:4-flush-253:2]
         0 2724628     2   0      12:44 ?              00:00:00 [kworker/0:0-cgroup_destroy]
         0 2726090     2   0      12:48 ?              00:00:00 [kworker/2:2-ata_sff]
         0 2726751     2   0      12:51 ?              00:00:00 [kworker/0:1-events]
         0 2726928     2   0      12:51 ?              00:00:00 [kworker/u16:0-events_unbound]
      1000 2727320 2254713  0     12:52 ?              00:00:00 /opt/vivaldi/vivaldi-bin --type=renderer --crashpad-handler-pid=2254702 --enable-crash-reporter=,stable --extension-process --change-stack-guard-on-fork=enable --disable-gpu-compositing --lang=ko --num-raster-threads=2 --enable-main-frame-before-activation --renderer-client-id=353 --time-ticks-at-unix-epoch=-1752395935470365 --running-vivaldi --launch-time-ticks=1447949760259 --shared-files=v8_context_snapshot_data:100 --field-trial-handle=3,i,1262746119410881846,1209867511062030097,262144 --variations-seed-version
      1000 2727608  2473   0      12:53 ?              00:00:00 /usr/lib64/xfce4/xfconf/xfconfd
         0 2727888     2   0      12:54 ?              00:00:00 [kworker/2:1-events_freezable_pwr_ef]
      1001 2729015  2473   0      12:55 ?              00:00:00 db_w000_FREE
         0 2729041     2   0      12:55 ?              00:00:00 [kworker/1:2]
      1001 2729340  2473   0      12:56 ?              00:00:00 db_w001_FREE
         0 2729863 28974   0      12:56 ?              00:00:00 sleep 30
      1000 2729865 2477312  0     12:56 pts/30         00:00:00 /usr/bin/zsh -c export PAGER=cat; export LESS=-FX; export GIT_PAGER=cat; cd command_logon_ftk && ps -eo uid:10,pid:6,ppid:6,c:3,stime:10,tty:12,time:10,cmd > test_background_file && ls -la test_background_file && head -5 test_background_file
      1000 2729867 2729865  0     12:56 pts/30         00:00:00 ps -eo uid:10,pid:6,ppid:6,c:3,stime:10,tty:12,time:10,cmd
         0 3621834  1048   0     7월18 ?              00:00:00 sshd: jk [priv]
      1000 3621838 3621834  0    7월18 ?              00:00:02 sshd: jk@notty
      1000 3621840 3621838  0    7월18 ?              00:00:01 /usr/libexec/openssh/sftp-server
      1000 4064978  2626   0     7월25 ?              00:00:01 /usr/libexec/gvfsd-admin --spawner :1.21 /org/gtk/gvfs/exec_spaw/6 --address unix:path=/run/user/1000/bus --dir /run/user/1000
         0 4075208  2473   0     7월25 ?              00:00:53 vmhgfs-fuse .host:/ /mnt/hgfs -o subtype=vmhgfs-fuse,allow_other
