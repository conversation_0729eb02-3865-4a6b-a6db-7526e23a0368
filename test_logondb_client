ELF          >    0@     @       `A          @ 8 
 @ !         @       @ @     @ @     �      �                         @     @                                          @       @     x	      x	                           @      @     		      		                             @       @     �      �                   �-      �=@     �=@     �      �                   �-      �=@     �=@                                8      8@     8@                                  X      X@     X@     D       D              S�td   8      8@     8@                            P�td   �"      �"@     �"@     t       t              Q�td                                                  R�td   �-      �=@     �=@     0      0             /lib64/ld-linux-x86-64.so.2              GNU � �                   GNU 0耿�b��7�v��"�=�꼿         GNU                                             yIk�                            �                      �                      �                      �                      r                      �                      �                      �                      �                      �                      w                      j                      �                      �                                             [                                             ,                       �                      F       @              __gmon_start__ _ITM_deregisterTMCloneTable _ITM_registerTMCloneTable __gxx_personality_v0 _Unwind_Resume connect recv __libc_start_main socket memset select puts close strncpy strlen send sprintf strerror __errno_location libstdc++.so.6 libm.so.6 libgcc_s.so.1 libc.so.6 GCC_3.0 CXXABI_1.3 GLIBC_2.34 GLIBC_2.2.5                                �          P&y           �          簞k                    ��   #     ui	   .      �?@                   �?@                   �?@                   �?@                   @@                    @@                   (@@                   0@@                   8@@                   @@@                   H@@                   P@@                   X@@        	           `@@        
           h@@                   p@@        
           x@@                   �@@                   �@@                   �@@                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ��H��H��/  H��t��H���     �5�/  �%�/  @ �%�/  h    禹����%�/  h   優����%�/  h   湧����%�/  h   欲����%�/  h   �����%�/  h   �����%�/  h   �����%�/  h   �p����%�/  h   �`����%�/  h	   �P����%�/  h
   �@����%�/  h   �0����%�/  h   � ����%z/  h
   �����%r/  h   � ����%j/  h   昱�����1�I��^H��H�崖PTE1�1�H피@ ��.  �f.�     �餉f.�     �H�=)/  H�"/  H9�tH�^.  H��t	���    ��    H�=�.  H�5�.  H)�H��H즈?H진H�H拉tH�-.  H��t��fD  ��    ��=�.   uUH�堰z�����.  ]��ff.�     @ �穴�UH��SH��8!  �싱��H�돛���싱��BH��잗��H� H�틸� @ �    夭��H��잗��H� H�틸� @ �    緩���   勖  H��잗��H�@H�E�H��旣��H�험�  욥 @ �N���H�E�H�틸!@ �    ���H�U�H��旣��H��H�험  �E�}� t+H��旣��H�험  H�틸!@ �    �H���   �V  �5!@ 巍��H�E�X!@ H�E�H�틸�!@ �    ���H�E�H�험Z����H�M�H��旣��H��H�험�  �E�}� y:H��旣��H�험�  H�틸�!@ �    宛��H��旣��H�험1  �   茸   �E�틸�!@ �    ���왈!@ �A��H��錦���    �    H�험飇��H��錦��H��旣��H��H�험  �E�}� u�"@ 燿���D�}� y#H��旣��H�험�  H�틸?"@ �    �#���H��錦���E�틸Y"@ �    ���H��旣��H�험o  �v"@ ����    H��旣��H�험[   �孟H��H��旣��H�험E   H��H�험���H�]弊�UH��H��H�}�H�E幣 ����H�E�H���   �    H�험�����UH��H��H�}�H�E� ��x
H�E� �험;�����UH��H��H�}�H�u��    �   �   �T��H�U��H�E�� ��y5���� �험V��H��H�E�H��� @ H�퓔    �	������腰   H�E��n   �    H�험Y��f�E� H�E�H�U�H�J�k   H��H�勸'��H�E�� H�M��n   H��험.��쥡��tI���� �험���H��H�E�H���) @ H�퓔    �|��H�E�� �험?��H�E�� ����������    ��UH��H��0H�}�H�u�U�H�E� ��y�����S�E�Hc�H�E� H�u仙    �험f���E�}� y+僚��� �험/��H��H�E�H���< @ H�퓔    外���E惶�UH��H��0H�}�H�u�U�H�E� ��y�����S�E�Hc�H�E� H�u仙    �험숟���E�}� y+�n��� �험딩��H��H�E�H���L @ H�퓔    �h���E惶�UH��H�李   H��X�����T�����P���H��X���� ��y
����瑢   H��p���H�E曹E�    �H�E�U�H��    �E��}�v�H��X���� �P?��H쩝���Hc�H���p���H��X���� ��?�   ��H袋H��H	�Hc�H���p�����T���H�H��`�����P���H�H��h���H��X���� �xH��`���H��p���I�橘    �    H�펨鴻�����UH��H�� H�}�H�u�H�E翁    �   H�험筬���E�}� u!H�E�H���\ @ H�퓔    ����    �}�}� y2妖��� �험4��H��H�E�H���d @ H�퓔    了�������EH�M�H�E翁    H��H�험入���E�}� u!H�E�H���v @ H�퓔    蜈��������E惶�UH��H��H�}�H�E� ��xH�E� �험E��H�E幣 �������UH��H�}�H�E�H��]�  ��H��H���                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     socket create failed: %s connect failed: %s send failed: %s recv failed: %s timeout select failed: %s close by peer     Usage: %s <domain_socket_path>
 Example: %s /home/<USER>/CLionProjects/ftalk_up/cfg/kko_ftk/logonDB
 === LogonDB Client Test === Connecting to: %s
 [ERR] Connection failed: %s
 [INF] Connected successfully       ID=testuser&PASSWORD=testpass&REPORT=S  [INF] Sending test message: %s
 [ERR] Send failed: %s
 [INF] Sent %d bytes
     [INF] Waiting for response (5 second timeout)...        [WARN] Timeout occurred - no response within 5 seconds [ERR] Receive failed: %s
 [INF] Received %d bytes: %s
 [INF] Connection closed   ;t   
   ����   ����   嗜���   ���$  ����   2��   X��D  j��d  涯���  ^���  X���  (���  X��             zR x�        ��&    D   0   $��       $   D   其��   FJw� ?;*3$"       l   ��3    A�C
n          zPLR x @ �      $   
��&   �$@ A�C
a      �   ��   A�C
    �   ���y    A�C
t       X��y    A�C
t     0  뀝���    A�C
�     P  ����    A�C
�     p  <��0    A�C
k      �  L��    A�C
M   $   (  Z��x  �$@ A�C
H�k      �� ��9!  ��� �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          @     �@            �              �              �                            @     
       �@            �=@                          �=@                   萃�o    �@            �@            �@     
       :                                           @@            �                           �@            �@            `       	              ���o    (@     ���o           ��o    �@                                                                                                                                     �=@                     6@     F@     V@     f@     v@     �@     �@     �@     �@     �@     �@     �@     �@     @     @     &@         GCC: (GNU) 11.5.0 20240719 (Red Hat 11.5.0-5) AV:4p1292 RV:running gcc 11.5.0 20240719 BV:annobin gcc 11.5.0 20240719 GW:0x3d2056a ../sysdeps/x86/abi-note.c SP:3 SC:1 CF:8 ../sysdeps/x86/abi-note.c FL:-1 ../sysdeps/x86/abi-note.c GA:1 PI:3 SE:0 iS:0 GW:0x3d2056a init.c CF:8 init.c FL:-1 init.c GW:0x3d2056a static-reloc.c CF:8 static-reloc.c FL:-1 static-reloc.c          GA$3a1 0@     V@              GA$3a1 e@     e@              GA$3a1  @     @              GA$3a1 �@     @              GA$3a1 p@     @              GA$3a1 �@     �@              GA$3a1 �@     �@              GA$3a1 @     @              GA$3a1 @     	@                                       @                   8@                   X@                   |@                   �@                   �@                   �@                   �@                  	 (@                  
 �@                   �@                    @                  
  @                   0@                   �@                     @                   �"@                   #@                   �$@                   �=@                   �=@                   �=@                   �?@                    @@                   �@@                   �@@                                                               �`@                 �                >     |@             H    �                S     p@             U     �@             h     �@             ~     �@@            �     �=@             �     @             �     �=@             �    �                H    �                �     �$@                  �                     �"@                 �=@                  @@             4                     �                     Q  "  �@           r  "  �@     3       �                     �    �@@             �                     �  "  �@     �       {     �@@             �                     �                     �      @            �                         @     x                           (  "  t@     y       ?                     S                     f    @             s  "  �@     &       �  "  �@     �       �   �@             �                     �   `@            �  "  �@     3       �  "  �@                                                       '  "  �@     &           0@     &       9    @             ?   �@@             K  "  �@     y       c  "  �@     0       y    �@@             �    �@@             �    �@@             �                     �      @             �                      �                     �                      
                      $                      /usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64/crt1.o __abi_tag crtstuff.c deregister_tm_clones __do_global_dtors_aux completed.0 __do_global_dtors_aux_fini_array_entry frame_dummy __frame_dummy_init_array_entry test_logondb_client.cpp __FRAME_END__ __GNU_EH_FRAME_HDR _DYNAMIC _GLOBAL_OFFSET_TABLE_ __errno_location@GLIBC_2.2.5 _ZN9CKSSocket13connectDomainEPKc _ZN9CKSSocketC2Ev sprintf@GLIBC_2.2.5 _edata socket@GLIBC_2.2.5 _ZN9CKSSocket6rcvmsgEPc recv@GLIBC_2.2.5 strerror@GLIBC_2.2.5 _IO_stdin_used strlen@GLIBC_2.2.5 main send@GLIBC_2.2.5 _ZN9CKSSocket4recvEPci strncpy@GLIBC_2.2.5 memset@GLIBC_2.2.5 __dso_handle _ZN9CKSSocketD2Ev _ZN9CKSSocket6selectEii _fini __libc_start_main@GLIBC_2.34 _dl_relocate_static_pie _ZN9CKSSocketC1Ev _ZN9CKSSocket5errorEv connect@GLIBC_2.2.5 select@GLIBC_2.2.5 _ZN9CKSSocketD1Ev _init __TMC_END__ _ZN9CKSSocket4sendEPKci _ZN9CKSSocket5closeEv __data_start _end __bss_start puts@GLIBC_2.2.5 __gxx_personality_v0@CXXABI_1.3 _ITM_deregisterTMCloneTable _Unwind_Resume@GCC_3.0 __gmon_start__ _ITM_registerTMCloneTable close@GLIBC_2.2.5  .symtab .strtab .shstrtab .interp .note.gnu.property .note.gnu.build-id .note.ABI-tag .gnu.hash .dynsym .dynstr .gnu.version .gnu.version_r .rela.dyn .rela.plt .init .text .fini .rodata .eh_frame_hdr .eh_frame .gcc_except_table .init_array .fini_array .dynamic .got .got.plt .data .bss .comment .annobin.notes .gnu.build.attributes                                                                                   @                                         #             8@     8                                     6             X@     X      $                              I             |@     |                                     W   ��o       �@     �      $                             a             �@     �      �                          i             �@     �      :                             q   ���o       �@     �      *                            ~   ���o       (@     (      p                            �             �@     �      `                            �      B       �@     �      �                          �              @                                          �              @                                        �             0@     0      �                             �             �@     �      
                              �               @             �                             �             �"@     �"      t                              �             #@     #      �                             �             �$@     �$                                    �             �=@     �-                                   �             �=@     �-                                   �             �=@     �-                                              �?@     �/                                                 @@      0      �                                         �@@     �0                                                �@@     �0                                         0               �0      .                             (     0               �0      >                            7             �`@     2      D                                                   P3      �         /                 	                      �;      6                                                   @      M                             