# Reporting security vulnerabilities

Oracle values the independent security research community and believes that
responsible disclosure of security vulnerabilities helps us ensure the security
and privacy of all our users.

Please do NOT raise a GitHub Issue to report a security vulnerability. If you
believe you have found a security vulnerability, please submit a report to
[<EMAIL>][1] preferably with a proof of concept. Please review
some additional information on [how to report security vulnerabilities to
Oracle][2].  We encourage people who contact Oracle Security to use email
encryption using [our encryption key][3].

We ask that you do not use other channels or contact the project maintainers
directly.

Non-vulnerability related security issues such as great new ideas for security
features are welcome on GitHub Issues.

## Security updates, alerts and bulletins

Our project will typically release security fixes in conjunction with each
patch release.

Oracle security information, including past advisories, is available on the
[security alerts][4] page.

## Security-related information

We will provide security related information such as a threat model,
considerations for secure use, or any known security issues in our
documentation. Please note that labs and sample code are intended to
demonstrate a concept and may not be sufficiently hardened for production use.

[1]: mailto:<EMAIL>
[2]: https://www.oracle.com/corporate/security-practices/assurance/vulnerability/reporting.html
[3]: https://www.oracle.com/security-alerts/encryptionkey.html
[4]: https://www.oracle.com/security-alerts/
