---
name: Questions and Runtime Problems
about: For general ODPI-C questions
title: ''
labels: question
assignees: ''

---

<!--

Thank you for using ODPI-C.

Review the user manual: https://oracle.github.io/odpi/doc/index.html

Please answer these questions so we can help you.

Use Markdown syntax, see https://docs.github.com/github/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax

GitHub issues that are not updated for a month may be automatically closed.  Feel free to update them at any time.

-->

1. What version of ODPI-C are you using (see dpi.h)?

2. What OS (and version) is your application on?

3. What compiler (and version) did you use?

4. What is your version of the Oracle Client (e.g. Instant Client)?  How was it installed?  Where it is installed?

5. What is your Oracle Database version?

6. What is the `PATH` environment variable (on Windows) or `LD_LIBRARY_PATH` (on Linux) set to?

7. What environment variables did you set?  How *exactly* did you set them?

8. What problem or error(s) you are seeing?

9. Do you have a runnable code snippet to describe the problem?

<!--

Use a gist for long code, see https://gist.github.com/

Or format code by using three backticks on a line before and after code snippets, for example:

```
#include <stdio.h>
```
-->
