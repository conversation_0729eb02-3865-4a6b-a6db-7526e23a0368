---
name: Installation Problems
about: Use this for installation questions
title: ''
labels: install & configuration
assignees: ''

---

<!--

Thank you for using ODPI-C.

Do these before creating a new issue:

    If you are installing a driver like cx_Oracle, node-oracledb, godror, or any other driver that uses ODPI-C then open an issue on the driver's issue page.

    Review the user manual: https://oracle.github.io/odpi/doc/index.html

    If you have a `DPI-1047`, `DPI-1050` or `DPI-1072` error, re-review the link above.

    Google any errors.

Then please answer these questions so we can help you.

GitHub issues that are not updated for a month may be automatically closed.  Feel free to update them at any time.

-->

1. What version of ODPI-C are you using (see dpi.h)?

2. What OS (and version) is your application on?

3. What compiler (and version) did you use?

4. What is your version of the Oracle Client (e.g. Instant Client)?  How was it installed?  Where it is installed?

5. What is your Oracle Database version?

6. What is the `PATH` environment variable (on Windows) or `LD_LIBRARY_PATH` (on Linux) set to?

7. What environment variables did you set?  How *exactly* did you set them?

8. What problem or error(s) you are seeing?
