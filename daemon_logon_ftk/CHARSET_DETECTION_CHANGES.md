# Database Charset Detection Implementation

## Overview
Added functionality to detect database character set and intelligently decide whether UTF-8 to EUC-KR conversion is needed.

## Changes Made

### 1. Header File Changes (inc/DatabaseORA_MMS.h)
- Added global variable declarations:
  ```cpp
  extern char g_db_charset[50];
  extern bool g_db_is_euckr;
  extern bool g_charset_checked;
  ```
- Added method declaration:
  ```cpp
  int checkDatabaseCharset(char* charset, int charset_size);
  ```

### 2. Implementation Changes (lib/DatabaseORA_MMS.cpp)
- Added global variable definitions
- Implemented `checkDatabaseCharset()` method that:
  - Queries `V$NLS_PARAMETERS` for `NLS_CHARACTERSET`
  - Checks if charset is EUC-KR compatible (KO16MSWIN949, KO16KSC5601)
  - Updates global variables with results
  - Logs charset information
- Modified `connectToOracle()` to call charset detection after successful connection

### 3. Pro*C Version Changes (build_occi/DatabaseORA_MMS.pc)
- Added same global variables and method implementation
- Updated connection logic to include charset detection

### 4. Main Application Changes (src/senderFtalkProDB.cpp)
- Added extern declarations for global variables
- Modified encoding conversion logic to use database charset information:
  - `needsConversion = isUtf8 && g_charset_checked && g_db_is_euckr`
- Updated all variable processing sections:
  - MessageVariable
  - ButtonVariable
  - CouponVariable
  - ImageVariable
  - VideoVariable
  - CommerceVariable
  - CarouselVariable
- Enhanced logging to show conversion decisions

## How It Works

1. **Database Connection**: When `connectToOracle()` is called, it automatically calls `checkDatabaseCharset()`
2. **Charset Detection**: The method queries the database for its character set
3. **EUC-KR Check**: Determines if the database uses EUC-KR compatible encoding
4. **Conversion Decision**: UTF-8 data is only converted to EUC-KR if:
   - Input is UTF-8 encoded
   - Database charset has been successfully checked
   - Database uses EUC-KR compatible charset

## Benefits

1. **Intelligent Conversion**: Only converts when actually needed
2. **Better Logging**: Clear indication of when and why conversion occurs
3. **Error Prevention**: Avoids unnecessary conversions that could corrupt data
4. **Debugging**: Easy to see database charset and conversion decisions in logs

## Testing

A test program (`test_charset_detection.cpp`) is provided to verify the functionality.

## Log Examples

```
[INF] CDatabaseORA::checkDatabaseCharset() Success - Charset[KO16MSWIN949] EUC-KR_Compatible[YES]
[INF] UTF-8 encoding detected, DB charset[KO16MSWIN949], conversion needed[YES] for MMSID[12345]
[INF] UTF-8 to EUC-KR conversion successful for MessageVariable MMSID[12345]
```

## Supported EUC-KR Charsets

- `KO16MSWIN949`: Windows Korean (Code Page 949)
- `KO16KSC5601`: Korean Standard (KSC 5601)

Additional charsets can be easily added to the detection logic if needed.
