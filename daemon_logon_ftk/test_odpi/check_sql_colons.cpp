#include <iostream>
#include <sstream>
#include <string>

using namespace std;

int main() {
    printf("=== Checking SQL for colon characters ===\n");
    
    // Simulate the same SQL generation as logonDB
    ostringstream sql_stream;
    
    int nRptNoDataSleep = 3;
    int nmRST = -999;
    string szCID = "talktest1";
    string szPWD = "talktest1";
    
    sql_stream << "DECLARE "
               << "  v_appname VARCHAR2(256); "
               << "  v_sip VARCHAR2(64); "
               << "  v_pid NUMBER; "
               << "  v_job NUMBER; "
               << "  v_c_job NUMBER; "
               << "  v_prt NUMBER; "
               << "  v_cnt NUMBER; "
               << "  v_rpt_cnt NUMBER; "
               << "  v_server_info VARCHAR2(512); "
               << "  v_rpt_sleep_cnt NUMBER DEFAULT " << nRptNoDataSleep << "; "
               << "  v_sender_proc VARCHAR2(256); "
               << "  v_report_proc VARCHAR2(256); "
               << "  v_senderdb_info VARCHAR2(512); "
               << "  v_reportdb_info VARCHAR2(512); "
               << "  v_logfile_info VARCHAR2(512); "
               << "  v_etc VARCHAR2(512); "
               << "  v_rst NUMBER DEFAULT " << nmRST << "; "
               << "  v_rstmsg VARCHAR2(512); "
               << "BEGIN "
               << "  proc_check_mms_login_ext2("
               << "    in_cid=>'" << szCID << "',"
               << "    in_pwd=>'" << szPWD << "',"
               << "    ot_appname=>v_appname,"
               << "    ot_sip=>v_sip,"
               << "    ot_pid=>v_pid,"
               << "    ot_job=>v_job,"
               << "    ot_c_job=>v_c_job,"
               << "    ot_prt=>v_prt,"
               << "    ot_cnt=>v_cnt,"
               << "    ot_rpt_cnt=>v_rpt_cnt,"
               << "    ot_server_info=>v_server_info,"
               << "    ot_rpt_sleep_cnt=>v_rpt_sleep_cnt,"
               << "    ot_sender_proc=>v_sender_proc,"
               << "    ot_report_proc=>v_report_proc,"
               << "    ot_senderdb_info=>v_senderdb_info,"
               << "    ot_reportdb_info=>v_reportdb_info,"
               << "    ot_logfile_info=>v_logfile_info,"
               << "    ot_etc=>v_etc,"
               << "    ot_rst=>v_rst,"
               << "    ot_rstmsg=>v_rstmsg"
               << "  ); "
               << "END;";
    
    string final_sql = sql_stream.str();
    
    printf("SQL length: %zu\n", final_sql.length());
    
    // Check for colon characters
    size_t colon_count = 0;
    size_t pos = 0;
    while ((pos = final_sql.find(":", pos)) != string::npos) {
        printf("Found ':' at position %zu: context '%.20s'\n", pos, final_sql.substr(pos, 20).c_str());
        colon_count++;
        pos++;
    }
    
    printf("Total ':' characters found: %zu\n", colon_count);
    
    if (colon_count == 0) {
        printf("? No colon characters - should not trigger bind processing\n");
    } else {
        printf("? Found colon characters - may trigger bind processing\n");
    }
    
    // Check for potential bind variable patterns
    pos = 0;
    while ((pos = final_sql.find(":=", pos)) != string::npos) {
        printf("Found ':=' (assignment) at position %zu\n", pos);
        pos += 2;
    }
    
    return 0;
}
