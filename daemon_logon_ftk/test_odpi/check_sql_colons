ELF          >    `"@     @       0]          @ 8 
 @ !         @       @ @     @ @     �      �                         @     @                                          @       @     �      �                             @       @     �      �                    0       0@      0@      	       	                   �=      �M@     �M@     l      p                   �=      �M@     �M@                                8      8@     8@                                  X      X@     X@     D       D              S�td   8      8@     8@                            P�td   �5      �5@     �5@     �       �              Q�td                                                  R�td   �=      �M@     �M@     8      8             /lib64/ld-linux-x86-64.so.2              GNU � �                   GNU x15��.b!�o^�ZY#k��C         GNU                      &                     &   4�=�xIk�)E�L                        �                                          �                                          O                     �                     �                     :                                          �                     �                     �                      �                     Z                                          �                     �                     9                     �                     �                     F                      �                                          �                     �                      U                     �                     �                     �                                            �                     B                     m                                            ,                                            �                     Z  "  �*@           -     �!@                  P"@              __gmon_start__ _ITM_deregisterTMCloneTable _ITM_registerTMCloneTable _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE13_S_copy_charsEPcPKcS7_ _ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE6lengthEv _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE7_M_dataEPc _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE11_M_capacityEm _ZStlsIcSt11char_traitsIcESaIcEERSt13basic_ostreamIT_T0_ES7_RKNSt7__cxx1112basic_stringIS4_S5_T1_EE _ZNSolsEi _ZNSaIcED1Ev _ZNKSt7__cxx1119basic_ostringstreamIcSt11char_traitsIcESaIcEE3strEv _ZNSt7__cxx1119basic_ostringstreamIcSt11char_traitsIcESaIcEEC1Ev _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_M_constructIPKcEEvT_S8_St20forward_iterator_tag _ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE6substrEmm _ZNSt8ios_base4InitD1Ev __cxa_begin_catch __gxx_personality_v0 _ZNSaIcEC1Ev _ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE5c_strEv _ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE4findEPKcm _ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE7_M_dataEv _ZNSaIcED2Ev _ZStlsISt11char_traitsIcEERSt13basic_ostreamIcT_ES5_PKc _ZNSt8ios_base4InitC1Ev _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE9_M_createERmm __cxa_rethrow _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEED1Ev _ZNSt7__cxx1119basic_ostringstreamIcSt11char_traitsIcESaIcEED1Ev _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE13_M_set_lengthEm _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE10_M_disposeEv __cxa_end_catch _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE13_M_local_dataEv _ZSt19__throw_logic_errorPKc _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_Alloc_hiderC1EPcRKS3_ _Unwind_Resume __libc_start_main __cxa_atexit puts strlen printf libstdc++.so.6 libm.so.6 libgcc_s.so.1 libc.so.6 GCC_3.0 GLIBCXX_3.4.26 CXXABI_1.3 GLIBCXX_3.4.21 GLIBCXX_3.4 GLIBC_2.34 GLIBC_2.2.5                                                 �         P&y   �        �     P   v�        簞k        q�        t)�   *        �         ��   6     ui	   A      �O@        
           �O@                   �O@        "           �O@        #           P@                    P@                   (P@                   0P@                   8P@                   @P@                   HP@                   PP@                   XP@        	           `P@                   hP@                   pP@        
           xP@                   �P@                   �P@                   �P@                   �P@                   �P@                   �P@                   �P@                   �P@                   �P@                   �P@                   �P@                   �P@                   �P@                   �P@                   �P@        '           �P@                    Q@                   Q@                    Q@        !           Q@        $            Q@        %           (Q@        (                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ��H��H��/  H��t��H���     �5�/  �%�/  @ �%�/  h    禹����%�/  h   優����%�/  h   湧����%�/  h   欲����%�/  h   �����%�/  h   �����%�/  h   �����%�/  h   �p����%�/  h   �`����%�/  h	   �P����%�/  h
   �@����%�/  h   �0����%�/  h   � ����%z/  h
   �����%r/  h   � ����%j/  h   昱����%b/  h   禹����%Z/  h   優����%R/  h   湧����%J/  h   欲����%B/  h   �����%:/  h   �����%2/  h   �����%*/  h   �p����%"/  h   �`����%/  h   �P����%/  h   �@����%
/  h   �0����%/  h   � ����%�.  h   �����%�.  h   � ����%�.  h   昱���%�.  h    禹���%�.  h!   優���%�.  h"   湧����1�I��^H��H�崖PTE1�1�H피F#@ �[-  �f.�     �餉f.�     �H�=�.  H��.  H9�tH�.-  H��t	���    ��    H�=a.  H�5Z.  H)�H��H즈?H진H�H拉tH��,  H��t��fD  ��    ��=.   uUH�堰z����.  ]��ff.�     @ �穴�UH��SH��(  �0@ �d���H��0���H�험����E�   �E���H�E�H�험����H�U�H������:0@ H�험  H�E�H�험���H�E�H�험[���H�U�H��縱���:0@ H�험�  H�E�H�험g��H��0����D0@ H�험���M0@ H�험���i0@ H�험缸���0@ H�험賃���0@ H�험絮��쐼0@ H�험溺��쓸0@ H�험퉁��아0@ H�험뫘��액0@ H�험���언0@ H�험����1@ H�험���H��E��H�羸q���21@ H�험t���81@ H�험g���X1@ H�험Z���x1@ H�험M���1@ H�험@��안1@ H�험3��얗1@ H�험&���2@ H�험��H��E��H�羸缸���21@ H�험曉���2@ H�험鋌���42@ H�험述���;2@ H�험憐���X2@ H�험힘��H��H�����H��H�羸｛���f2@ H�험����i2@ H�험���H��H��縱��H��H�羸t���f2@ H�험w���w2@ H�험j���2@ H�험]��쑤2@ H�험P��씀2@ H�험C��알2@ H�험6��얄2@ H�험)��얾2@ H�험���3@ H�험���(3@ H�험���P3@ H�험衝���x3@ H�험橈���3@ H�험壁��안3@ H�험九��언3@ H�험질���4@ H�험덜���=4@ H�험����P4@ H�험����c4@ H�험����{4@ H�험����4@ H�험s��H��旗��H��0���H��H�험j��H��旗��H�험艤��H�틸�4@ �    �i��H�E�    H�E�    �WH�E�H�U�H�둔���   H�험+��H�E�H�험o��H��H�E�H�틸�4@ �    ���H�E�H�험j��H�E�H�E�H�U�H��旗��씻4@ H�험表��H�E�H�}����u�H�E�H�틸�4@ �    臥��H�}� u욹4@ �A���
�05@ �5��H�E�    �H�E�H�틸h5@ �    ���H�E�H�U�H��旗���5@ H�험x��H�E�H�}����u슥    H��旗��H�험ㆈ��H��縱��H�험���H�����H�험���H��0���H�험7���驀�   H��H�E�H�험���WH��H�E�H�험 ���7H��H�E�H�험?���H��H��旗��H�험+���H��H��縱��H�험��H�����H�험��H��0���H�험벅��H��H�험^��H�]弊�UH��H���}�u�}�u'�}��  u�5Q@ 矮���0@ �5Q@ �P"@ 瑤�����UH�孃��  �   瑥���]�UH��H�� H�}�H�E�H�E片    ��tH�E�H�험2   �
H�E�H�험N�����UH��H�}�H�u�H�E��H�E�� 8���]�UH��H�� H�}�H�E�    �H�E��E� H�U�H�E�H�H�E�H��H�羸�������u�H�E弊�UH��H��H�}�H�E�H�험������UH��SH��8H�}�H�u�H�U�H�]�H�E�H�험迅��H��H�E�H��H��H�省>��H�}� tH�E�H�험託��H�U�H伎�   H�E�H�U�H�M�H�E�H��H�험T   �H��H�E�H�험]���H��H�험람��H�]弊�UH��H��H�}�H�u�H�E�H�험8  H�E�H�U�H��H�험/  ��UH��SH��8H�}�H�u�H�U�H�E�H�험�   ��tH�E�H;E�t�   ��    ��t
�5@ 猥��H�U�H�E�H��H�험r���H�E�H�E�H��v=H�M�H�E曼    H��H�험��H��H�E�H��H�험빎��H�U�H�E�H��H�험��H�E�H�험��H��H�U�H�E�H��H�勸��H�U�H�E�H��H�험B���,H�험慓��H�E�H�험鐄�����H�췄O��H��H�험t��H�]弊�UH��H�}�H�}� ��]�UH��H�}�]�UH��H�}�H�u�H�E�H+E�]츈�H��H���                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             === Checking SQL for colon characters === talktest1 DECLARE    v_appname VARCHAR2(256);    v_sip VARCHAR2(64);    v_pid NUMBER;    v_job NUMBER;    v_c_job NUMBER;    v_prt NUMBER;    v_cnt NUMBER;    v_rpt_cnt NUMBER;        v_server_info VARCHAR2(512);    v_rpt_sleep_cnt NUMBER DEFAULT  ;       v_sender_proc VARCHAR2(256);    v_report_proc VARCHAR2(256);    v_senderdb_info VARCHAR2(512);          v_reportdb_info VARCHAR2(512);          v_logfile_info VARCHAR2(512);    v_etc VARCHAR2(512);    v_rst NUMBER DEFAULT    v_rstmsg VARCHAR2(512);  BEGIN    proc_check_mms_login_ext2(     in_cid=>' ',     in_pwd=>'     ot_appname=>v_appname,     ot_sip=>v_sip,     ot_pid=>v_pid,     ot_job=>v_job,     ot_c_job=>v_c_job,     ot_prt=>v_prt,     ot_cnt=>v_cnt,     ot_rpt_cnt=>v_rpt_cnt,          ot_server_info=>v_server_info,          ot_rpt_sleep_cnt=>v_rpt_sleep_cnt,      ot_sender_proc=>v_sender_proc,          ot_report_proc=>v_report_proc,          ot_senderdb_info=>v_senderdb_info,      ot_reportdb_info=>v_reportdb_info,      ot_logfile_info=>v_logfile_info,     ot_etc=>v_etc,     ot_rst=>v_rst,     ot_rstmsg=>v_rstmsg   );  END; SQL length: %zu
  Found ':' at position %zu: context '%.20s'
 :   Total ':' characters found: %zu
        ? No colon characters - should not trigger bind processing      ? Found colon characters - may trigger bind processing  Found ':=' (assignment) at position %zu
 :=     basic_string::_M_construct null not valid   ;�      \���   ����   莖���   ���4  曖���  "���  7���   q��\  ���|  免���  締���  ����  얽��  餃��,  性��L  柔��l         zR x�        足��&    D   0   ��       $   D   ���@   FJw� ?;*3$"       l   ;��:    A�C
u          zPLR x�!@ �  $   $   F��b  �8@ A�C
H�U     �   
��!    A�C
\      �   ��F    A�C
A       4��    A�C
V   $   �   0���   �8@ A�C
E��      \  ���1    A�C
l   $   �   뀝��  �8@ A�C
E�     �  ���    A�C
M      �  ���
    A�C
E      �  v��    A�C
Q        P��>    A�C
y      $  n��    A�C
P       ��-  R�	 ��	 ���
 �3�
 ��	 �B�
 �
  ��
   H.x �    �%Dc  ���  �
� �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  @#@     �(@     #@            �             �             �             �               @     
       �+@            �M@                          �M@                   萃�o    �@            �@            �@     
       M                                           P@            H                           H@            �@            `       	              ���o    H@     ���o           ��o    �@                                                                                                                                     �M@                     6 @     F @     V @     f @     v @     � @     � @     � @     � @     � @     � @     � @     � @     !@     !@     &!@     6!@     F!@     V!@     f!@     v!@     �!@     �!@     �!@     �!@     �!@     �!@     �!@     �!@     "@     "@     &"@     6"@     F"@     V"@         GCC: (GNU) 11.5.0 20240719 (Red Hat 11.5.0-5) AV:4p1292 RV:running gcc 11.5.0 20240719 BV:annobin gcc 11.5.0 20240719 GW:0x3d2056a ../sysdeps/x86/abi-note.c SP:3 SC:1 CF:8 ../sysdeps/x86/abi-note.c FL:-1 ../sysdeps/x86/abi-note.c GA:1 PI:3 SE:0 iS:0 GW:0x3d2056a init.c CF:8 init.c FL:-1 init.c GW:0x3d2056a static-reloc.c CF:8 static-reloc.c FL:-1 static-reloc.c          GA$3a1 `"@     �"@              GA$3a1 �"@     �"@              GA$3a1   @      @              GA$3a1 �+@     �+@              GA$3a1 �"@     F#@              GA$3a1 �+@     �+@              GA$3a1 �+@     �+@              GA$3a1  @      @              GA$3a1 �+@     �+@                                       @                   8@                   X@                   |@                   �@                   �@                   �@                   �@                  	 H@                  
 �@                   H@                     @                  
   @                   `"@                   �+@                    0@                   �5@                   P6@                   �8@                   �M@                   �M@                   �M@                   �O@                    P@                   0Q@                   4Q@                                                               8q@                 �                >     |@             H    �                S     �"@             U     �"@             h     #@             ~     4Q@            �     �M@             �     @#@             �     �M@             �    �                �     5Q@                 �(@     >       0    �(@            H    �                D    �8@                  �                R     �5@             e    �M@             n     P@             �                     �                     �  "  �+@                                 d    4Q@             �	     0Q@             k     0@            z  "  �*@           �                     �                     M                     ?    F#@     b      `   0@             m                     �                                          5   �+@             ;  "  5)@     !       c  "  �+@            �                     �   �"@            �                                          h                     �                     �  "  �)@            �	    `"@     &       <                     �                     �                     #                     �  "  �)@            �  "  V)@     F       �                     
                     Z                     �     @             �   8Q@             �                     	  "  �)@     �       b	                     �	    0Q@             �	    8Q@             �	  "  �+@     
       
                     -
                     |
    4Q@             �
                     �
                     �
  "  P*@     1                                  �!@             ;  "  �)@     �       �                     �                      �                     �                     �                     6  "  �(@     :       W                      f                      �                     �                     $
     P"@              /usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64/crt1.o __abi_tag crtstuff.c deregister_tm_clones __do_global_dtors_aux completed.0 __do_global_dtors_aux_fini_array_entry frame_dummy __frame_dummy_init_array_entry check_sql_colons.cpp _ZStL8__ioinit _Z41__static_initialization_and_destruction_0ii _GLOBAL__sub_I_main __FRAME_END__ __GNU_EH_FRAME_HDR _DYNAMIC _GLOBAL_OFFSET_TABLE_ printf@GLIBC_2.2.5 _ZNSaIcED2Ev@GLIBCXX_3.4 _ZSt10__distanceIPKcENSt15iterator_traitsIT_E15difference_typeES3_S3_St26random_access_iterator_tag _ZNSt7__cxx1119basic_ostringstreamIcSt11char_traitsIcESaIcEED1Ev@GLIBCXX_3.4.21 _edata _IO_stdin_used _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_M_constructIPKcEEvT_S8_St20forward_iterator_tag __cxa_begin_catch@CXXABI_1.3 _ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE5c_strEv@GLIBCXX_3.4.21 strlen@GLIBC_2.2.5 __dso_handle _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEED1Ev@GLIBCXX_3.4.21 _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE13_M_set_lengthEm@GLIBCXX_3.4.21 _ZSt19__throw_logic_errorPKc@GLIBCXX_3.4 _fini _ZN9__gnu_cxx11char_traitsIcE2eqERKcS3_ _ZN9__gnu_cxx17__is_null_pointerIKcEEbPT_ __libc_start_main@GLIBC_2.34 _dl_relocate_static_pie _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE13_M_local_dataEv@GLIBCXX_3.4.21 _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE7_M_dataEPc@GLIBCXX_3.4.21 __cxa_atexit@GLIBC_2.2.5 _ZStlsIcSt11char_traitsIcESaIcEERSt13basic_ostreamIT_T0_ES7_RKNSt7__cxx1112basic_stringIS4_S5_T1_EE@GLIBCXX_3.4.21 _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_Alloc_hiderD1Ev _ZStlsISt11char_traitsIcEERSt13basic_ostreamIcT_ES5_PKc@GLIBCXX_3.4 _ZNKSt7__cxx1119basic_ostringstreamIcSt11char_traitsIcESaIcEE3strEv@GLIBCXX_3.4.21 _ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE6substrEmm@GLIBCXX_3.4.21 _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_Alloc_hiderC1EPcRKS3_@GLIBCXX_3.4.21 _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_Alloc_hiderD2Ev _ZN9__gnu_cxx11char_traitsIcE6lengthEPKc _ZNSaIcED1Ev@GLIBCXX_3.4 _ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE7_M_dataEv@GLIBCXX_3.4.21 _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE13_S_copy_charsEPcPKcS7_@GLIBCXX_3.4.21 _init __TMC_END__ _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE10_M_disposeEv@GLIBCXX_3.4.21 _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEC1IS3_EEPKcRKS3_ _ZNSt7__cxx1119basic_ostringstreamIcSt11char_traitsIcESaIcEEC1Ev@GLIBCXX_3.4.26 __data_start _end _ZSt19__iterator_categoryIPKcENSt15iterator_traitsIT_E17iterator_categoryERKS3_ __cxa_rethrow@CXXABI_1.3 _ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE6lengthEv@GLIBCXX_3.4.21 __bss_start _ZNSt8ios_base4InitC1Ev@GLIBCXX_3.4 puts@GLIBC_2.2.5 _ZSt8distanceIPKcENSt15iterator_traitsIT_E15difference_typeES3_S3_ __cxa_end_catch@CXXABI_1.3 __gxx_personality_v0@CXXABI_1.3 _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEC2IS3_EEPKcRKS3_ _ZNSolsEi@GLIBCXX_3.4 _ITM_deregisterTMCloneTable _Unwind_Resume@GCC_3.0 _ZNSaIcEC1Ev@GLIBCXX_3.4 _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE9_M_createERmm@GLIBCXX_3.4.21 _ZNSt11char_traitsIcE6lengthEPKc __gmon_start__ _ITM_registerTMCloneTable _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE11_M_capacityEm@GLIBCXX_3.4.21 _ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE4findEPKcm@GLIBCXX_3.4.21 _ZNSt8ios_base4InitD1Ev@GLIBCXX_3.4  .symtab .strtab .shstrtab .interp .note.gnu.property .note.gnu.build-id .note.ABI-tag .gnu.hash .dynsym .dynstr .gnu.version .gnu.version_r .rela.dyn .rela.plt .init .text .fini .rodata .eh_frame_hdr .eh_frame .gcc_except_table .init_array .fini_array .dynamic .got .got.plt .data .bss .comment .annobin.notes .gnu.build.attributes                                                                                 @                                         #             8@     8                                     6             X@     X      $                              I             |@     |                                     W   ��o       �@     �      0                             a             �@     �      �                          i             �@     �      M                             q   ���o       �@     �      R                            ~   ���o       H@     H      �                            �             �@     �      `                            �      B       H@     H      H                          �               @                                           �               @             @                            �             `"@     `"      d	                             �             �+@     �+      
                              �              0@      0      �                             �             �5@     �5      �                              �             P6@     P6      D                             �             �8@     �8      l                              �             �M@     �=                                   �             �M@     �=                                   �             �M@     �=                                              �O@     �?                                                 P@      @      0                                        0Q@     0A                                                4Q@     4A                                         0               4A      .                             (     0               bA      >                            7             8q@     �B      D                                                   �C      �
         2                 	                      �N      H
                                                   �[      M                             