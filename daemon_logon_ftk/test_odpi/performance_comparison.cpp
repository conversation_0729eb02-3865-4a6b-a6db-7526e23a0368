#include "../../libsrc/odpi/inc/odpi_wrapper.hh"
#include <iostream>
#include <chrono>
#include <vector>
#include <cstring>

using namespace std;
using namespace std::chrono;

// Performance measurement utilities
class PerformanceTimer {
private:
    high_resolution_clock::time_point start_time;
    
public:
    void start() {
        start_time = high_resolution_clock::now();
    }
    
    double elapsed_ms() {
        auto end_time = high_resolution_clock::now();
        auto duration = duration_cast<microseconds>(end_time - start_time);
        return duration.count() / 1000.0; // Convert to milliseconds
    }
};

// Test log function
void logfunc(const char *s) {
    // Minimal logging for performance testing
    if (strstr(s, "ERROR") || strstr(s, "FAIL")) {
        printf("[LOG] %s\n", s);
    }
}

// Performance test functions
double test_connection_performance(const char* tns, const char* user, const char* password, int iterations) {
    printf("Testing connection performance (%d iterations)...\n", iterations);
    
    PerformanceTimer timer;
    timer.start();
    
    for (int i = 0; i < iterations; i++) {
        ORAPP::Connection db;
        
        if (!db.connect(tns, user, password)) {
            printf("Connection failed at iteration %d: %s\n", i, db.error().c_str());
            return -1;
        }
        
        if (!db.disconnect()) {
            printf("Disconnect failed at iteration %d: %s\n", i, db.error().c_str());
            return -1;
        }
    }
    
    double elapsed = timer.elapsed_ms();
    printf("Connection test completed: %.2f ms total, %.2f ms per connection\n", 
           elapsed, elapsed / iterations);
    
    return elapsed / iterations;
}

double test_query_performance(ORAPP::Connection& db, int iterations) {
    printf("Testing query performance (%d iterations)...\n", iterations);
    
    PerformanceTimer timer;
    timer.start();
    
    for (int i = 0; i < iterations; i++) {
        ORAPP::Query* query = db.query();
        if (!query) {
            printf("Failed to create query at iteration %d\n", i);
            return -1;
        }
        
        query->clear();
        *query << "SELECT " << i << " as ITERATION, SYSDATE as CURRENT_TIME FROM DUAL";
        
        if (!query->execute()) {
            printf("Query failed at iteration %d: %s\n", i, query->error().c_str());
            return -1;
        }
        
        if (query->next()) {
            ORAPP::Row& row = query->row();
            // Access the data to ensure it's actually fetched
            string iteration = row[0].value();
            string current_time = row[1].value();
            
            // Verify data occasionally
            if (i % 100 == 0) {
                printf("Iteration %d: %s, %s\n", i, iteration.c_str(), current_time.c_str());
            }
        }
    }
    
    double elapsed = timer.elapsed_ms();
    printf("Query test completed: %.2f ms total, %.2f ms per query\n", 
           elapsed, elapsed / iterations);
    
    return elapsed / iterations;
}

double test_multiple_row_performance(ORAPP::Connection& db, int row_count, int iterations) {
    printf("Testing multiple row fetch performance (%d rows x %d iterations)...\n", 
           row_count, iterations);
    
    PerformanceTimer timer;
    timer.start();
    
    int total_rows_fetched = 0;
    
    for (int i = 0; i < iterations; i++) {
        ORAPP::Query* query = db.query();
        if (!query) {
            printf("Failed to create query at iteration %d\n", i);
            return -1;
        }
        
        query->clear();
        *query << "SELECT LEVEL as ROW_NUM, 'Data_' || LEVEL as DATA_VALUE, SYSDATE as TIMESTAMP ";
        *query << "FROM DUAL CONNECT BY LEVEL <= " << row_count;
        
        if (!query->execute()) {
            printf("Query failed at iteration %d: %s\n", i, query->error().c_str());
            return -1;
        }
        
        int rows_in_iteration = 0;
        while (query->next()) {
            ORAPP::Row& row = query->row();
            // Access all columns to ensure data is fetched
            string row_num = row[0].value();
            string data_value = row[1].value();
            string timestamp = row[2].value();
            
            rows_in_iteration++;
            total_rows_fetched++;
        }
        
        if (rows_in_iteration != row_count) {
            printf("Warning: Expected %d rows, got %d at iteration %d\n", 
                   row_count, rows_in_iteration, i);
        }
    }
    
    double elapsed = timer.elapsed_ms();
    printf("Multiple row test completed: %.2f ms total, %.2f ms per iteration, %.2f ms per row\n", 
           elapsed, elapsed / iterations, elapsed / total_rows_fetched);
    
    return elapsed / iterations;
}

double test_transaction_performance(ORAPP::Connection& db, int iterations) {
    printf("Testing transaction performance (%d iterations)...\n", iterations);
    
    PerformanceTimer timer;
    timer.start();
    
    for (int i = 0; i < iterations; i++) {
        // Test commit
        if (!db.commit()) {
            printf("Commit failed at iteration %d: %s\n", i, db.error().c_str());
            return -1;
        }
        
        // Test rollback (though there's nothing to rollback)
        if (!db.rollback()) {
            printf("Rollback failed at iteration %d: %s\n", i, db.error().c_str());
            return -1;
        }
    }
    
    double elapsed = timer.elapsed_ms();
    printf("Transaction test completed: %.2f ms total, %.2f ms per transaction\n", 
           elapsed, elapsed / iterations);
    
    return elapsed / iterations;
}

// Main performance test
int main(int argc, char* argv[]) {
    if (argc != 4) {
        printf("Usage: %s <tns> <user> <password>\n", argv[0]);
        printf("Example: %s localhost:1521/XE testuser testpass\n", argv[0]);
        return 1;
    }
    
    const char* tns = argv[1];
    const char* user = argv[2];
    const char* password = argv[3];
    
    printf("=== ODPI-C Performance Comparison Test ===\n");
    printf("Database: %s@%s\n", user, tns);
    printf("Testing ODPI-C wrapper performance...\n\n");
    
    // Set up minimal logging
    ORAPP::log_to(logfunc);
    
    // Test 1: Connection Performance
    double conn_perf = test_connection_performance(tns, user, password, 10);
    if (conn_perf < 0) {
        printf("Connection performance test failed\n");
        return 1;
    }
    
    // Establish connection for remaining tests
    ORAPP::Connection db;
    if (!db.connect(tns, user, password)) {
        printf("Failed to establish connection for remaining tests: %s\n", db.error().c_str());
        return 1;
    }
    
    printf("\nConnected successfully. Database version: %s\n\n", db.version().c_str());
    
    // Test 2: Query Performance
    double query_perf = test_query_performance(db, 1000);
    if (query_perf < 0) {
        printf("Query performance test failed\n");
        return 1;
    }
    
    // Test 3: Multiple Row Performance
    double multi_row_perf = test_multiple_row_performance(db, 100, 50);
    if (multi_row_perf < 0) {
        printf("Multiple row performance test failed\n");
        return 1;
    }
    
    // Test 4: Transaction Performance
    double trans_perf = test_transaction_performance(db, 100);
    if (trans_perf < 0) {
        printf("Transaction performance test failed\n");
        return 1;
    }
    
    // Summary
    printf("\n=== Performance Summary (ODPI-C) ===\n");
    printf("Connection (per operation):    %.2f ms\n", conn_perf);
    printf("Query (per operation):         %.2f ms\n", query_perf);
    printf("Multi-row fetch (per batch):   %.2f ms\n", multi_row_perf);
    printf("Transaction (per operation):   %.2f ms\n", trans_perf);
    
    // Disconnect
    if (!db.disconnect()) {
        printf("Disconnect failed: %s\n", db.error().c_str());
        return 1;
    }
    
    printf("\n=== Performance test completed successfully ===\n");
    return 0;
}
