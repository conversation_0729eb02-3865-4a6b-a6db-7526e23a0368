#include "../../libsrc/odpi/inc/odpi_wrapper.hh"
#include <iostream>

using namespace std;

void logfunc(const char *s) {
    // Minimal logging
}

int main(int argc, char* argv[]) {
    if (argc != 4) {
        printf("Usage: %s <tns> <user> <password>\n", argv[0]);
        return 1;
    }
    
    const char* tns = argv[1];
    const char* user = argv[2];
    const char* password = argv[3];
    
    printf("=== Checking Real Users ===\n");
    
    ORAPP::log_to(logfunc);
    ORAPP::Connection db;
    
    if (!db.connect(tns, user, password)) {
        printf("Connection failed: %s\n", db.error().c_str());
        return 1;
    }
    
    // Check if talktest1 exists with correct column names
    printf("=== Checking talktest1 in TBL_USER (correct columns) ===\n");
    ORAPP::Query* query1 = db.query();
    *query1 << "SELECT COUNT(*) as USER_COUNT FROM TBL_USER WHERE UPPER(C_ID_VCH) = 'TALKTEST1'";
    
    if (query1->execute() && query1->next()) {
        ORAPP::Row& row = query1->row();
        printf("talktest1 found in TBL_USER: %s records\n", row[0].value().c_str());
    }
    
    // Show some existing users
    printf("\n=== Sample users in TBL_USER ===\n");
    ORAPP::Query* query2 = db.query();
    *query2 << "SELECT C_ID_VCH, C_PASSWORD_VCH, C_NAME_VCH FROM TBL_USER WHERE ROWNUM <= 5";
    
    if (query2->execute()) {
        printf("Sample users:\n");
        while (query2->next()) {
            ORAPP::Row& row = query2->row();
            printf("  ID: %s, PWD: %s, NAME: %s\n", 
                   row[0].value().c_str(), row[1].value().c_str(), row[2].value().c_str());
        }
    }
    
    // Test with an existing user
    printf("\n=== Testing with first existing user ===\n");
    ORAPP::Query* query3 = db.query();
    *query3 << "SELECT C_ID_VCH, C_PASSWORD_VCH FROM TBL_USER WHERE ROWNUM = 1";
    
    string test_id, test_pwd;
    if (query3->execute() && query3->next()) {
        ORAPP::Row& row = query3->row();
        test_id = row[0].value();
        test_pwd = row[1].value();
        printf("Testing with user: %s / %s\n", test_id.c_str(), test_pwd.c_str());
        
        // Test procedure with existing user
        ORAPP::Query* query4 = db.query();
        *query4 << "DECLARE "
                << "  v_rst NUMBER := -999; "
                << "  v_rstmsg VARCHAR2(512); "
                << "  v_appname VARCHAR2(256); "
                << "  v_sip VARCHAR2(64); "
                << "  v_pid NUMBER; "
                << "  v_job NUMBER; "
                << "  v_c_job NUMBER; "
                << "  v_prt NUMBER; "
                << "  v_cnt NUMBER; "
                << "  v_rpt_cnt NUMBER; "
                << "  v_server_info VARCHAR2(512); "
                << "  v_rpt_sleep_cnt NUMBER := 3; "
                << "  v_sender_proc VARCHAR2(256); "
                << "  v_report_proc VARCHAR2(256); "
                << "  v_senderdb_info VARCHAR2(512); "
                << "  v_reportdb_info VARCHAR2(512); "
                << "  v_logfile_info VARCHAR2(512); "
                << "  v_etc VARCHAR2(512); "
                << "BEGIN "
                << "  proc_check_mms_login_ext2("
                << "    in_cid=>'" << test_id << "',"
                << "    in_pwd=>'" << test_pwd << "',"
                << "    ot_appname=>v_appname,"
                << "    ot_sip=>v_sip,"
                << "    ot_pid=>v_pid,"
                << "    ot_job=>v_job,"
                << "    ot_c_job=>v_c_job,"
                << "    ot_prt=>v_prt,"
                << "    ot_cnt=>v_cnt,"
                << "    ot_rpt_cnt=>v_rpt_cnt,"
                << "    ot_server_info=>v_server_info,"
                << "    ot_rpt_sleep_cnt=>v_rpt_sleep_cnt,"
                << "    ot_sender_proc=>v_sender_proc,"
                << "    ot_report_proc=>v_report_proc,"
                << "    ot_senderdb_info=>v_senderdb_info,"
                << "    ot_reportdb_info=>v_reportdb_info,"
                << "    ot_logfile_info=>v_logfile_info,"
                << "    ot_etc=>v_etc,"
                << "    ot_rst=>v_rst,"
                << "    ot_rstmsg=>v_rstmsg"
                << "  ); "
                << "  IF v_rst != -999 THEN "
                << "    RAISE_APPLICATION_ERROR(-20002, 'SUCCESS: RST=' || v_rst || ', MSG=' || NVL(v_rstmsg, 'NULL')); "
                << "  ELSE "
                << "    RAISE_APPLICATION_ERROR(-20003, 'FAILED: RST still -999, MSG=' || NVL(v_rstmsg, 'NULL')); "
                << "  END IF; "
                << "END;";
        
        if (query4->execute()) {
            printf("Existing user test: Unexpected success\n");
        } else {
            string error = query4->error();
            if (error.find("SUCCESS:") != string::npos) {
                printf("Existing user test: %s\n", error.c_str());
            } else {
                printf("Existing user test failed: %s\n", error.c_str());
            }
        }
    }
    
    db.disconnect();
    return 0;
}
