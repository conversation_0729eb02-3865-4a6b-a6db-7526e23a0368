#include "../../libsrc/odpi/inc/odpi_wrapper.hh"
#include <iostream>

using namespace std;

void logfunc(const char *s) {
    printf("[PARAM-CHECK] %s\n", s);
}

int main(int argc, char* argv[]) {
    if (argc != 4) {
        printf("Usage: %s <tns> <user> <password>\n", argv[0]);
        return 1;
    }
    
    const char* tns = argv[1];
    const char* user = argv[2];
    const char* password = argv[3];
    
    printf("=== Checking Parameter Sizes ===\n");
    
    ORAPP::log_to(logfunc);
    ORAPP::Connection db;
    
    if (!db.connect(tns, user, password)) {
        printf("Connection failed: %s\n", db.error().c_str());
        return 1;
    }
    
    printf("Connected successfully!\n");
    
    // Get detailed parameter information
    ORAPP::Query* query = db.query();
    *query << "SELECT "
           << "  argument_name, "
           << "  data_type, "
           << "  data_length, "
           << "  data_precision, "
           << "  data_scale, "
           << "  in_out, "
           << "  position "
           << "FROM user_arguments "
           << "WHERE object_name = 'PROC_CHECK_MMS_LOGIN_EXT2' "
           << "ORDER BY position";
    
    if (!query->execute()) {
        printf("Query failed: %s\n", query->error().c_str());
        return 1;
    }
    
    printf("\nParameter details:\n");
    printf("%-20s %-12s %-6s %-9s %-5s %-6s %-3s\n", 
           "NAME", "TYPE", "LENGTH", "PRECISION", "SCALE", "IN_OUT", "POS");
    printf("%-20s %-12s %-6s %-9s %-5s %-6s %-3s\n", 
           "--------------------", "------------", "------", "---------", "-----", "------", "---");
    
    while (query->next()) {
        ORAPP::Row& row = query->row();
        printf("%-20s %-12s %-6s %-9s %-5s %-6s %-3s\n",
               row[0].value().c_str(),  // argument_name
               row[1].value().c_str(),  // data_type
               row[2].value().c_str(),  // data_length
               row[3].value().c_str(),  // data_precision
               row[4].value().c_str(),  // data_scale
               row[5].value().c_str(),  // in_out
               row[6].value().c_str()); // position
    }
    
    // Check if there are any constraints on the parameters
    printf("\n=== Checking for constraints ===\n");
    query->clear();
    *query << "SELECT "
           << "  p.object_name, "
           << "  p.procedure_name, "
           << "  p.argument_name, "
           << "  p.data_type, "
           << "  p.data_length "
           << "FROM user_procedures pr, user_arguments p "
           << "WHERE pr.object_name = p.object_name "
           << "AND pr.object_name = 'PROC_CHECK_MMS_LOGIN_EXT2' "
           << "ORDER BY p.position";
    
    if (query->execute()) {
        printf("Procedure constraints:\n");
        while (query->next()) {
            ORAPP::Row& row = query->row();
            printf("  %s.%s: %s(%s)\n",
                   row[1].value().c_str(),  // procedure_name
                   row[2].value().c_str(),  // argument_name
                   row[3].value().c_str(),  // data_type
                   row[4].value().c_str()); // data_length
        }
    }
    
    // Test with smaller buffer sizes
    printf("\n=== Testing with appropriate sizes ===\n");
    query->clear();
    *query << "DECLARE "
           << "  v_appname VARCHAR2(256); "      // Reduced from 32767
           << "  v_sip VARCHAR2(64); "           // Reduced from 32767
           << "  v_pid NUMBER; "
           << "  v_job NUMBER; "
           << "  v_c_job NUMBER; "
           << "  v_prt NUMBER; "
           << "  v_cnt NUMBER; "
           << "  v_rpt_cnt NUMBER; "
           << "  v_server_info VARCHAR2(512); "  // Reduced from 32767
           << "  v_rpt_sleep_cnt NUMBER := 3; "
           << "  v_sender_proc VARCHAR2(256); "  // Reduced from 32767
           << "  v_report_proc VARCHAR2(256); "  // Reduced from 32767
           << "  v_senderdb_info VARCHAR2(512); " // Reduced from 32767
           << "  v_reportdb_info VARCHAR2(512); " // Reduced from 32767
           << "  v_logfile_info VARCHAR2(512); "  // Reduced from 32767
           << "  v_etc VARCHAR2(512); "          // Reduced from 32767
           << "  v_rst NUMBER := -999; "
           << "  v_rstmsg VARCHAR2(512); "       // Reduced from 32767
           << "BEGIN "
           << "  proc_check_mms_login_ext2("
           << "    in_cid=>'talktest1',"
           << "    in_pwd=>'talktest1',"
           << "    ot_appname=>v_appname,"
           << "    ot_sip=>v_sip,"
           << "    ot_pid=>v_pid,"
           << "    ot_job=>v_job,"
           << "    ot_c_job=>v_c_job,"
           << "    ot_prt=>v_prt,"
           << "    ot_cnt=>v_cnt,"
           << "    ot_rpt_cnt=>v_rpt_cnt,"
           << "    ot_server_info=>v_server_info,"
           << "    ot_rpt_sleep_cnt=>v_rpt_sleep_cnt,"
           << "    ot_sender_proc=>v_sender_proc,"
           << "    ot_report_proc=>v_report_proc,"
           << "    ot_senderdb_info=>v_senderdb_info,"
           << "    ot_reportdb_info=>v_reportdb_info,"
           << "    ot_logfile_info=>v_logfile_info,"
           << "    ot_etc=>v_etc,"
           << "    ot_rst=>v_rst,"
           << "    ot_rstmsg=>v_rstmsg"
           << "  ); "
           << "  DBMS_OUTPUT.PUT_LINE('RST: ' || v_rst || ', MSG: ' || v_rstmsg); "
           << "END;";
    
    if (query->execute()) {
        printf("Test with appropriate sizes: SUCCESS\n");
    } else {
        printf("Test with appropriate sizes failed: %s\n", query->error().c_str());
    }
    
    db.disconnect();
    return 0;
}
