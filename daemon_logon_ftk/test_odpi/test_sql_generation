ELF          >    p@     @       �F          @ 8 
 @ !         @       @ @     @ @     �      �                         @     @                                          @       @     8      8                           @      @     �      �                             @       @     �
      �
                   �-      �=@     �=@           (                   �-      �=@     �=@                              8      8@     8@                                  X      X@     X@     D       D              S�td   8      8@     8@                            P�td   �&      �&@     �&@     �       �              Q�td                                                  R�td   �-      �=@     �=@     H      H             /lib64/ld-linux-x86-64.so.2              GNU � �                   GNU a��:�sя!�HN��恁�|b3         GNU                               �@0  	         뷩�|CE礪�qXxIk�)E�L                        �                     G                     N                                            �                      �                     4                                            >                     E                     -                     �                                          '                     ,                     k                     F                      �                      �                     �                                           ,                       9                                          �    �@@             �    �@@             �    �@@             t     �@                  �@              __gmon_start__ _ITM_deregisterTMCloneTable _ITM_registerTMCloneTable _ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE7compareEPKc _ZNSolsEi _ZNSt7__cxx1119basic_ostringstreamIcSt11char_traitsIcESaIcEEC1Ev _ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE6substrEmm _ZNSt8ios_base4InitD1Ev _ZdlPv _ZNKSt7__cxx1115basic_stringbufIcSt11char_traitsIcESaIcEE3strEv __gxx_personality_v0 _ZSt16__ostream_insertIcSt11char_traitsIcEERSt13basic_ostreamIT_T0_ES6_PKS3_l _ZStlsISt11char_traitsIcEERSt13basic_ostreamIcT_ES5_PKc _ZNSt8ios_base4InitC1Ev _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE9_M_createERmm _ZNSt7__cxx1119basic_ostringstreamIcSt11char_traitsIcESaIcEED1Ev _ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE4findEPKcmm _ZSt19__throw_logic_errorPKc _Unwind_Resume __libc_start_main __cxa_atexit puts strlen snprintf memcpy libclntsh.so.21.1 libstdc++.so.6 libm.so.6 libgcc_s.so.1 libc.so.6 _edata __bss_start _end GCC_3.0 GLIBC_2.34 GLIBC_2.14 GLIBC_2.2.5 GLIBCXX_3.4.9 GLIBCXX_3.4.26 CXXABI_1.3 GLIBCXX_3.4.21 GLIBCXX_3.4                        	      
              �         P&y   �        �     @   ��  
 �     ���   �     ui	   �        g         �)  	 �     v�   �     簞k   �     q�        t)�         �?@                   �?@                   �?@                   �?@                   @@                    @@                   (@@                   0@@                   8@@                   @@@                   H@@        	           P@@        
           X@@                   `@@                   h@@                   p@@        
           x@@                   �@@                   �@@                   �@@                   �@@                   �@@                   �@@                   �@@                   �@@                   �@@                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ��H��H��/  H��t��H���     �5�/  �%�/  @ �%�/  h    禹����%�/  h   優����%�/  h   湧����%�/  h   欲����%�/  h   �����%�/  h   �����%�/  h   �����%�/  h   �p����%�/  h   �`����%�/  h	   �P����%�/  h
   �@����%�/  h   �0����%�/  h   � ����%z/  h
   �����%r/  h   � ����%j/  h   昱����%b/  h   禹����%Z/  h   優����%R/  h   湧����%J/  h   欲����%B/  h   �����%:/  h   ����H�|$`H9�t����H�|$@H�D$PH9�t瑤���H�|$ L9�t姚���H�<$L9�t歪���H��$�   �C���H�省畵��f.�     �AV�@ @ AUATUSH��   �w����`@ L�l$曜  H��$�   �����"@ H��L�,$�v"@ �5  L�t$0�"@ �v"@ H�|$ L�t$ �  �"@ H��$�   �U���H�퓸�"@ �H���H�퓸�"@ �;���H�퓸�"@ �.���H�퓸�"@ �!���H�퓸�"@ ����H�퓸�"@ ����H�퓸#@ 橈��H�퓸#@ 妖��H�퓸` @ 歪��H�퓸(#@ 腕��H�퓸   �f��H�퓸�$@ 癰��H�퓸� @ 玉��H�퓸� @ ���H�퓸� @ ���H�퓸� @ ���H�퓸!@ �x��H�퓸E#@ �k��H�퓸]#@ �^��H�퓸��尿��H�퓸�$@ �D��H�퓸p#@ �7��H�퓸�#@ �*��H�퓸�#@ ���H�퓸�#@ ���H�T$H�4$H�험���H�퓸�#@ 嶢��H�퓸�#@ 猥��H�T$(H�t$ H�험c��H�퓸�#@ 婉��H�퓸�#@ 癰��H�퓸�#@ 玉��H�퓸�#@ ���H�퓸$@ ���H�퓸"$@ ���H�퓸9$@ �x��H�퓸L$@ �k��H�퓸_$@ �^��H�퓸8!@ �Q��H�퓸`!@ �D��H�퓸�!@ �7��H�퓸�!@ �*��H�퓸�!@ ���H�퓸 "@ ���H�퓸("@ ���H�퓸z$@ 擾��H�퓸�$@ 僥��H�퓸�$@ 汪��H�퓸�$@ 碗��H�퓸�%@ 臥��H��$�   H�|$@�p��H�t$H�P"@ 1잭��H�t$@옛$@ 1잭��왐$@ �D��1�1欌fD  H��H�P�   양$@ H�|$@���H��u�1�1罰H��H�P�   억$@ H�|$@巍��H��u�1�E1秧�    I��H�P�   �%@ H�|$@兀��H��u�H�楮�$@ 1잭o��H�乍	%@ 1잭`��L�嚥%@ 1잭Q��H����H�����t
I����   �J%@ �k��H�D$HH�t$@H�|$`H프����H�P澮,��H�|$`�%@ H�l$p僚��H�|$`��H9�t�W����uZ�e%@ ���H�|$@H�D$PH9�t�5��H�|$ L9�t�&��H�<$L9�t���H��$�   �{��H��   1�[]A\A]A^첼z%@ 瓦��H�D$HH���H�t$@H�|$`H�P打���H�t$`�%@ 1잭R��H�|$`H9��k���瑥���a����,%@ �p��� ���H�췌뇌��H�췌k��H�췌���H�췌j��H�췌��� H��왐@@ �"��� @ 앗@@ 욹@ H��謠��f.�     ��1�I��^H��H�崖PTE1�1�H피�@ �c(  �f.�     �餉f.�     �H�=)  H�)  H9�tH�(  H��t	���    ��    H�=�(  H�5�(  H)�H��H즈?H진H�H拉tH��'  H��t��fD  ��    ��=�(   uUH�堰z�����(  ]��ff.�     @ �穴�f.�     �ff.�      ATI��UH��SH��H��H��tH��tnI)�L�d$I��w2H�H��I��u�E �H�H�D$H�C� H��[]A\�M��t言H��1�H�t$�&��H�H��H�D$H�CL��H�迪\��H�逾� @ �-��f.�      H�=�'  ��     H��'  ��     H�=�'   t>H��t9UH��H�g  �   1�H��   H��H�炡=��H��l'  H��   ]� ��    H�=P'   t>H��t9UH��H�&  �   1�H��   H��H�炡莊��H��'  H��   ]� ��    H�= '   t>H��t9UH��H��  �   1�H��   H��H�炡���H���&  H��   ]� ��    H�=�&   tFH��H��t>UH��   H��H��t6I��H��  H��1읍   �E��H��t&  H��   ]� ��    H�t  �   H��1잭��贇H�=@&   tFH��H��t>UH��   H��H��t6I��H�F  H��1읍   豌��H��&  H��   ]� ��    H�,  �   H��1잭���贇H�=�%   tFH��tAH��t<UI��H��H�
  �   1�H��   H��H�炡e��H���%  H��   ]� �ff.�     @ H�=p%   tFH��H��t>UH��   H��H��t6I��A��H��
  H�絶   1잭��H��1%  H��   ]쳉�    A��H��
  H��1읍   碗��贇ff.�     f�H�=�$   tH����H�����tH��u�UI��I��H��H�,
  �   1�H��   H��H�炡v��H���$  H��   ]�@ H�=�$   tfATI��USH��   H��t=H��H��t5�&��H��I��L��H=�  w=H��	  �   H��1잭��H��>$  H��   []A\�f.�     ��    H��	  �   H��1잭凌��陸��H��H���                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 basic_string::_M_construct null not valid       === Testing SQL Generation ===    v_server_info VARCHAR2(512);    v_sender_proc VARCHAR2(256);    v_report_proc VARCHAR2(256);    v_senderdb_info VARCHAR2(512);          v_reportdb_info VARCHAR2(512);          v_logfile_info VARCHAR2(512);             ot_server_info=>v_server_info,          ot_rpt_sleep_cnt=>v_rpt_sleep_cnt,      ot_sender_proc=>v_sender_proc,          ot_report_proc=>v_report_proc,          ot_senderdb_info=>v_senderdb_info,      ot_reportdb_info=>v_reportdb_info,      ot_logfile_info=>v_logfile_info,    Generated SQL length: %zu characters
 talktest1 DECLARE    v_appname VARCHAR2(256);    v_sip VARCHAR2(64);    v_pid NUMBER;    v_job NUMBER;    v_c_job NUMBER;    v_prt NUMBER;    v_cnt NUMBER;    v_rpt_cnt NUMBER;    v_rpt_sleep_cnt NUMBER :=    v_etc VARCHAR2(512);    v_rst NUMBER :=    v_rstmsg VARCHAR2(512);  BEGIN    proc_check_mms_login_ext2(     in_cid=>' ',     in_pwd=>'     ot_appname=>v_appname,     ot_sip=>v_sip,     ot_pid=>v_pid,     ot_job=>v_job,     ot_c_job=>v_c_job,     ot_prt=>v_prt,     ot_cnt=>v_cnt,     ot_rpt_cnt=>v_rpt_cnt,     ot_etc=>v_etc,     ot_rst=>v_rst,     ot_rstmsg=>v_rstmsg   );  Generated SQL:
%s
 
=== Syntax Check === DECLARE BEGIN DECLARE count: %zu
 BEGIN count: %zu
 END; count: %zu
 ? SQL structure looks correct ? SQL structure has issues ? SQL ends with END; ? SQL does not end with END; Last 10 characters: '%s'
 [ODPI-INFO] %s [ODPI-ERROR] %s [ODPI-DEBUG] %s [ODPI-STEP] %s: %s [ODPI-STEP] %s [ODPI-MEMORY] %s: ptr=%p [ODPI-MEMORY] %s [ODPI-OP] %s: %s [ODPI-CONN] %s@%s: %s [ODPI-QUERY] %.500s... : %s [ODPI-QUERY] %s : %s     [ODPI-ERROR] %s failed: code=%d, message=%s     [ODPI-ERROR] %s failed: code=%d ;�      H���   멸���  ��x  h���  ����   흩���   ���  ���$  8��   H��  X��(  ⑫��L  剽��p  H���  몬���  (���  ���  ��0  h��P             zR x�        器��&    D   0   逸��       $   D   X��p   FJw� ?;*3$"       l   p��       0   �   l���    B�D�D �G0~
 AABA         zPLR x�@ �  H   $   ���M  l*@ B�G�B �A(�A0�G��
0C(A BBBA       p   麟��U   �*@ ������     D  x��&    D]    \  0��          p  ,��           �  (��I    P�X�[AD�      �  T��I    P�X�[AD�      �  ���I    P�X�[AD�  $   �  ю��p    S�G�qAD�H��$     替��p    S�G�qAD�H��    @  <��Q    U�[�[AD�  $   d  x��s    S�G�tAA�H��   �  基��\    a�^�[A<   �  ���    L�D�A �G�I AABK쳤�H����     ��'!  F�
 d�
 v��
 ���
 �	�
 �
�
 ��P                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           P@     @@      @            U             g             v             �             �              @     
       �@            �=@                          �=@                   萃�o    �@            �@            �@     
                                                  @@                                       (@            �@            `       	              ���o    @     ���o           ��o    �
@                                                                                                                                     �=@                     6@     F@     V@     f@     v@     �@     �@     �@     �@     �@     �@     �@     �@     @     @     &@     6@     F@     V@     f@     v@     �@         GCC: (GNU) 11.5.0 20240719 (Red Hat 11.5.0-5) AV:4p1292 RV:running gcc 11.5.0 20240719 BV:annobin gcc 11.5.0 20240719 GW:0x3d2056a ../sysdeps/x86/abi-note.c SP:3 SC:1 CF:8 ../sysdeps/x86/abi-note.c FL:-1 ../sysdeps/x86/abi-note.c GA:1 PI:3 SE:0 iS:0 GW:0x3d2056a init.c CF:8 init.c FL:-1 init.c GW:0x3d2056a static-reloc.c CF:8 static-reloc.c FL:-1 static-reloc.c          GA$3a1 p@     �@              GA$3a1 �@     �@              GA$3a1  @     @              GA$3a1 �@     �@              GA$3a1 �@     V@              GA$3a1 �@     �@              GA$3a1 �@     �@              GA$3a1 @     @              GA$3a1 �@     �@                                       @                   8@                   X@                   |@                   �@                   �@                   �@                   �
@                  	 @                  
 �@                   (@                    @                  
  @                   �@                   �@                     @                   �&@                   �'@                   l*@                   �=@                   �=@                   �=@                   �?@                    @@                   �@@                   �@@                                                               �`@                 �                >     |@             H    �                `     p@     �       �     �@     U       �     @@     &       �     �@@               �                    �@                 �@             %     @             ;    �@@            G    �=@             n    P@             z    �=@             �   �                �    �@@               �                �    h*@                  �                �     @@             �    �=@             �     �&@             �    �@     M      �                     '    �@     I       �                     ?                     Q   �@@             �    p@     &       ]                      y   �@                 �@@             �    �@@             �                     �    �@     p       �                          �@@             %                     t                      �                     �    0@     I       �                     �    @            �                     �                     6     �@             V    �@@             ]                     t     �@             �                     �    �@     \            @     Q       9    @@     �       Z    `@     s       |    @             �                     �                     �   �@                                 U                     �                     �     �@@                                  Q                     u     @            �                      �    @             �                     �     `@            �    �@     I       �      @            �                          @     p        /usr/lib/gcc/x86_64-redhat-linux/11/../../../../lib64/crt1.o __abi_tag test_sql_generation.cpp _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_M_constructIPKcEEvT_S8_St20forward_iterator_tag.isra.0 main.cold _GLOBAL__sub_I__Z7logfuncPKc _ZStL8__ioinit crtstuff.c deregister_tm_clones __do_global_dtors_aux completed.0 __do_global_dtors_aux_fini_array_entry frame_dummy __frame_dummy_init_array_entry log.cpp _ZN5ORAPPL10g_log_funcE __FRAME_END__ _GLOBAL_OFFSET_TABLE_ _DYNAMIC __GNU_EH_FRAME_HDR main _ZSt19__throw_logic_errorPKc@GLIBCXX_3.4 _ZN5ORAPP9log_debugEPKc memcpy@GLIBC_2.14 __TMC_END__ _ITM_deregisterTMCloneTable _fini __data_start __bss_start _ZNSolsEi@GLIBCXX_3.4 _ZN5ORAPP15log_memory_infoEPKcPv _ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE4findEPKcmm@GLIBCXX_3.4.21 _end _ZNKSt7__cxx1115basic_stringbufIcSt11char_traitsIcESaIcEE3strEv@GLIBCXX_3.4.21 __gmon_start__ strlen@GLIBC_2.2.5 _ZN5ORAPP11log_messageEPKc snprintf@GLIBC_2.2.5 _ZN5ORAPP6log_toEPFvPKcE _ZdlPv@GLIBCXX_3.4 _ZStlsISt11char_traitsIcEERSt13basic_ostreamIcT_ES5_PKc@GLIBCXX_3.4 __gxx_personality_v0@CXXABI_1.3 _edata _Unwind_Resume@GCC_3.0 _ZNSt8ios_base4InitD1Ev@GLIBCXX_3.4 _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE9_M_createERmm@GLIBCXX_3.4.21 _ZN5ORAPP19log_connection_infoEPKcS1_S1_ _ZN5ORAPP18log_odpi_operationEPKcS1_ _ZN5ORAPP14log_query_infoEPKcS1_ _ZN5ORAPP14log_odpi_errorEPKciS1_ _init __cxa_atexit@GLIBC_2.2.5 _ZNSt7__cxx1119basic_ostringstreamIcSt11char_traitsIcESaIcEED1Ev@GLIBCXX_3.4.21 _dl_relocate_static_pie _ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE7compareEPKc@GLIBCXX_3.4.21 _ZNSt7__cxx1119basic_ostringstreamIcSt11char_traitsIcESaIcEEC1Ev@GLIBCXX_3.4.26 _ZSt16__ostream_insertIcSt11char_traitsIcEERSt13basic_ostreamIT_T0_ES6_PKS3_l@GLIBCXX_3.4.9 _ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE6substrEmm@GLIBCXX_3.4.21 _ZNSt8ios_base4InitC1Ev@GLIBCXX_3.4 _ZN5ORAPP12get_log_funcEv _ITM_registerTMCloneTable __dso_handle puts@GLIBC_2.2.5 _ZN5ORAPP9log_errorEPKc _IO_stdin_used __libc_start_main@GLIBC_2.34 _ZN5ORAPP14log_debug_stepEPKcS1_  .symtab .strtab .shstrtab .interp .note.gnu.property .note.gnu.build-id .note.ABI-tag .gnu.hash .dynsym .dynstr .gnu.version .gnu.version_r .rela.dyn .rela.plt .init .text .fini .rodata .eh_frame_hdr .eh_frame .gcc_except_table .init_array .fini_array .dynamic .got .got.plt .data .bss .comment .annobin.notes .gnu.build.attributes                                                                                     @                                         #             8@     8                                     6             X@     X      $                              I             |@     |                                     W   ��o       �@     �      8                             a             �@     �      �                          i             �@     �                                   q   ���o       �
@     �
      <                            ~   ���o       @           �                            �             �@     �      `                            �      B       (@     (                                �              @                                          �              @            p                            �             �@     �      @                             �             �@     �      
                              �               @             �                             �             �&@     �&      �                              �             �'@     �'      �                             �             l*@     l*      3                              �             �=@     �-                                   �             �=@     �-                                   �             �=@     �-                                             �?@     �/                                                 @@      0      �                                         �@@     �0                                                �@@     �0                                         0               �0      .                             (     0               �0      >                            7             �`@     82      D                                                   �3      �	         5                 	                      @=      ,                                                   lE      M                             