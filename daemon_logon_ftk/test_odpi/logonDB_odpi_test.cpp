#include "logonDB_odpi.h"
#include <iostream>
#include <cstring>

// Test log function for ODPI-C wrapper
void logfunc(const char *s)
{
    // For debugging long SQL statements, write directly to file
    if (strstr(s, "Executing SQL") || strstr(s, "SQL prepared")) {
        FILE* debug_file = fopen("/tmp/logondb_odpi_debug.log", "a");
        if (debug_file) {
            time_t now = time(NULL);
            struct tm* tm_info = localtime(&now);
            char timestamp[64];
            strftime(timestamp, sizeof(timestamp), "%Y%m%d,%H:%M:%S", tm_info);
            fprintf(debug_file, "[%s] - Original length: %zu - [%s]\n", timestamp, strlen(s), s);
            fclose(debug_file);
        }
    }
    
    // Also print to stdout for immediate feedback
    printf("[ODPI-LOG] %s\n", s);
    fflush(stdout);
}

bool orapp_connect(ORAPP::Connection &db, const char *tns, const char *user, const char *pass) 
{
    if (!db.connect(tns, user, pass)) 
    {
        printf("[ERR] db connect failed - [%s]\n", db.error().c_str());
        return false;
    }
    printf("[INF] db connect success - ver [%s]\n", db.version().c_str());
    return true;
}

bool orapp_disconnect(ORAPP::Connection &db) {
    if (!db.disconnect()) 
    {
        printf("[ERR] db disconnect failed\n");
        return false;
    }
    printf("db disconnect success\n");
    return true;
}

int checkLogon(ORAPP::Connection &db, char* buff, int type)
{
    // Simplified version for testing
    printf("[INF] checkLogon called with type: %d\n", type);
    
    ORAPP::Query *query = db.query();
    if (!query) {
        printf("[ERR] Failed to create query\n");
        return -1;
    }
    
    // Test query - similar to what logonDB does
    *query << "SELECT 'TEST_USER' as USER_ID, 'ACTIVE' as STATUS FROM DUAL";
    
    if (!query->execute()) {
        printf("[ERR] Query execution failed: %s\n", query->error().c_str());
        return -1;
    }
    
    if (query->next()) {
        ORAPP::Row& row = query->row();
        printf("[INF] Query result: USER_ID=%s, STATUS=%s\n", 
               row[0].value().c_str(), row[1].value().c_str());
        return 0; // Success
    }
    
    return -2; // Not found
}

int loadCallback(ORAPP::Connection &db, char* buff, int type, set<string> &set_callback_list) 
{
    printf("[INF] loadCallback called with type: %d\n", type);
    
    ORAPP::Query *query = db.query();
    if (!query) {
        printf("[ERR] Failed to create query\n");
        return -1;
    }
    
    // Simulate the callback query from original logonDB
    int test_pid = 12345; // Test PID
    char qry[1024];
    sprintf(qry, "SELECT %d as PTN_ID, 'TEST_CALLBACK' as CALLBACK FROM DUAL", test_pid);
    
    *query << qry;
    
    printf("[INF] Executing: %s\n", qry);
    
    if (!query->execute()) {
        printf("[ERR] Query execution failed: %s\n", query->error().c_str());
        return -1;
    }
    
    int count = 0;
    while (query->next()) {
        ORAPP::Row& row = query->row();
        printf("[INF] Callback result: PTN_ID=%s, CALLBACK=%s\n", 
               row[0].value().c_str(), row[1].value().c_str());
        
        set_callback_list.insert(row[1].value());
        count++;
    }
    
    printf("[INF] Loaded %d callback entries\n", count);
    return 0;
}

// Test main function
int main(int argc, char* argv[]) {
    if (argc != 4) {
        printf("Usage: %s <tns> <user> <password>\n", argv[0]);
        return 1;
    }
    
    const char* tns = argv[1];
    const char* user = argv[2];
    const char* password = argv[3];
    
    printf("=== LogonDB ODPI-C Integration Test ===\n");
    printf("Connecting to: %s@%s\n", user, tns);
    
    // Set up logging
    ORAPP::log_to(logfunc);
    ORAPP::Connection db;
    
    // Test connection
    if (!orapp_connect(db, tns, user, password)) {
        printf("Connection test failed\n");
        return 1;
    }
    
    printf("\n=== Testing checkLogon function ===\n");
    char dummy_buff[256];
    int result = checkLogon(db, dummy_buff, 1);
    printf("checkLogon result: %d\n", result);
    
    printf("\n=== Testing loadCallback function ===\n");
    set<string> callback_list;
    result = loadCallback(db, dummy_buff, 1, callback_list);
    printf("loadCallback result: %d\n", result);
    printf("Callback list size: %zu\n", callback_list.size());
    
    // Print callback list
    for (const auto& callback : callback_list) {
        printf("Callback: %s\n", callback.c_str());
    }
    
    printf("\n=== Testing transaction operations ===\n");
    if (db.commit()) {
        printf("Commit successful\n");
    } else {
        printf("Commit failed: %s\n", db.error().c_str());
    }
    
    printf("\n=== Testing disconnect ===\n");
    if (orapp_disconnect(db)) {
        printf("Disconnect successful\n");
    } else {
        printf("Disconnect failed\n");
    }
    
    printf("\n=== Test completed ===\n");
    return 0;
}
