#include "../../libsrc/odpi/inc/odpi_wrapper.hh"
#include <iostream>

using namespace std;

void logfunc(const char *s) {
    printf("[DEBUG-PROC] %s\n", s);
}

int main(int argc, char* argv[]) {
    if (argc != 4) {
        printf("Usage: %s <tns> <user> <password>\n", argv[0]);
        return 1;
    }
    
    const char* tns = argv[1];
    const char* user = argv[2];
    const char* password = argv[3];
    
    printf("=== Debugging Procedure Results ===\n");
    
    ORAPP::log_to(logfunc);
    ORAPP::Connection db;
    
    if (!db.connect(tns, user, password)) {
        printf("Connection failed: %s\n", db.error().c_str());
        return 1;
    }
    
    printf("Connected successfully!\n");
    
    // Test 1: Check if user exists in database
    printf("\n=== Test 1: Check User Existence ===\n");
    ORAPP::Query* query1 = db.query();
    *query1 << "SELECT COUNT(*) as USER_COUNT FROM dual WHERE EXISTS ("
            << "  SELECT 1 FROM user_tables WHERE table_name LIKE '%USER%' OR table_name LIKE '%LOGIN%' OR table_name LIKE '%MEMBER%'"
            << ")";
    
    if (query1->execute() && query1->next()) {
        ORAPP::Row& row = query1->row();
        printf("User-related tables check: %s\n", row[0].value().c_str());
    }
    
    // Test 2: Check available tables
    printf("\n=== Test 2: Available Tables ===\n");
    ORAPP::Query* query2 = db.query();
    *query2 << "SELECT table_name FROM user_tables WHERE table_name LIKE '%USER%' OR table_name LIKE '%LOGIN%' OR table_name LIKE '%MEMBER%' ORDER BY table_name";
    
    if (query2->execute()) {
        printf("User-related tables:\n");
        while (query2->next()) {
            ORAPP::Row& row = query2->row();
            printf("  - %s\n", row[0].value().c_str());
        }
    }
    
    // Test 3: Call procedure with debug output
    printf("\n=== Test 3: Procedure Call with Debug ===\n");
    ORAPP::Query* query3 = db.query();
    *query3 << "DECLARE "
            << "  v_appname VARCHAR2(256); "
            << "  v_sip VARCHAR2(64); "
            << "  v_pid NUMBER; "
            << "  v_job NUMBER; "
            << "  v_c_job NUMBER; "
            << "  v_prt NUMBER; "
            << "  v_cnt NUMBER; "
            << "  v_rpt_cnt NUMBER; "
            << "  v_server_info VARCHAR2(512); "
            << "  v_rpt_sleep_cnt NUMBER := 3; "
            << "  v_sender_proc VARCHAR2(256); "
            << "  v_report_proc VARCHAR2(256); "
            << "  v_senderdb_info VARCHAR2(512); "
            << "  v_reportdb_info VARCHAR2(512); "
            << "  v_logfile_info VARCHAR2(512); "
            << "  v_etc VARCHAR2(512); "
            << "  v_rst NUMBER := -999; "
            << "  v_rstmsg VARCHAR2(512); "
            << "BEGIN "
            << "  -- Enable DBMS_OUTPUT"
            << "  DBMS_OUTPUT.ENABLE(1000000); "
            << "  "
            << "  -- Log before procedure call"
            << "  DBMS_OUTPUT.PUT_LINE('Before procedure call - v_rst: ' || v_rst); "
            << "  "
            << "  -- Call procedure"
            << "  proc_check_mms_login_ext2("
            << "    in_cid=>'talktest1',"
            << "    in_pwd=>'talktest1',"
            << "    ot_appname=>v_appname,"
            << "    ot_sip=>v_sip,"
            << "    ot_pid=>v_pid,"
            << "    ot_job=>v_job,"
            << "    ot_c_job=>v_c_job,"
            << "    ot_prt=>v_prt,"
            << "    ot_cnt=>v_cnt,"
            << "    ot_rpt_cnt=>v_rpt_cnt,"
            << "    ot_server_info=>v_server_info,"
            << "    ot_rpt_sleep_cnt=>v_rpt_sleep_cnt,"
            << "    ot_sender_proc=>v_sender_proc,"
            << "    ot_report_proc=>v_report_proc,"
            << "    ot_senderdb_info=>v_senderdb_info,"
            << "    ot_reportdb_info=>v_reportdb_info,"
            << "    ot_logfile_info=>v_logfile_info,"
            << "    ot_etc=>v_etc,"
            << "    ot_rst=>v_rst,"
            << "    ot_rstmsg=>v_rstmsg"
            << "  ); "
            << "  "
            << "  -- Log after procedure call"
            << "  DBMS_OUTPUT.PUT_LINE('After procedure call:'); "
            << "  DBMS_OUTPUT.PUT_LINE('  v_rst: ' || NVL(TO_CHAR(v_rst), 'NULL')); "
            << "  DBMS_OUTPUT.PUT_LINE('  v_rstmsg: ' || NVL(v_rstmsg, 'NULL')); "
            << "  DBMS_OUTPUT.PUT_LINE('  v_appname: ' || NVL(v_appname, 'NULL')); "
            << "  DBMS_OUTPUT.PUT_LINE('  v_sip: ' || NVL(v_sip, 'NULL')); "
            << "  DBMS_OUTPUT.PUT_LINE('  v_pid: ' || NVL(TO_CHAR(v_pid), 'NULL')); "
            << "  DBMS_OUTPUT.PUT_LINE('  v_job: ' || NVL(TO_CHAR(v_job), 'NULL')); "
            << "END;";
    
    if (query3->execute()) {
        printf("Procedure call with debug: SUCCESS\n");
    } else {
        printf("Procedure call with debug failed: %s\n", query3->error().c_str());
    }
    
    // Test 4: Try different user credentials
    printf("\n=== Test 4: Testing Different Scenarios ===\n");
    
    // Test with different user
    ORAPP::Query* query4 = db.query();
    *query4 << "DECLARE "
            << "  v_rst NUMBER := -999; "
            << "  v_rstmsg VARCHAR2(512); "
            << "  v_appname VARCHAR2(256); "
            << "  v_sip VARCHAR2(64); "
            << "  v_pid NUMBER; "
            << "  v_job NUMBER; "
            << "  v_c_job NUMBER; "
            << "  v_prt NUMBER; "
            << "  v_cnt NUMBER; "
            << "  v_rpt_cnt NUMBER; "
            << "  v_server_info VARCHAR2(512); "
            << "  v_rpt_sleep_cnt NUMBER := 3; "
            << "  v_sender_proc VARCHAR2(256); "
            << "  v_report_proc VARCHAR2(256); "
            << "  v_senderdb_info VARCHAR2(512); "
            << "  v_reportdb_info VARCHAR2(512); "
            << "  v_logfile_info VARCHAR2(512); "
            << "  v_etc VARCHAR2(512); "
            << "BEGIN "
            << "  -- Test with admin user"
            << "  proc_check_mms_login_ext2("
            << "    in_cid=>'admin',"
            << "    in_pwd=>'admin',"
            << "    ot_appname=>v_appname,"
            << "    ot_sip=>v_sip,"
            << "    ot_pid=>v_pid,"
            << "    ot_job=>v_job,"
            << "    ot_c_job=>v_c_job,"
            << "    ot_prt=>v_prt,"
            << "    ot_cnt=>v_cnt,"
            << "    ot_rpt_cnt=>v_rpt_cnt,"
            << "    ot_server_info=>v_server_info,"
            << "    ot_rpt_sleep_cnt=>v_rpt_sleep_cnt,"
            << "    ot_sender_proc=>v_sender_proc,"
            << "    ot_report_proc=>v_report_proc,"
            << "    ot_senderdb_info=>v_senderdb_info,"
            << "    ot_reportdb_info=>v_reportdb_info,"
            << "    ot_logfile_info=>v_logfile_info,"
            << "    ot_etc=>v_etc,"
            << "    ot_rst=>v_rst,"
            << "    ot_rstmsg=>v_rstmsg"
            << "  ); "
            << "  DBMS_OUTPUT.PUT_LINE('Admin test - RST: ' || v_rst || ', MSG: ' || NVL(v_rstmsg, 'NULL')); "
            << "END;";
    
    if (query4->execute()) {
        printf("Admin user test: SUCCESS\n");
    } else {
        printf("Admin user test failed: %s\n", query4->error().c_str());
    }
    
    db.disconnect();
    return 0;
}
