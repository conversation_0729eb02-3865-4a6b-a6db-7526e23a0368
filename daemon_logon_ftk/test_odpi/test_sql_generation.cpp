#include "../../libsrc/odpi/inc/odpi_wrapper.hh"
#include <iostream>
#include <sstream>

using namespace std;

void logfunc(const char *s) {
    // Minimal logging
}

int main() {
    printf("=== Testing SQL Generation ===\n");
    
    ORAPP::log_to(logfunc);
    
    // Simulate the same SQL generation as logonDB
    ostringstream sql_stream;
    
    int nRptNoDataSleep = 3;
    int nmRST = -999;
    string szCID = "talktest1";
    string szPWD = "talktest1";
    
    sql_stream << "DECLARE "
               << "  v_appname VARCHAR2(256); "
               << "  v_sip VARCHAR2(64); "
               << "  v_pid NUMBER; "
               << "  v_job NUMBER; "
               << "  v_c_job NUMBER; "
               << "  v_prt NUMBER; "
               << "  v_cnt NUMBER; "
               << "  v_rpt_cnt NUMBER; "
               << "  v_server_info VARCHAR2(512); "
               << "  v_rpt_sleep_cnt NUMBER := " << nRptNoDataSleep << "; "
               << "  v_sender_proc VARCHAR2(256); "
               << "  v_report_proc VARCHAR2(256); "
               << "  v_senderdb_info VARCHAR2(512); "
               << "  v_reportdb_info VARCHAR2(512); "
               << "  v_logfile_info VARCHAR2(512); "
               << "  v_etc VARCHAR2(512); "
               << "  v_rst NUMBER := " << nmRST << "; "
               << "  v_rstmsg VARCHAR2(512); "
               << "BEGIN "
               << "  proc_check_mms_login_ext2("
               << "    in_cid=>'" << szCID << "',"
               << "    in_pwd=>'" << szPWD << "',"
               << "    ot_appname=>v_appname,"
               << "    ot_sip=>v_sip,"
               << "    ot_pid=>v_pid,"
               << "    ot_job=>v_job,"
               << "    ot_c_job=>v_c_job,"
               << "    ot_prt=>v_prt,"
               << "    ot_cnt=>v_cnt,"
               << "    ot_rpt_cnt=>v_rpt_cnt,"
               << "    ot_server_info=>v_server_info,"
               << "    ot_rpt_sleep_cnt=>v_rpt_sleep_cnt,"
               << "    ot_sender_proc=>v_sender_proc,"
               << "    ot_report_proc=>v_report_proc,"
               << "    ot_senderdb_info=>v_senderdb_info,"
               << "    ot_reportdb_info=>v_reportdb_info,"
               << "    ot_logfile_info=>v_logfile_info,"
               << "    ot_etc=>v_etc,"
               << "    ot_rst=>v_rst,"
               << "    ot_rstmsg=>v_rstmsg"
               << "  ); "
               << "END;";
    
    string final_sql = sql_stream.str();
    
    printf("Generated SQL length: %zu characters\n", final_sql.length());
    printf("Generated SQL:\n%s\n", final_sql.c_str());
    
    // Check for syntax issues
    printf("\n=== Syntax Check ===\n");
    
    // Count DECLARE/BEGIN/END
    size_t declare_count = 0;
    size_t begin_count = 0;
    size_t end_count = 0;
    
    size_t pos = 0;
    while ((pos = final_sql.find("DECLARE", pos)) != string::npos) {
        declare_count++;
        pos += 7;
    }
    
    pos = 0;
    while ((pos = final_sql.find("BEGIN", pos)) != string::npos) {
        begin_count++;
        pos += 5;
    }
    
    pos = 0;
    while ((pos = final_sql.find("END;", pos)) != string::npos) {
        end_count++;
        pos += 4;
    }
    
    printf("DECLARE count: %zu\n", declare_count);
    printf("BEGIN count: %zu\n", begin_count);
    printf("END; count: %zu\n", end_count);
    
    if (declare_count == 1 && begin_count == 1 && end_count == 1) {
        printf("? SQL structure looks correct\n");
    } else {
        printf("? SQL structure has issues\n");
    }
    
    // Check if it ends properly
    if (final_sql.substr(final_sql.length() - 4) == "END;") {
        printf("? SQL ends with END;\n");
    } else {
        printf("? SQL does not end with END;\n");
        printf("Last 10 characters: '%s'\n", final_sql.substr(final_sql.length() - 10).c_str());
    }
    
    return 0;
}
