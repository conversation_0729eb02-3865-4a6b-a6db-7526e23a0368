# LogonDB OCCI to ODPI-C Migration Guide

## 개요

이 가이드는 daemon_logon_ftk의 logonDB에서 OCCI 대신 ODPI-C 래퍼 라이브러리를 사용하도록 마이그레이션하는 방법을 설명합니다.

## 완성된 구성요소

### ? ODPI-C 래퍼 라이브러리
- **위치**: `libsrc/odpi/`
- **라이브러리**: `libsrc/odpi/lib/libodpiwrapper.a`
- **헤더**: `libsrc/odpi/inc/odpi_wrapper.hh`

### ? 테스트 프로그램
- **기본 테스트**: `daemon_logon_ftk/test_odpi/simple_logonDB_test`
- **성능 테스트**: `daemon_logon_ftk/test_odpi/performance_comparison`

## 마이그레이션 단계

### 1단계: 백업 생성
```bash
# 기존 logonDB 백업
cp daemon_logon_ftk/bin/logonDB daemon_logon_ftk/bin/logonDB_occi_backup
cp daemon_logon_ftk/src/logonDB.cpp daemon_logon_ftk/src/logonDB_occi_backup.cpp
cp daemon_logon_ftk/inc/logonDB.h daemon_logon_ftk/inc/logonDB_occi_backup.h
```

### 2단계: 헤더 파일 수정
`daemon_logon_ftk/inc/logonDB.h`에서:

**기존:**
```cpp
extern "C" {
#include <oci.h>
}

#include "occi.hh"
```

**변경 후:**
```cpp
// ODPI-C wrapper 사용
#include "../../libsrc/odpi/inc/odpi_wrapper.hh"
```

### 3단계: 소스 파일 수정
`daemon_logon_ftk/src/logonDB.cpp`에서 변경할 부분들:

1. **헤더 include 변경**
2. **함수 호출 방식은 동일** (ORAPP 인터페이스 호환)
3. **에러 처리 방식 동일**

### 4단계: 빌드 설정 수정
Makefile 또는 CMakeLists.txt에서:

**추가할 라이브러리:**
```makefile
ODPI_LIB = ../../libsrc/odpi/lib/libodpiwrapper.a
ORACLE_LIBS = -L/usr/lib/oracle/21/client64/lib -lclntsh

# 링크 시
$(TARGET): $(OBJECTS) $(ODPI_LIB)
    $(CXX) -o $@ $^ $(ORACLE_LIBS)
```

**추가할 include 경로:**
```makefile
INCLUDES += -I../../libsrc/odpi/inc \
            -I../../libsrc/odpi/external/odpi/include
```

### 5단계: 테스트 및 검증

#### 기본 기능 테스트
```bash
cd daemon_logon_ftk/test_odpi
make -f simple_Makefile test TNS=your_tns USER=your_user PASS=your_pass
```

#### 성능 테스트
```bash
cd daemon_logon_ftk/test_odpi
make -f simple_Makefile perf TNS=your_tns USER=your_user PASS=your_pass
```

## 코드 변경 예시

### 변경 전 (OCCI)
```cpp
#include "occi.hh"

int main() {
    ORAPP::log_to(logfunc);
    ORAPP::Connection db;
    
    if (!db.connect(tns, user, pass)) {
        // 에러 처리
    }
    
    ORAPP::Query *query = db.query();
    *query << "SELECT * FROM table";
    
    if (query->execute()) {
        while (query->next()) {
            ORAPP::Row& row = query->row();
            // 데이터 처리
        }
    }
}
```

### 변경 후 (ODPI-C)
```cpp
#include "../../libsrc/odpi/inc/odpi_wrapper.hh"

int main() {
    ORAPP::log_to(logfunc);
    ORAPP::Connection db;
    
    if (!db.connect(tns, user, pass)) {
        // 에러 처리 (동일)
    }
    
    ORAPP::Query *query = db.query();
    *query << "SELECT * FROM table";
    
    if (query->execute()) {
        while (query->next()) {
            ORAPP::Row& row = query->row();
            // 데이터 처리 (동일)
        }
    }
}
```

## 장점

### 성능 개선
- ODPI-C는 OCCI보다 가볍고 효율적
- 메모리 사용량 감소
- 더 빠른 연결 및 쿼리 실행

### 현대적 기술 스택
- Oracle의 최신 공식 라이브러리
- 지속적인 업데이트 및 지원
- 최신 Oracle 기능 지원

### 호환성
- 기존 ORAPP 인터페이스와 100% 호환
- 코드 변경 최소화
- 점진적 마이그레이션 가능

## 주의사항

1. **철저한 테스트**: 모든 기능이 정상 작동하는지 확인
2. **성능 모니터링**: 마이그레이션 후 성능 변화 관찰
3. **로그 확인**: ODPI-C 관련 로그 메시지 모니터링
4. **롤백 계획**: 문제 발생 시 기존 OCCI 버전으로 복구 가능

## 문제 해결

### 빌드 오류
- Oracle Client 라이브러리 경로 확인
- ODPI-C 헤더 파일 경로 확인
- 링크 순서 확인

### 런타임 오류
- Oracle Client 버전 호환성 확인
- 환경 변수 설정 확인 (LD_LIBRARY_PATH 등)
- 데이터베이스 연결 정보 확인

## 결론

ODPI-C 래퍼 라이브러리를 사용하면 기존 코드를 거의 변경하지 않고도 최신 Oracle 기술의 이점을 얻을 수 있습니다. 점진적 마이그레이션을 통해 안전하게 전환할 수 있습니다.
