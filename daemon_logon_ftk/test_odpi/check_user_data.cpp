#include "../../libsrc/odpi/inc/odpi_wrapper.hh"
#include <iostream>

using namespace std;

void logfunc(const char *s) {
    printf("[USER-CHECK] %s\n", s);
}

int main(int argc, char* argv[]) {
    if (argc != 4) {
        printf("Usage: %s <tns> <user> <password>\n", argv[0]);
        return 1;
    }
    
    const char* tns = argv[1];
    const char* user = argv[2];
    const char* password = argv[3];
    
    printf("=== Checking User Data ===\n");
    
    ORAPP::log_to(logfunc);
    ORAPP::Connection db;
    
    if (!db.connect(tns, user, password)) {
        printf("Connection failed: %s\n", db.error().c_str());
        return 1;
    }
    
    printf("Connected successfully!\n");
    
    // Check TBL_USER table structure
    printf("\n=== TBL_USER Table Structure ===\n");
    ORAPP::Query* query1 = db.query();
    *query1 << "SELECT column_name, data_type, data_length FROM user_tab_columns WHERE table_name = 'TBL_USER' ORDER BY column_id";
    
    if (query1->execute()) {
        printf("TBL_USER columns:\n");
        while (query1->next()) {
            ORAPP::Row& row = query1->row();
            printf("  %s: %s(%s)\n", row[0].value().c_str(), row[1].value().c_str(), row[2].value().c_str());
        }
    }
    
    // Check if talktest1 exists in TBL_USER
    printf("\n=== Checking talktest1 in TBL_USER ===\n");
    ORAPP::Query* query2 = db.query();
    *query2 << "SELECT COUNT(*) as USER_COUNT FROM TBL_USER WHERE UPPER(user_id) = 'TALKTEST1' OR UPPER(cid) = 'TALKTEST1' OR UPPER(login_id) = 'TALKTEST1'";
    
    if (query2->execute() && query2->next()) {
        ORAPP::Row& row = query2->row();
        printf("talktest1 found in TBL_USER: %s records\n", row[0].value().c_str());
    }
    
    // Check TBL_TELCO_USER
    printf("\n=== Checking talktest1 in TBL_TELCO_USER ===\n");
    ORAPP::Query* query3 = db.query();
    *query3 << "SELECT COUNT(*) as USER_COUNT FROM TBL_TELCO_USER WHERE UPPER(user_id) = 'TALKTEST1' OR UPPER(cid) = 'TALKTEST1' OR UPPER(login_id) = 'TALKTEST1'";
    
    if (query3->execute() && query3->next()) {
        ORAPP::Row& row = query3->row();
        printf("talktest1 found in TBL_TELCO_USER: %s records\n", row[0].value().c_str());
    }
    
    // Check WEB_MTS_USER
    printf("\n=== Checking talktest1 in WEB_MTS_USER ===\n");
    ORAPP::Query* query4 = db.query();
    *query4 << "SELECT COUNT(*) as USER_COUNT FROM WEB_MTS_USER WHERE UPPER(user_id) = 'TALKTEST1' OR UPPER(cid) = 'TALKTEST1' OR UPPER(login_id) = 'TALKTEST1'";
    
    if (query4->execute() && query4->next()) {
        ORAPP::Row& row = query4->row();
        printf("talktest1 found in WEB_MTS_USER: %s records\n", row[0].value().c_str());
    }
    
    // Try to find any user with similar name
    printf("\n=== Searching for similar users ===\n");
    ORAPP::Query* query5 = db.query();
    *query5 << "SELECT 'TBL_USER' as table_name, user_id, cid, login_id FROM TBL_USER WHERE UPPER(user_id) LIKE '%TALK%' OR UPPER(cid) LIKE '%TALK%' OR UPPER(login_id) LIKE '%TALK%' "
            << "UNION ALL "
            << "SELECT 'TBL_TELCO_USER' as table_name, user_id, cid, login_id FROM TBL_TELCO_USER WHERE UPPER(user_id) LIKE '%TALK%' OR UPPER(cid) LIKE '%TALK%' OR UPPER(login_id) LIKE '%TALK%' "
            << "UNION ALL "
            << "SELECT 'WEB_MTS_USER' as table_name, user_id, cid, login_id FROM WEB_MTS_USER WHERE UPPER(user_id) LIKE '%TALK%' OR UPPER(cid) LIKE '%TALK%' OR UPPER(login_id) LIKE '%TALK%'";
    
    if (query5->execute()) {
        printf("Users with 'TALK' in name:\n");
        while (query5->next()) {
            ORAPP::Row& row = query5->row();
            printf("  %s: user_id=%s, cid=%s, login_id=%s\n", 
                   row[0].value().c_str(), row[1].value().c_str(), 
                   row[2].value().c_str(), row[3].value().c_str());
        }
    }
    
    // Simple procedure test with minimal output
    printf("\n=== Simple Procedure Test ===\n");
    ORAPP::Query* query6 = db.query();
    *query6 << "DECLARE "
            << "  v_rst NUMBER := -999; "
            << "  v_rstmsg VARCHAR2(512); "
            << "  v_appname VARCHAR2(256); "
            << "  v_sip VARCHAR2(64); "
            << "  v_pid NUMBER; "
            << "  v_job NUMBER; "
            << "  v_c_job NUMBER; "
            << "  v_prt NUMBER; "
            << "  v_cnt NUMBER; "
            << "  v_rpt_cnt NUMBER; "
            << "  v_server_info VARCHAR2(512); "
            << "  v_rpt_sleep_cnt NUMBER := 3; "
            << "  v_sender_proc VARCHAR2(256); "
            << "  v_report_proc VARCHAR2(256); "
            << "  v_senderdb_info VARCHAR2(512); "
            << "  v_reportdb_info VARCHAR2(512); "
            << "  v_logfile_info VARCHAR2(512); "
            << "  v_etc VARCHAR2(512); "
            << "BEGIN "
            << "  proc_check_mms_login_ext2("
            << "    in_cid=>'talktest1',"
            << "    in_pwd=>'talktest1',"
            << "    ot_appname=>v_appname,"
            << "    ot_sip=>v_sip,"
            << "    ot_pid=>v_pid,"
            << "    ot_job=>v_job,"
            << "    ot_c_job=>v_c_job,"
            << "    ot_prt=>v_prt,"
            << "    ot_cnt=>v_cnt,"
            << "    ot_rpt_cnt=>v_rpt_cnt,"
            << "    ot_server_info=>v_server_info,"
            << "    ot_rpt_sleep_cnt=>v_rpt_sleep_cnt,"
            << "    ot_sender_proc=>v_sender_proc,"
            << "    ot_report_proc=>v_report_proc,"
            << "    ot_senderdb_info=>v_senderdb_info,"
            << "    ot_reportdb_info=>v_reportdb_info,"
            << "    ot_logfile_info=>v_logfile_info,"
            << "    ot_etc=>v_etc,"
            << "    ot_rst=>v_rst,"
            << "    ot_rstmsg=>v_rstmsg"
            << "  ); "
            << "  IF v_rst = -999 THEN "
            << "    RAISE_APPLICATION_ERROR(-20001, 'RST still -999: ' || NVL(v_rstmsg, 'No message')); "
            << "  END IF; "
            << "END;";
    
    if (query6->execute()) {
        printf("Procedure executed successfully (RST changed from -999)\n");
    } else {
        printf("Procedure result: %s\n", query6->error().c_str());
    }
    
    db.disconnect();
    return 0;
}
