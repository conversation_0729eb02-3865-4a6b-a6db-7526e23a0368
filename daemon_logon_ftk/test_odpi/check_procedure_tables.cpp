#include "../../libsrc/odpi/inc/odpi_wrapper.hh"
#include <iostream>

using namespace std;

void logfunc(const char *s) {
    // Minimal logging
}

int main(int argc, char* argv[]) {
    if (argc != 4) {
        printf("Usage: %s <tns> <user> <password>\n", argv[0]);
        return 1;
    }
    
    const char* tns = argv[1];
    const char* user = argv[2];
    const char* password = argv[3];
    
    printf("=== Checking Procedure Required Tables ===\n");
    
    ORAPP::log_to(logfunc);
    ORAPP::Connection db;
    
    if (!db.connect(tns, user, password)) {
        printf("Connection failed: %s\n", db.error().c_str());
        return 1;
    }
    
    // Check required tables
    string required_tables[] = {
        "TBL_LOGON",
        "TBL_PROCESS_LOGFILE", 
        "TBL_PROCESS_GROUP",
        "TBL_PROCESS_INFO",
        "TBL_PTN_INFO",
        "TBL_CODE"
    };
    
    printf("=== Table Existence Check ===\n");
    for (const string& table : required_tables) {
        ORAPP::Query* query = db.query();
        *query << "SELECT COUNT(*) FROM user_tables WHERE table_name = '" << table << "'";
        
        if (query->execute() && query->next()) {
            ORAPP::Row& row = query->row();
            string count = row[0].value();
            printf("%-20s: %s\n", table.c_str(), (count == "1" ? "EXISTS" : "MISSING"));
        }
    }
    
    // Check tbl_logon data
    printf("\n=== TBL_LOGON Data Check ===\n");
    ORAPP::Query* query1 = db.query();
    *query1 << "SELECT COUNT(*) as TOTAL_RECORDS FROM TBL_LOGON";
    
    if (query1->execute() && query1->next()) {
        ORAPP::Row& row = query1->row();
        printf("Total records in TBL_LOGON: %s\n", row[0].value().c_str());
    } else {
        printf("Failed to query TBL_LOGON: %s\n", query1->error().c_str());
    }
    
    // Check for talktest1 in tbl_logon
    printf("\n=== Checking talktest1 in TBL_LOGON ===\n");
    ORAPP::Query* query2 = db.query();
    *query2 << "SELECT cid, pwd, pid, job, use_yn FROM TBL_LOGON WHERE cid = 'talktest1'";
    
    if (query2->execute()) {
        if (query2->next()) {
            ORAPP::Row& row = query2->row();
            printf("Found talktest1: CID=%s, PWD=%s, PID=%s, JOB=%s, USE_YN=%s\n",
                   row[0].value().c_str(), row[1].value().c_str(), 
                   row[2].value().c_str(), row[3].value().c_str(), row[4].value().c_str());
        } else {
            printf("talktest1 NOT FOUND in TBL_LOGON\n");
        }
    } else {
        printf("Failed to query TBL_LOGON for talktest1: %s\n", query2->error().c_str());
    }
    
    // Show sample data from tbl_logon
    printf("\n=== Sample TBL_LOGON Data ===\n");
    ORAPP::Query* query3 = db.query();
    *query3 << "SELECT cid, pwd, pid, job, use_yn FROM TBL_LOGON WHERE ROWNUM <= 3";
    
    if (query3->execute()) {
        printf("Sample records:\n");
        while (query3->next()) {
            ORAPP::Row& row = query3->row();
            printf("  CID=%s, PWD=%s, PID=%s, JOB=%s, USE_YN=%s\n",
                   row[0].value().c_str(), row[1].value().c_str(), 
                   row[2].value().c_str(), row[3].value().c_str(), row[4].value().c_str());
        }
    }
    
    // Test procedure with debug exception handling
    printf("\n=== Testing Procedure with Exception Handling ===\n");
    ORAPP::Query* query4 = db.query();
    *query4 << "DECLARE "
            << "  v_rst NUMBER := -999; "
            << "  v_rstmsg VARCHAR2(512); "
            << "  v_appname VARCHAR2(256); "
            << "  v_sip VARCHAR2(64); "
            << "  v_pid NUMBER; "
            << "  v_job NUMBER; "
            << "  v_c_job NUMBER; "
            << "  v_prt NUMBER; "
            << "  v_cnt NUMBER; "
            << "  v_rpt_cnt NUMBER; "
            << "  v_server_info VARCHAR2(512); "
            << "  v_rpt_sleep_cnt NUMBER := 3; "
            << "  v_sender_proc VARCHAR2(256); "
            << "  v_report_proc VARCHAR2(256); "
            << "  v_senderdb_info VARCHAR2(512); "
            << "  v_reportdb_info VARCHAR2(512); "
            << "  v_logfile_info VARCHAR2(512); "
            << "  v_etc VARCHAR2(512); "
            << "BEGIN "
            << "  BEGIN "
            << "    proc_check_mms_login_ext2("
            << "      in_cid=>'talktest1',"
            << "      in_pwd=>'talktest1',"
            << "      ot_appname=>v_appname,"
            << "      ot_sip=>v_sip,"
            << "      ot_pid=>v_pid,"
            << "      ot_job=>v_job,"
            << "      ot_c_job=>v_c_job,"
            << "      ot_prt=>v_prt,"
            << "      ot_cnt=>v_cnt,"
            << "      ot_rpt_cnt=>v_rpt_cnt,"
            << "      ot_server_info=>v_server_info,"
            << "      ot_rpt_sleep_cnt=>v_rpt_sleep_cnt,"
            << "      ot_sender_proc=>v_sender_proc,"
            << "      ot_report_proc=>v_report_proc,"
            << "      ot_senderdb_info=>v_senderdb_info,"
            << "      ot_reportdb_info=>v_reportdb_info,"
            << "      ot_logfile_info=>v_logfile_info,"
            << "      ot_etc=>v_etc,"
            << "      ot_rst=>v_rst,"
            << "      ot_rstmsg=>v_rstmsg"
            << "    ); "
            << "  EXCEPTION "
            << "    WHEN OTHERS THEN "
            << "      v_rst := -888; "
            << "      v_rstmsg := 'OUTER EXCEPTION: ' || SQLERRM; "
            << "  END; "
            << "  RAISE_APPLICATION_ERROR(-20001, 'RST=' || v_rst || ', MSG=' || NVL(v_rstmsg, 'NULL')); "
            << "END;";
    
    if (query4->execute()) {
        printf("Procedure test: Unexpected success\n");
    } else {
        printf("Procedure result: %s\n", query4->error().c_str());
    }
    
    db.disconnect();
    return 0;
}
