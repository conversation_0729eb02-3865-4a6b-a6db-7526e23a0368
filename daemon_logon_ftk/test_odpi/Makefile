# LogonDB ODPI-C Integration Test Makefile

# Compiler settings
CXX = g++
CXXFLAGS = -std=c++11 -Wall -Wextra -O2

# Directories
FTALK_ROOT = ../..
ODPI_DIR = $(FTALK_ROOT)/libsrc/odpi
LOGON_INC = ../inc
LIBKSKYB_INC = $(FTALK_ROOT)/libsrc/libkskyb/inc

# Include paths
INCLUDES = -I$(ODPI_DIR)/inc \
           -I$(LOGON_INC) \
           -I$(LIBKSKYB_INC) \
           -I/usr/include/oracle/21/client64

# Library paths
ODPI_LIB = $(ODPI_DIR)/lib/libodpiwrapper.a
ORACLE_LIBS = -L/usr/lib/oracle/21/client64/lib -lclntsh
LIBKSKYB_LIB = $(FTALK_ROOT)/libsrc/libkskyb/lib/libkskyb.a

# Source files
SOURCES = logonDB_odpi_test.cpp
TARGET = logonDB_odpi_test

# Default target
all: $(TARGET)

# Build target
$(TARGET): $(SOURCES) $(ODPI_LIB)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(ODPI_LIB) $(LIBKSKYB_LIB) $(ORACLE_LIBS)

# Build ODPI-C library if needed
$(ODPI_LIB):
	cd $(ODPI_DIR) && make

# Clean
clean:
	rm -f $(TARGET)

# Test run (requires database connection parameters)
test: $(TARGET)
	@echo "Usage: make test TNS=<tns> USER=<user> PASS=<password>"
	@if [ -n "$(TNS)" ] && [ -n "$(USER)" ] && [ -n "$(PASS)" ]; then \
		echo "Running test with $(USER)@$(TNS)"; \
		./$(TARGET) $(TNS) $(USER) $(PASS); \
	else \
		echo "Please provide TNS, USER, and PASS parameters"; \
		echo "Example: make test TNS=localhost:1521/XE USER=testuser PASS=testpass"; \
	fi

# Help
help:
	@echo "Available targets:"
	@echo "  all     - Build the test program"
	@echo "  clean   - Remove built files"
	@echo "  test    - Run the test (requires TNS, USER, PASS parameters)"
	@echo "  help    - Show this help"

.PHONY: all clean test help
