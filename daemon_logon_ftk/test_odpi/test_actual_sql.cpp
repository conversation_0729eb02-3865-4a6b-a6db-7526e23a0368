#include "../../libsrc/odpi/inc/odpi_wrapper.hh"
#include <iostream>

using namespace std;

void logfunc(const char *s) {
    printf("[SQL-TEST] %s\n", s);
}

int main(int argc, char* argv[]) {
    if (argc != 4) {
        printf("Usage: %s <tns> <user> <password>\n", argv[0]);
        return 1;
    }
    
    const char* tns = argv[1];
    const char* user = argv[2];
    const char* password = argv[3];
    
    printf("=== Testing Actual SQL Execution ===\n");
    
    ORAPP::log_to(logfunc);
    ORAPP::Connection db;
    
    if (!db.connect(tns, user, password)) {
        printf("Connection failed: %s\n", db.error().c_str());
        return 1;
    }
    
    printf("Connected successfully!\n");
    
    // Test the exact SQL that logonDB generates
    ORAPP::Query* query = db.query();
    
    int nRptNoDataSleep = 3;
    int nmRST = -999;
    
    *query << "DECLARE "
           << "  v_appname VARCHAR2(256); "
           << "  v_sip VARCHAR2(64); "
           << "  v_pid NUMBER; "
           << "  v_job NUMBER; "
           << "  v_c_job NUMBER; "
           << "  v_prt NUMBER; "
           << "  v_cnt NUMBER; "
           << "  v_rpt_cnt NUMBER; "
           << "  v_server_info VARCHAR2(512); "
           << "  v_rpt_sleep_cnt NUMBER DEFAULT " << nRptNoDataSleep << "; "
           << "  v_sender_proc VARCHAR2(256); "
           << "  v_report_proc VARCHAR2(256); "
           << "  v_senderdb_info VARCHAR2(512); "
           << "  v_reportdb_info VARCHAR2(512); "
           << "  v_logfile_info VARCHAR2(512); "
           << "  v_etc VARCHAR2(512); "
           << "  v_rst NUMBER DEFAULT " << nmRST << "; "
           << "  v_rstmsg VARCHAR2(512); "
           << "BEGIN "
           << "  proc_check_mms_login_ext2("
           << "    in_cid=>'talktest1',"
           << "    in_pwd=>'talktest1',"
           << "    ot_appname=>v_appname,"
           << "    ot_sip=>v_sip,"
           << "    ot_pid=>v_pid,"
           << "    ot_job=>v_job,"
           << "    ot_c_job=>v_c_job,"
           << "    ot_prt=>v_prt,"
           << "    ot_cnt=>v_cnt,"
           << "    ot_rpt_cnt=>v_rpt_cnt,"
           << "    ot_server_info=>v_server_info,"
           << "    ot_rpt_sleep_cnt=>v_rpt_sleep_cnt,"
           << "    ot_sender_proc=>v_sender_proc,"
           << "    ot_report_proc=>v_report_proc,"
           << "    ot_senderdb_info=>v_senderdb_info,"
           << "    ot_reportdb_info=>v_reportdb_info,"
           << "    ot_logfile_info=>v_logfile_info,"
           << "    ot_etc=>v_etc,"
           << "    ot_rst=>v_rst,"
           << "    ot_rstmsg=>v_rstmsg"
           << "  ); "
           << "END;";
    
    string sql = query->get_sql();
    printf("SQL length: %zu\n", sql.length());
    printf("SQL: %s\n", sql.c_str());
    
    printf("\n=== Executing SQL ===\n");
    if (query->execute()) {
        printf("? SQL executed successfully!\n");
    } else {
        printf("? SQL execution failed: %s\n", query->error().c_str());
        
        // Try to identify the exact problem
        printf("\n=== Analyzing Error ===\n");
        string error = query->error();
        if (error.find("column 1021") != string::npos) {
            printf("Error at position 1021, SQL length is %zu\n", sql.length());
            if (sql.length() >= 1021) {
                printf("Character at position 1021: '%c'\n", sql[1020]); // 0-based index
                printf("Context around position 1021: '%.20s'\n", sql.substr(1015, 20).c_str());
            }
        }
    }
    
    db.disconnect();
    return 0;
}
