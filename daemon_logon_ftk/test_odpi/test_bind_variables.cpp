#include "../../libsrc/odpi/inc/odpi_wrapper.hh"
#include <iostream>
#include <cstring>

using namespace std;

// Test log function
void logfunc(const char *s) {
    printf("[BIND-TEST] %s\n", s);
    fflush(stdout);
}

int main(int argc, char* argv[]) {
    if (argc != 4) {
        printf("Usage: %s <tns> <user> <password>\n", argv[0]);
        return 1;
    }
    
    const char* tns = argv[1];
    const char* user = argv[2];
    const char* password = argv[3];
    
    printf("=== Bind Variables Test ===\n");
    printf("Connecting to: %s@%s\n", user, tns);
    
    // Set up logging
    ORAPP::log_to(logfunc);
    ORAPP::Connection db;
    
    // Test connection
    if (!db.connect(tns, user, password)) {
        printf("Connection failed: %s\n", db.error().c_str());
        return 1;
    }
    
    printf("Connected successfully!\n");
    
    // Test 1: Simple bind variables like logonDB uses
    printf("\n=== Test 1: Simple Bind Variables ===\n");
    
    ORAPP::Query* query = db.query();
    if (!query) {
        printf("Failed to create query\n");
        return 1;
    }
    
    // Simulate the logonDB bind pattern
    char szAPPName[256] = "TEST_APP";
    char szSIP[64] = "127.0.0.1";
    int nmPID = 12345;
    int nmJOB = 1;
    int nmCJOB = 2;
    int nmPRT = 8080;
    int nmCNT = 100;
    int nmRptCnt = 5;
    char szServerInfo[512] = "TEST_SERVER";
    int nRptWait = 3;
    char szSenderProc[256] = "SENDER_PROC";
    char szReportProc[256] = "REPORT_PROC";
    char szSenderDBInfo[512] = "SENDER_DB";
    char szReportDBInfo[512] = "REPORT_DB";
    char szLogFileInfo[512] = "LOG_FILE";
    char szReserve[512] = "RESERVE";
    int nmRST = -999;
    char szErrMsg[512] = "";
    
    // Build SQL like logonDB does
    *query << "BEGIN "
           << "proc_check_mms_login_ext2("
           << "in_cid=>'talktest1',"
           << "in_pwd=>'talktest1',"
           << "ot_appname=>:szAPPName,"
           << "ot_sip=>:szSIP,"
           << "ot_pid=>:nmPID,"
           << "ot_job=>:nmJOB,"
           << "ot_c_job=>:nmCJOB,"
           << "ot_prt=>:nmPRT,"
           << "ot_cnt=>:nmCNT,"
           << "ot_rpt_cnt=>:nmRptCnt,"
           << "ot_server_info=>:szServerInfo,"
           << "ot_rpt_sleep_cnt=>:nRptWait,"
           << "ot_sender_proc=>:szSenderProc,"
           << "ot_report_proc=>:szReportProc,"
           << "ot_senderdb_info=>:szSenderDBInfo,"
           << "ot_reportdb_info=>:szReportDBInfo,"
           << "ot_logfile_info=>:szLogFileInfo,"
           << "ot_etc=>:szReserve,"
           << "ot_rst=>:nmRST,"
           << "ot_rstmsg=>:szErrMsg"
           << ");"
           << "END;";
    
    // Bind variables like logonDB does
    query->bind(":szAPPName", szAPPName, sizeof(szAPPName));
    query->bind(":szSIP", szSIP, sizeof(szSIP));
    query->bind(":nmPID", nmPID);
    query->bind(":nmJOB", nmJOB);
    query->bind(":nmCJOB", nmCJOB);
    query->bind(":nmPRT", nmPRT);
    query->bind(":nmCNT", nmCNT);
    query->bind(":nmRptCnt", nmRptCnt);
    query->bind(":szServerInfo", szServerInfo, sizeof(szServerInfo));
    query->bind(":nRptWait", nRptWait);
    query->bind(":szSenderProc", szSenderProc, sizeof(szSenderProc));
    query->bind(":szReportProc", szReportProc, sizeof(szReportProc));
    query->bind(":szSenderDBInfo", szSenderDBInfo, sizeof(szSenderDBInfo));
    query->bind(":szReportDBInfo", szReportDBInfo, sizeof(szReportDBInfo));
    query->bind(":szLogFileInfo", szLogFileInfo, sizeof(szLogFileInfo));
    query->bind(":szReserve", szReserve, sizeof(szReserve));
    query->bind(":nmRST", nmRST);
    query->bind(":szErrMsg", szErrMsg, sizeof(szErrMsg));
    
    // Check SQL after binding
    std::string final_sql = query->get_sql();
    printf("Final SQL length: %zu characters\n", final_sql.length());
    printf("First 200 chars: %.200s\n", final_sql.c_str());
    
    // Test 2: Simple SELECT to verify basic functionality
    printf("\n=== Test 2: Simple SELECT ===\n");
    
    ORAPP::Query* simple_query = db.query();
    *simple_query << "SELECT 'SUCCESS' as RESULT, SYSDATE as CURRENT_TIME FROM DUAL";
    
    if (!simple_query->execute()) {
        printf("Simple query failed: %s\n", simple_query->error().c_str());
    } else {
        printf("Simple query executed successfully\n");
        if (simple_query->next()) {
            ORAPP::Row& row = simple_query->row();
            printf("Result: %s, Time: %s\n", row[0].value().c_str(), row[1].value().c_str());
        }
    }
    
    // Disconnect
    if (!db.disconnect()) {
        printf("Disconnect failed: %s\n", db.error().c_str());
        return 1;
    }
    
    printf("\n=== Bind Variables Test Completed ===\n");
    return 0;
}
