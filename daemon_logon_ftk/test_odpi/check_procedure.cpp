#include "../../libsrc/odpi/inc/odpi_wrapper.hh"
#include <iostream>

using namespace std;

void logfunc(const char *s) {
    printf("[PROC-CHECK] %s\n", s);
}

int main(int argc, char* argv[]) {
    if (argc != 4) {
        printf("Usage: %s <tns> <user> <password>\n", argv[0]);
        return 1;
    }
    
    const char* tns = argv[1];
    const char* user = argv[2];
    const char* password = argv[3];
    
    printf("=== Checking proc_check_mms_login_ext2 ===\n");
    
    ORAPP::log_to(logfunc);
    ORAPP::Connection db;
    
    if (!db.connect(tns, user, password)) {
        printf("Connection failed: %s\n", db.error().c_str());
        return 1;
    }
    
    printf("Connected successfully!\n");
    
    // Check if procedure exists
    ORAPP::Query* query = db.query();
    *query << "SELECT object_name, object_type, status FROM user_objects WHERE object_name = 'PROC_CHECK_MMS_LOGIN_EXT2'";
    
    if (!query->execute()) {
        printf("Query failed: %s\n", query->error().c_str());
        return 1;
    }
    
    bool found = false;
    while (query->next()) {
        ORAPP::Row& row = query->row();
        printf("Found: %s %s %s\n", row[0].value().c_str(), row[1].value().c_str(), row[2].value().c_str());
        found = true;
    }
    
    if (!found) {
        printf("Procedure proc_check_mms_login_ext2 not found!\n");
        
        // Check all procedures
        query->clear();
        *query << "SELECT object_name FROM user_objects WHERE object_type = 'PROCEDURE' ORDER BY object_name";
        
        if (query->execute()) {
            printf("\nAvailable procedures:\n");
            while (query->next()) {
                ORAPP::Row& row = query->row();
                printf("  - %s\n", row[0].value().c_str());
            }
        }
    } else {
        // Get procedure parameters
        query->clear();
        *query << "SELECT argument_name, data_type, in_out, position "
               << "FROM user_arguments "
               << "WHERE object_name = 'PROC_CHECK_MMS_LOGIN_EXT2' "
               << "ORDER BY position";
        
        if (query->execute()) {
            printf("\nProcedure parameters:\n");
            while (query->next()) {
                ORAPP::Row& row = query->row();
                printf("  %s: %s %s %s (pos: %s)\n", 
                       row[0].value().c_str(), 
                       row[1].value().c_str(), 
                       row[2].value().c_str(),
                       row[0].value().c_str(),
                       row[3].value().c_str());
            }
        }
    }
    
    // Test a simple procedure call
    printf("\n=== Testing simple procedure call ===\n");
    query->clear();
    *query << "BEGIN DBMS_OUTPUT.PUT_LINE('Hello from Oracle'); END;";
    
    if (query->execute()) {
        printf("Simple procedure call succeeded\n");
    } else {
        printf("Simple procedure call failed: %s\n", query->error().c_str());
    }
    
    db.disconnect();
    return 0;
}
