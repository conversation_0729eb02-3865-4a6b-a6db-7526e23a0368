#include "../../libsrc/odpi/inc/odpi_wrapper.hh"
#include <iostream>
#include <cstring>
#include <set>

using namespace std;

// Test log function for ODPI-C wrapper
void logfunc(const char *s)
{
    printf("[ODPI-LOG] %s\n", s);
    fflush(stdout);
}

bool orapp_connect(ORAPP::Connection &db, const char *tns, const char *user, const char *pass) 
{
    if (!db.connect(tns, user, pass)) 
    {
        printf("[ERR] db connect failed - [%s]\n", db.error().c_str());
        return false;
    }
    printf("[INF] db connect success - ver [%s]\n", db.version().c_str());
    return true;
}

bool orapp_disconnect(ORAPP::Connection &db) {
    if (!db.disconnect()) 
    {
        printf("[ERR] db disconnect failed\n");
        return false;
    }
    printf("db disconnect success\n");
    return true;
}

int checkLogon(ORAPP::Connection &db, int type)
{
    printf("[INF] checkLogon called with type: %d\n", type);
    
    ORAPP::Query *query = db.query();
    if (!query) {
        printf("[ERR] Failed to create query\n");
        return -1;
    }
    
    // Test query - similar to what logonDB does
    *query << "SELECT 'TEST_USER' as USER_ID, 'ACTIVE' as STATUS FROM DUAL";
    
    if (!query->execute()) {
        printf("[ERR] Query execution failed: %s\n", query->error().c_str());
        return -1;
    }
    
    if (query->next()) {
        ORAPP::Row& row = query->row();
        printf("[INF] Query result: USER_ID=%s, STATUS=%s\n", 
               row[0].value().c_str(), row[1].value().c_str());
        return 0; // Success
    }
    
    return -2; // Not found
}

int loadCallback(ORAPP::Connection &db, int type, set<string> &set_callback_list) 
{
    printf("[INF] loadCallback called with type: %d\n", type);
    
    ORAPP::Query *query = db.query();
    if (!query) {
        printf("[ERR] Failed to create query\n");
        return -1;
    }
    
    // Simulate the callback query from original logonDB
    int test_pid = 12345; // Test PID
    
    query->clear();
    *query << "SELECT " << test_pid << " as PTN_ID, 'TEST_CALLBACK' as CALLBACK FROM DUAL";
    
    printf("[INF] Executing callback query\n");
    
    if (!query->execute()) {
        printf("[ERR] Query execution failed: %s\n", query->error().c_str());
        return -1;
    }
    
    int count = 0;
    while (query->next()) {
        ORAPP::Row& row = query->row();
        printf("[INF] Callback result: PTN_ID=%s, CALLBACK=%s\n", 
               row[0].value().c_str(), row[1].value().c_str());
        
        set_callback_list.insert(row[1].value());
        count++;
    }
    
    printf("[INF] Loaded %d callback entries\n", count);
    return 0;
}

// Test multiple row fetching like the original logonDB
int testMultipleRows(ORAPP::Connection &db)
{
    printf("\n=== Testing Multiple Row Fetch ===\n");
    
    ORAPP::Query *query = db.query();
    if (!query) {
        printf("[ERR] Failed to create query\n");
        return -1;
    }
    
    // Query that returns multiple rows
    *query << "SELECT LEVEL as ROW_NUM, 'Data_' || LEVEL as DATA_VALUE FROM DUAL CONNECT BY LEVEL <= 5";
    
    if (!query->execute()) {
        printf("[ERR] Multiple rows query failed: %s\n", query->error().c_str());
        return -1;
    }
    
    int row_count = 0;
    while (query->next()) {
        ORAPP::Row& row = query->row();
        printf("[INF] Row %d: ROW_NUM=%s, DATA_VALUE=%s\n", 
               ++row_count, row[0].value().c_str(), row[1].value().c_str());
    }
    
    printf("[INF] Total rows fetched: %d\n", row_count);
    return row_count;
}

// Test parameterized query like logonDB does
int testParameterizedQuery(ORAPP::Connection &db, int pid)
{
    printf("\n=== Testing Parameterized Query ===\n");
    
    ORAPP::Query *query = db.query();
    if (!query) {
        printf("[ERR] Failed to create query\n");
        return -1;
    }
    
    // Build query with parameters like original logonDB
    query->clear();
    *query << "SELECT PTN_ID, CALLBACK FROM (";
    *query << "  SELECT " << pid << " as PTN_ID, 'CALLBACK_" << pid << "' as CALLBACK FROM DUAL";
    *query << "  UNION ALL";
    *query << "  SELECT " << (pid + 1) << " as PTN_ID, 'CALLBACK_" << (pid + 1) << "' as CALLBACK FROM DUAL";
    *query << ") WHERE PTN_ID = " << pid;
    
    printf("[INF] Executing parameterized query for PID: %d\n", pid);
    
    if (!query->execute()) {
        printf("[ERR] Parameterized query failed: %s\n", query->error().c_str());
        return -1;
    }
    
    int found_count = 0;
    while (query->next()) {
        ORAPP::Row& row = query->row();
        printf("[INF] Found: PTN_ID=%s, CALLBACK=%s\n", 
               row[0].value().c_str(), row[1].value().c_str());
        found_count++;
    }
    
    return found_count;
}

// Test main function
int main(int argc, char* argv[]) {
    if (argc != 4) {
        printf("Usage: %s <tns> <user> <password>\n", argv[0]);
        printf("Example: %s localhost:1521/XE testuser testpass\n", argv[0]);
        return 1;
    }
    
    const char* tns = argv[1];
    const char* user = argv[2];
    const char* password = argv[3];
    
    printf("=== LogonDB ODPI-C Integration Test ===\n");
    printf("Connecting to: %s@%s\n", user, tns);
    
    // Set up logging
    ORAPP::log_to(logfunc);
    ORAPP::Connection db;
    
    // Test connection
    if (!orapp_connect(db, tns, user, password)) {
        printf("Connection test failed\n");
        return 1;
    }
    
    printf("\n=== Testing checkLogon function ===\n");
    int result = checkLogon(db, 1);
    printf("checkLogon result: %d\n", result);
    
    printf("\n=== Testing loadCallback function ===\n");
    set<string> callback_list;
    result = loadCallback(db, 1, callback_list);
    printf("loadCallback result: %d\n", result);
    printf("Callback list size: %zu\n", callback_list.size());
    
    // Print callback list
    for (const auto& callback : callback_list) {
        printf("Callback: %s\n", callback.c_str());
    }
    
    // Test multiple rows
    testMultipleRows(db);
    
    // Test parameterized query
    testParameterizedQuery(db, 12345);
    
    printf("\n=== Testing transaction operations ===\n");
    if (db.commit()) {
        printf("Commit successful\n");
    } else {
        printf("Commit failed: %s\n", db.error().c_str());
    }
    
    printf("\n=== Testing disconnect ===\n");
    if (orapp_disconnect(db)) {
        printf("Disconnect successful\n");
    } else {
        printf("Disconnect failed\n");
    }
    
    printf("\n=== Test completed successfully ===\n");
    return 0;
}
