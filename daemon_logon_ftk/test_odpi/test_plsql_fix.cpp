#include "../../libsrc/odpi/inc/odpi_wrapper.hh"
#include <iostream>

using namespace std;

void logfunc(const char *s) {
    printf("[PLSQL-FIX] %s\n", s);
}

int main(int argc, char* argv[]) {
    if (argc != 4) {
        printf("Usage: %s <tns> <user> <password>\n", argv[0]);
        return 1;
    }
    
    const char* tns = argv[1];
    const char* user = argv[2];
    const char* password = argv[3];
    
    printf("=== Testing PL/SQL Fix ===\n");
    
    ORAPP::log_to(logfunc);
    ORAPP::Connection db;
    
    if (!db.connect(tns, user, password)) {
        printf("Connection failed: %s\n", db.error().c_str());
        return 1;
    }
    
    printf("Connected successfully!\n");
    
    // Test 1: Original problematic approach
    printf("\n=== Test 1: Original Approach (should fail) ===\n");
    ORAPP::Query* query1 = db.query();
    *query1 << "BEGIN proc_check_mms_login_ext2("
            << "in_cid=>'talktest1',"
            << "in_pwd=>'talktest1',"
            << "ot_appname=>'',"
            << "ot_sip=>'',"
            << "ot_pid=>0,"
            << "ot_job=>0,"
            << "ot_c_job=>0,"
            << "ot_prt=>0,"
            << "ot_cnt=>0,"
            << "ot_rpt_cnt=>0,"
            << "ot_server_info=>'',"
            << "ot_rpt_sleep_cnt=>3,"
            << "ot_sender_proc=>'',"
            << "ot_report_proc=>'',"
            << "ot_senderdb_info=>'',"
            << "ot_reportdb_info=>'',"
            << "ot_logfile_info=>'',"
            << "ot_etc=>'',"
            << "ot_rst=>-999,"
            << "ot_rstmsg=>''"
            << "); END;";
    
    if (!query1->execute()) {
        printf("Expected failure: %s\n", query1->error().c_str());
    } else {
        printf("Unexpected success!\n");
    }
    
    // Test 2: Fixed approach with declared variables
    printf("\n=== Test 2: Fixed Approach with Variables ===\n");
    ORAPP::Query* query2 = db.query();
    *query2 << "DECLARE "
            << "  v_appname VARCHAR2(256); "
            << "  v_sip VARCHAR2(64); "
            << "  v_pid NUMBER; "
            << "  v_job NUMBER; "
            << "  v_c_job NUMBER; "
            << "  v_prt NUMBER; "
            << "  v_cnt NUMBER; "
            << "  v_rpt_cnt NUMBER; "
            << "  v_server_info VARCHAR2(512); "
            << "  v_rpt_sleep_cnt NUMBER := 3; "
            << "  v_sender_proc VARCHAR2(256); "
            << "  v_report_proc VARCHAR2(256); "
            << "  v_senderdb_info VARCHAR2(512); "
            << "  v_reportdb_info VARCHAR2(512); "
            << "  v_logfile_info VARCHAR2(512); "
            << "  v_etc VARCHAR2(512); "
            << "  v_rst NUMBER := -999; "
            << "  v_rstmsg VARCHAR2(512); "
            << "BEGIN "
            << "  proc_check_mms_login_ext2("
            << "    in_cid=>'talktest1',"
            << "    in_pwd=>'talktest1',"
            << "    ot_appname=>v_appname,"
            << "    ot_sip=>v_sip,"
            << "    ot_pid=>v_pid,"
            << "    ot_job=>v_job,"
            << "    ot_c_job=>v_c_job,"
            << "    ot_prt=>v_prt,"
            << "    ot_cnt=>v_cnt,"
            << "    ot_rpt_cnt=>v_rpt_cnt,"
            << "    ot_server_info=>v_server_info,"
            << "    ot_rpt_sleep_cnt=>v_rpt_sleep_cnt,"
            << "    ot_sender_proc=>v_sender_proc,"
            << "    ot_report_proc=>v_report_proc,"
            << "    ot_senderdb_info=>v_senderdb_info,"
            << "    ot_reportdb_info=>v_reportdb_info,"
            << "    ot_logfile_info=>v_logfile_info,"
            << "    ot_etc=>v_etc,"
            << "    ot_rst=>v_rst,"
            << "    ot_rstmsg=>v_rstmsg"
            << "  ); "
            << "  DBMS_OUTPUT.PUT_LINE('Result: ' || v_rst || ', Message: ' || v_rstmsg); "
            << "END;";
    
    printf("SQL: %s\n", query2->get_sql().c_str());
    
    if (query2->execute()) {
        printf("Fixed approach succeeded!\n");
    } else {
        printf("Fixed approach failed: %s\n", query2->error().c_str());
    }
    
    db.disconnect();
    return 0;
}
