# Simple LogonDB ODPI-C Test Makefile

# Compiler settings
CXX = g++
CXXFLAGS = -std=c++11 -Wall -Wextra -O2

# Directories
ODPI_DIR = ../../libsrc/odpi

# Include paths
INCLUDES = -I$(ODPI_DIR)/inc \
           -I$(ODPI_DIR)/external/odpi/include \
           -I/usr/include/oracle/21/client64

# Library paths
ODPI_LIB = $(ODPI_DIR)/lib/libodpiwrapper.a
ORACLE_LIBS = -L/usr/lib/oracle/21/client64/lib -lclntsh

# Source files
SOURCES = simple_logonDB_test.cpp
TARGET = simple_logonDB_test

PERF_SOURCES = performance_comparison.cpp
PERF_TARGET = performance_comparison

BIND_SOURCES = test_bind_variables.cpp
BIND_TARGET = test_bind_variables

CHECK_SOURCES = check_procedure.cpp
CHECK_TARGET = check_procedure

FIX_SOURCES = test_plsql_fix.cpp
FIX_TARGET = test_plsql_fix

PARAM_SOURCES = check_param_sizes.cpp
PARAM_TARGET = check_param_sizes

DEBUG_SOURCES = debug_procedure_result.cpp
DEBUG_TARGET = debug_procedure_result

USER_SOURCES = check_user_data.cpp
USER_TARGET = check_user_data

REAL_SOURCES = check_real_users.cpp
REAL_TARGET = check_real_users

TABLES_SOURCES = check_procedure_tables.cpp
TABLES_TARGET = check_procedure_tables

SQL_SOURCES = test_sql_generation.cpp
SQL_TARGET = test_sql_generation

ACTUAL_SOURCES = test_actual_sql.cpp
ACTUAL_TARGET = test_actual_sql

# Default target
all: $(TARGET) $(PERF_TARGET) $(BIND_TARGET) $(CHECK_TARGET)

# Build targets
$(TARGET): $(SOURCES) $(ODPI_LIB)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(ODPI_LIB) $(ORACLE_LIBS)

$(PERF_TARGET): $(PERF_SOURCES) $(ODPI_LIB)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(ODPI_LIB) $(ORACLE_LIBS)

$(BIND_TARGET): $(BIND_SOURCES) $(ODPI_LIB)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(ODPI_LIB) $(ORACLE_LIBS)

$(CHECK_TARGET): $(CHECK_SOURCES) $(ODPI_LIB)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(ODPI_LIB) $(ORACLE_LIBS)

$(FIX_TARGET): $(FIX_SOURCES) $(ODPI_LIB)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(ODPI_LIB) $(ORACLE_LIBS)

$(PARAM_TARGET): $(PARAM_SOURCES) $(ODPI_LIB)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(ODPI_LIB) $(ORACLE_LIBS)

$(DEBUG_TARGET): $(DEBUG_SOURCES) $(ODPI_LIB)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(ODPI_LIB) $(ORACLE_LIBS)

$(USER_TARGET): $(USER_SOURCES) $(ODPI_LIB)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(ODPI_LIB) $(ORACLE_LIBS)

$(REAL_TARGET): $(REAL_SOURCES) $(ODPI_LIB)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(ODPI_LIB) $(ORACLE_LIBS)

$(TABLES_TARGET): $(TABLES_SOURCES) $(ODPI_LIB)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(ODPI_LIB) $(ORACLE_LIBS)

$(SQL_TARGET): $(SQL_SOURCES) $(ODPI_LIB)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(ODPI_LIB) $(ORACLE_LIBS)

$(ACTUAL_TARGET): $(ACTUAL_SOURCES) $(ODPI_LIB)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(ODPI_LIB) $(ORACLE_LIBS)

# Build ODPI-C library if needed
$(ODPI_LIB):
	cd $(ODPI_DIR) && make

# Clean
clean:
	rm -f $(TARGET) $(PERF_TARGET) $(BIND_TARGET) $(CHECK_TARGET)

# Test run (requires database connection parameters)
test: $(TARGET)
	@echo "Usage: make test TNS=<tns> USER=<user> PASS=<password>"
	@if [ -n "$(TNS)" ] && [ -n "$(USER)" ] && [ -n "$(PASS)" ]; then \
		echo "Running test with $(USER)@$(TNS)"; \
		./$(TARGET) $(TNS) $(USER) $(PASS); \
	else \
		echo "Please provide TNS, USER, and PASS parameters"; \
		echo "Example: make test TNS=localhost:1521/XE USER=testuser PASS=testpass"; \
	fi

# Performance test
perf: $(PERF_TARGET)
	@echo "Usage: make perf TNS=<tns> USER=<user> PASS=<password>"
	@if [ -n "$(TNS)" ] && [ -n "$(USER)" ] && [ -n "$(PASS)" ]; then \
		echo "Running performance test with $(USER)@$(TNS)"; \
		./$(PERF_TARGET) $(TNS) $(USER) $(PASS); \
	else \
		echo "Please provide TNS, USER, and PASS parameters"; \
		echo "Example: make perf TNS=localhost:1521/XE USER=testuser PASS=testpass"; \
	fi

.PHONY: all clean test perf
