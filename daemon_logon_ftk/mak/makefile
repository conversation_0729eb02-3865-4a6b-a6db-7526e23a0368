COPY=cp
PROC=proc

# Database connection configuration
# Option 1: Use environment variables (recommended for CI/CD)
# Option 2: Use separate config file (recommended for local development)

# Try to include database config file if it exists
-include db_config.mk
-include oracle_config.mk

# If config file doesn't exist, check environment variables
ifndef DBSTRING
ifdef DB_CONFIG_FILE
$(error Database config file db_config.mk not found. Copy db_config.mk.template to db_config.mk and set your values)
else
$(error DBSTRING not set. Either set environment variables or create db_config.mk from template)
endif
endif

ifndef DBID
$(error DBID not set. Either set environment variables or create db_config.mk from template)
endif

ifndef DBPASS
$(error DBPASS not set. Either set environment variables or create db_config.mk from template)
endif
CC=g++
RM=rm
#CFLAGS = -g -D_DEBUG -lrt
# Legacy CFLAGS (commented for compatibility)
# CFLAGS = -g -lrt -w

# New CFLAGS with OpenSSL compatibility (aligned with CMakeLists.txt)
#CFLAGS = -g -lrt -w -std=gnu++98 -Wno-deprecated-declarations $(OPENSSL_CFLAGS)
CFLAGS = -g -lrt -Wall -std=gnu++11 $(OPENSSL_CFLAGS) -DDEBUG=5

# Legacy path settings (commented for compatibility with existing systems)
# ORG_D=${HOME}/daemon_logon_ftk
# EXT_LIB=/user/neoftk/command_logon_ftk/obj/sms_ctrlsub++.o
# EXT_INC=/user/neoftk/command_logon_ftk/inc

# New path settings (relative paths for current project structure)
ORG_D=..
BIN_D=${ORG_D}/bin
OBJ_D=${ORG_D}/obj
LIB_D=${ORG_D}/lib
INC_D=${ORG_D}/inc
SRC_D=${ORG_D}/src

EXT_LIB=../../command_logon_ftk/obj/sms_ctrlsub++.o
EXT_INC=../../command_logon_ftk/inc

# Legacy settings (commented for compatibility with existing systems)
# KSLIBRARY_PATH=$(HOME)/library
# KSLIBRARY_INC=$(HOME)/library
# ORACLE_VERSION  = 9
# ORACLE_INCLUDES = -I$(ORACLE_HOME)/rdbms/demo -I$(ORACLE_HOME)/rdbms/public
# ORACLE_LIBS     = -L$(ORACLE_HOME)/lib

# New Oracle and library settings (aligned with CMakeLists.txt)
KSLIBRARY_PATH=$(HOME)/library
KSLIBRARY_INC=$(HOME)/library

# Oracle 환경 설정 (환경변수 우선, 기본값 제공)
ORACLE_HOME ?= /usr/lib/oracle/21/client64
TNS_ADMIN ?= $(ORACLE_HOME)/network/admin
NLS_LANG ?= KOREAN_KOREA.AL32UTF8
ORACLE_VERSION ?= 21

# Pro*C 설정 파일 경로
PROC_CONFIG ?= $(ORACLE_HOME)/lib/precomp/admin/pcscfg.cfg

# Oracle include 경로 (버전별 대응)
#ORACLE_INCLUDES = -I/usr/include/oracle/21/client64
ORACLE_INCLUDES ?= -I/usr/include/oracle/$(ORACLE_VERSION)/client64
ORACLE_LIBS = -L$(ORACLE_HOME)/lib

GEN_FLAGS    =  -fno-exceptions -fno-rtti -D_REENTRANT=1
# ODPI-C-specific flags for logonDB (using ODPI-C wrapper)
ODPI_FLAGS   =  -D_REENTRANT=1 -std=c++11
ODPI_LIB_PATH = ../../libsrc/odpi/lib
ODPI_INC_PATH = ../../libsrc/odpi/inc
ODPI_EXTERNAL_INC = ../../libsrc/odpi/external/odpi/include
GEN_INCLUDES = -I$(ODPI_INC_PATH) -I$(ODPI_EXTERNAL_INC)
GEN_LIBS     = $(ODPI_LIB_PATH)/libodpiwrapper.a -ldl -lclntsh -lpthread
RPATH_FLAGS  = -Wl,-rpath,$(ORACLE_HOME)/lib

LOGON_SESSION_OBJ = $(OBJ_D)/logonSession.o \
		    $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/adminUtil.o

SENDERMMSPROCESSDB_OBJ = $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/dbUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/mmsPacketBase.o\
		    $(OBJ_D)/mmsPacketSend.o\
		    $(OBJ_D)/mmsFileProcess.o\
		    $(OBJ_D)/DatabaseORA_MMS.o\
		    $(OBJ_D)/monitor.o\
		    $(OBJ_D)/checkCallback.o\
		    $(OBJ_D)/Encrypt.o
		    
REPORTMMSPROCESSDB_OBJ = $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/mmsPacketBase.o\
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/DatabaseORA_MMS.o\
		    $(OBJ_D)/monitor.o 
		    

# Legacy Oracle settings (commented for compatibility with existing systems)
# ORALIB1 = ${ORACLE_HOME}/lib
# ORALIB2 = ${ORACLE_HOME}/plsql/lib
# ORALIB3 = ${ORACLE_HOME}/network/lib
# ORA_INC = ${ORACLE_HOME}/precomp/public
# INCLUDE =   $(PRECOMPPUBLIC) -I$(INC_D) -I$(ORA_INC) -I/usr/local/openssl/include
# LINKFLAGS = -L$(ORALIB1) -L$(ORALIB2) -L$(ORALIB3) -L/usr/local/openssl/lib
# LIBS = -lnsl -lpthread -lksbase64 -lkssocket -lksconfig -lksthread -lcrypto

# New Oracle and OpenSSL settings (aligned with CMakeLists.txt)
ORALIB1 = $(ORACLE_HOME)/lib
ORALIB2 = $(ORACLE_HOME)/plsql/lib
ORALIB3 = $(ORACLE_HOME)/network/lib
#ORA_INC = /usr/include/oracle/21/client64
ORA_INC ?= /usr/include/oracle/$(ORACLE_VERSION)/client64

# OpenSSL version detection and configuration
OPENSSL_VERSION := $(shell pkg-config --modversion openssl 2>/dev/null || echo "unknown")
OPENSSL_MAJOR := $(shell echo $(OPENSSL_VERSION) | cut -d. -f1)

# OpenSSL path configuration based on version
ifeq ($(OPENSSL_MAJOR),3)
    # OpenSSL 3.x configuration
    OPENSSL_CFLAGS = -DOPENSSL_API_COMPAT=0x10100000L -DOPENSSL_SUPPRESS_DEPRECATED -DROCKY_LINUX_9
    OPENSSL_INCLUDES = -I/usr/include/openssl
    OPENSSL_LINKFLAGS = -L/usr/lib64
    OPENSSL_LIBS = -lcrypto -lssl
else ifeq ($(OPENSSL_MAJOR),1)
    # OpenSSL 1.x configuration
    OPENSSL_CFLAGS = -DOPENSSL_API_COMPAT=0x10100000L -DOPENSSL_SUPPRESS_DEPRECATED
    OPENSSL_INCLUDES = -I/usr/local/openssl/include
    OPENSSL_LINKFLAGS = -L/usr/local/openssl/lib
    OPENSSL_LIBS = -lcrypto -lssl
else
    # Default/fallback configuration
    OPENSSL_CFLAGS = -DOPENSSL_API_COMPAT=0x10100000L -DOPENSSL_SUPPRESS_DEPRECATED
    OPENSSL_INCLUDES = -I/usr/include/openssl
    OPENSSL_LINKFLAGS = -L/usr/lib64
    OPENSSL_LIBS = -lcrypto -lssl
endif

#INCLUDE = $(PRECOMPPUBLIC) -I$(INC_D) -I$(ORA_INC) -I../../libsrc/occi $(OPENSSL_INCLUDES)
INCLUDE = -I$(INC_D) -I$(ORA_INC) $(OPENSSL_INCLUDES)
LINKFLAGS = -L$(ORALIB1) -L$(ORALIB2) -L$(ORALIB3) $(OPENSSL_LINKFLAGS)

ORALIB = -lclntsh

LIBS = -lnsl -lpthread -lksbase64 -lkssocket -lksconfig -lksthread $(OPENSSL_LIBS)

all: 	logonSession \
	logonDB \
	admin \
	monitorProcess \
	adminProcess \
	reportMMSProcDB \
	senderFtalkProDB
	
#senderMMSDB \
#reportMMSDB \
#senderFtalkCpool  \
#	senderMMSProcess \
#	reportMMSProcess \
	
# Pro*C version of logonDB (using DatabaseORA_MMS)
logonDB: $(OBJ_D)/logonDB.o $(OBJ_D)/DatabaseORA_MMS.o $(OBJ_D)/cust_lib_common.o $(OBJ_D)/packetUtil.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} $(ORACLE_LIBS) $(ORALIB) -I${INC_D} -o $(BIN_D)/$@

# ODPI-C version of logonDB (backup)
#logonDB_odpi: $(OBJ_D)/logonDB_odpi_backup.o $(OBJ_D)/cust_lib_common.o $(OBJ_D)/packetUtil.o
#	${CC} $(CFLAGS)  $(ODPI_FLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) $(GEN_INCLUDES) ${LIBS} -L$(KSLIBRARY_PATH)  ${LINKFLAGS} $(ORACLE_LIBS) $(GEN_LIBS) $(RPATH_FLAGS) -I${INC_D} -o $(BIN_D)/$@

senderMMSDB: $(OBJ_D)/senderMMSDB.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} $(ORALIB) -I${INC_D} -o $(BIN_D)/$@

reportMMSDB: $(OBJ_D)/reportMMSDB.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} $(ORALIB) -I${INC_D} -o $(BIN_D)/$@

logonSession: $(LOGON_SESSION_OBJ) 
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} -I${INC_D} -o $(BIN_D)/$@

monitorProcess: $(OBJ_D)/monitorProcess.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} -I${INC_D} -o $(BIN_D)/$@

adminProcess: $(OBJ_D)/adminProcess.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} -I${INC_D} -o $(BIN_D)/$@

senderMMSProcess: $(SENDERMMSPROCESSDB_OBJ) $(OBJ_D)/senderMMSProcess.o
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} $(ORALIB) -I${INC_D} -o $(BIN_D)/$@
	
senderFtalkCpool: $(SENDERMMSPROCESSDB_OBJ) $(OBJ_D)/Curl.o $(OBJ_D)/jsoncpp.o $(OBJ_D)/senderFtalkCpool.o 
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} $(ORALIB) -I${INC_D} -o $(BIN_D)/$@ -lcurl

senderFTkProDB: $(SENDERMMSPROCESSDB_OBJ) $(OBJ_D)/senderFTkProcessDB.o
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} $(ORALIB) -I${INC_D} -o $(BIN_D)/$@

senderFtalkProDB: $(SENDERMMSPROCESSDB_OBJ) $(OBJ_D)/Curl.o $(OBJ_D)/jsoncpp.o $(OBJ_D)/senderFtalkProDB.o 
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} $(ORALIB) -I${INC_D} -o $(BIN_D)/$@ -lcurl

reportMMSProcDB: $(REPORTMMSPROCESSDB_OBJ) $(OBJ_D)/reportMMSProcessDB.o
	${CC}  $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} $(ORALIB) -I${INC_D} -o $(BIN_D)/$@

reportMMSProcess: $(REPORTMMSPROCESSDB_OBJ) $(OBJ_D)/reportMMSProcess.o
	${CC}  $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} $(ORALIB) -I${INC_D} -o $(BIN_D)/$@
	
admin: $(OBJ_D)/admin.o $(OBJ_D)/adminUtil.o
	${CC} $(CFLAGS)  $^ ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket  -lksconfig ${LINKFLAGS} -I${INC_D} -o $(BIN_D)/$@

# DatabaseORA_MMS wrapper compilation rule for logonDB
$(OBJ_D)/logonDB.o: $(SRC_D)/logonDB.cpp
	$(RM) -rf $@
	$(CC) $(CFLAGS) -o $@ $(CFLAGS) $(INCLUDE) -I$(ORACLE_INCLUDES) -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $(SRC_D)/logonDB.cpp

# ODPI-C compilation rule for logonDB (backup)
#$(OBJ_D)/logonDB_odpi_backup.o: $(SRC_D)/logonDB_odpi_backup.cpp
#	$(RM) -rf $@
#	$(CC) $(CFLAGS)   $(ODPI_FLAGS) -o $@ $(CFLAGS) $(INCLUDE) $(GEN_INCLUDES) -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/DatabaseORA_MMS.o: $(LIB_D)/DatabaseORA_MMS.cpp
	$(RM) -rf $(OBJ_D)/DatabaseORA_MMS.*
	$(COPY) $(LIB_D)/DatabaseORA_MMS.cpp $(OBJ_D)/DatabaseORA_MMS.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/DatabaseORA_MMS.pc include=$(INC_D) include=$(ORA_INC) \
include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=SEMANTICS CHAR_MAP=STRING \
config=$(PROC_CONFIG) userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS) -o $(OBJ_D)/DatabaseORA_MMS.o ${LINKFLAGS} $(ORALIB) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/DatabaseORA_MMS.cpp

#export ORACLE_HOME=/usr/lib/oracle/21/client64 && export TNS_ADMIN=/usr/lib/oracle/21/client64/network/admin && export NLS_LANG=KOREAN_KOREA.AL32UTF8 && export LC_ALL=C && $(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/DatabaseORA_MMS.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=SEMANTICS config=/usr/lib/oracle/21/client64/lib/precomp/admin/pcscfg.cfg userid=$(DBID)/$(DBPASS)@$(DBSTRING)
#$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/DatabaseORA_MMS.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=SEMANTICS CHAR_MAP=STRING config=$(PROC_CONFIG) userid=$(DBID)/$(DBPASS)@$(DBSTRING)

$(OBJ_D)/senderMMSProcess.o: $(SRC_D)/senderMMSProcess.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) $(OPENSSL_INCLUDES) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/senderFtalkCpool.o: $(SRC_D)/senderFtalkCpool.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC)  -I /usr/include/curl $(OPENSSL_INCLUDES) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/reportMMSProcess.o: $(SRC_D)/reportMMSProcess.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/senderFTkProcessDB.o: $(SRC_D)/senderFTkProcessDB.cpp
	$(RM) -rf $(OBJ_D)/senderFTkProcessDB.*
	$(COPY) $(SRC_D)/senderFTkProcessDB.cpp $(OBJ_D)/senderFTkProcessDB.pc
	$(PROC) iname=$(OBJ_D)/senderFTkProcessDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=NONE
	$(RM) -rf tp*
	$(CC) $(CFLAGS)   -o $(OBJ_D)/senderFTkProcessDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -I /usr/include/curl $(OPENSSL_INCLUDES) -c $(OBJ_D)/senderFTkProcessDB.cpp

$(OBJ_D)/senderFtalkProDB.o: $(SRC_D)/senderFtalkProDB.cpp
	$(RM) -rf $(OBJ_D)/senderFtalkProDB.*
	$(COPY) $(SRC_D)/senderFtalkProDB.cpp $(OBJ_D)/senderFtalkProDB.pc
	$(PROC) iname=$(OBJ_D)/senderFtalkProDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=SEMANTICS config=$(PROC_CONFIG) userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)   -o $(OBJ_D)/senderFtalkProDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -I /usr/include/curl $(OPENSSL_INCLUDES) -c $(OBJ_D)/senderFtalkProDB.cpp
	
#export ORACLE_HOME=/usr/lib/oracle/21/client64 && export TNS_ADMIN=/usr/lib/oracle/21/client64/network/admin && $(PROC) iname=$(OBJ_D)/senderFtalkProDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=SEMANTICS config=/usr/lib/oracle/21/client64/lib/precomp/admin/pcscfg.cfg userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	
$(OBJ_D)/reportMMSProcessDB.o: $(SRC_D)/reportMMSProcessDB.cpp
	$(RM) -rf $(OBJ_D)/reportMMSProcessDB.*
	$(COPY) $(SRC_D)/reportMMSProcessDB.cpp $(OBJ_D)/reportMMSProcessDB.pc
	$(PROC) iname=$(OBJ_D)/reportMMSProcessDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=FULL userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)   -o $(OBJ_D)/reportMMSProcessDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/reportMMSProcessDB.cpp

$(OBJ_D)/senderMMSDB.o: $(SRC_D)/senderMMSDB.cpp
	$(RM) -rf $(OBJ_D)/senderMMSDB.*
	$(COPY) $(SRC_D)/senderMMSDB.cpp $(OBJ_D)/senderMMSDB.pc
	$(PROC) iname=$(OBJ_D)/senderMMSDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE THREADS=YES SQLCHECK=FULL userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)   -o $(OBJ_D)/senderMMSDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/senderMMSDB.cpp

$(OBJ_D)/reportMMSDB.o: $(SRC_D)/reportMMSDB.cpp
	$(RM) -rf $(OBJ_D)/reportMMSDB.*
	$(COPY) $(SRC_D)/reportMMSDB.cpp $(OBJ_D)/reportMMSDB.pc
	$(PROC) iname=$(OBJ_D)/reportMMSDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE THREADS=YES CTIMEOUT=3 SQLCHECK=FULL userid=$(DBID)/$(DBPASS)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)  -o $(OBJ_D)/reportMMSDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/reportMMSDB.cpp

$(OBJ_D)/logonSession.o: $(SRC_D)/logonSession.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/monitorProcess.o: $(SRC_D)/monitorProcess.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^
	
$(OBJ_D)/adminProcess.o: $(SRC_D)/adminProcess.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/admin.o: $(SRC_D)/admin.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^
	
$(OBJ_D)/mmsPacketUtil.o: $(LIB_D)/mmsPacketUtil.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/mmsPacketSend.o: $(LIB_D)/mmsPacketSend.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/mmsFileProcess.o: $(LIB_D)/mmsFileProcess.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/mmsPacketBase.o: $(LIB_D)/mmsPacketBase.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/adminUtil.o: $(LIB_D)/adminUtil.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/monitor.o: $(LIB_D)/monitor.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/cust_lib_common.o: $(LIB_D)/cust_lib_common.c
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -c $^

$(OBJ_D)/logonUtil.o: $(LIB_D)/logonUtil.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/dbUtil.o: $(LIB_D)/dbUtil.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/packetUtil.o: $(LIB_D)/packetUtil.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/checkCallback.o: $(LIB_D)/checkCallback.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/Encrypt.o: $(LIB_D)/Encrypt.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} $(OPENSSL_INCLUDES) -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^
	
$(OBJ_D)/Curl.o: $(LIB_D)/Curl.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I /usr/include/curl -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^	
	
$(OBJ_D)/jsoncpp.o: $(LIB_D)/jsoncpp.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^		

clean:
	rm  -rf $(OBJ_D)/*.o tp* $(OBJ_D)/*.lis $(OBJ_D)/*.pc $(OBJ_D)/*.cpp
