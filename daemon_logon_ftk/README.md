# Database Configuration for daemon_logon_ftk

이 프로젝트는 Pro*C 컴파일 시 데이터베이스 연결 정보가 필요합니다. 보안을 위해 데이터베이스 정보를 하드코딩하지 않고 다음 두 가지 방법 중 하나를 사용할 수 있습니다.

## Makefile 빌드

### 방법 1: 환경변수 사용 (권장 - CI/CD 환경)

빌드 전에 다음 환경변수를 설정하세요:

```bash
export DBSTRING=your_database_string
export DBID=your_database_id
export DBPASS=your_database_password

cd mak
make
```

### 방법 2: 설정 파일 사용 (권장 - 로컬 개발)

1. 템플릿 파일을 복사하여 설정 파일을 생성:
```bash
cd mak
cp db_config.mk.template db_config.mk
```

2. `db_config.mk` 파일을 편집하여 실제 데이터베이스 정보를 입력:
```makefile
DBSTRING=actual_database_string
DBID=actual_database_id
DBPASS=actual_database_password
```

3. 빌드 실행:
```bash
make
```

## CMake 빌드

### 방법 1: 환경변수 사용 (권장 - CI/CD 환경)

```bash
export DBSTRING=your_database_string
export DBID=your_database_id
export DBPASS=your_database_password

mkdir -p build
cd build
cmake ..
make
```

### 방법 2: 설정 파일 사용 (권장 - 로컬 개발)

1. 템플릿 파일을 복사하여 설정 파일을 생성:
```bash
cp db_config.cmake.template db_config.cmake
```

2. `db_config.cmake` 파일을 편집하여 실제 데이터베이스 정보를 입력:
```cmake
set(DBSTRING "actual_database_string")
set(DBID "actual_database_id")
set(DBPASS "actual_database_password")
```

3. CMake 빌드 실행:
```bash
mkdir -p build
cd build
cmake ..
make
```

## 우선순위

환경변수가 설정 파일보다 우선순위가 높습니다:
1. 환경변수 (최우선)
2. 설정 파일 (db_config.mk 또는 db_config.cmake)
3. 둘 다 없으면 빌드 오류

## 주의사항

- 설정 파일들은 `.gitignore`에 포함되어 있어 버전 관리에서 제외됩니다.
- 실제 데이터베이스 정보를 포함한 파일을 절대 커밋하지 마세요.
- 환경변수나 설정 파일 중 어느 것도 설정되지 않으면 빌드 시 오류가 발생합니다.

## 빌드 타겟

### Makefile 타겟:
- `make all`: 모든 실행파일 빌드
- `make logonSession`: logonSession 빌드
- `make logonDB`: logonDB 빌드
- `make admin`: admin 빌드
- `make senderMMSDB`: senderMMSDB 빌드
- `make reportMMSDB`: reportMMSDB 빌드
- `make clean`: 빌드 파일 정리

### CMake 타겟:
- 모든 실행파일이 자동으로 빌드됩니다
- 개별 타겟: `make logonSession`, `make logonDB`, `make admin` 등

## Oracle 환경 요구사항

- Oracle Instant Client 21c
- Pro*C 컴파일러
- 적절한 ORACLE_HOME 환경변수 설정
