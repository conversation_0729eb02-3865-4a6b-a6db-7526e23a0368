/*
 * logonDB_simple_wrapper.cpp - Simple wrapper using DatabaseORA_MMS
 * Uses Pro*C methods from DatabaseORA_MMS for database operations
 */

#include "DatabaseORA_MMS.h"
#include "kssocket.h"
#include "packetUtil.h"

using namespace KSKYB;

// Global variables
int activeProcess = TRUE;
struct _message_info message_info;
struct _shm_info *shm_info;
char PROCESS_NO[7];
char PROCESS_NAME[36];
CConfigLogonDB gConf;
char _DATALOG[64];

// Database instance
CDatabaseORA* g_database = NULL;

// Function implementations
int proc_connect(void) {
    if (g_database == NULL) {
        g_database = new CDatabaseORA();
    }
    
    char connect_string[256];
    sprintf(connect_string, "%s/%s@%s", gConf.dbID, gConf.dbPASS, gConf.dbSID);
    
    int result = g_database->connectToOracle(gConf.dbID, gConf.dbPASS);
    if (result <= 0) {
        log_history(0, 0, "[ERR] Database connection failed");
        return -1;
    }
    
    log_history(0, 0, "[INF] Database connected successfully");
    return 0;
}

int proc_disconnect(void) {
    if (g_database != NULL) {
        g_database->closeFromOracle();
        delete g_database;
        g_database = NULL;
    }
    return 0;
}

int checkLogon(void) {
    CKSSocket socket;
    CPacketUtil packetUtil;
    
    // Socket 초기화 및 바인드
    if (!socket.Create()) {
        log_history(0, 0, "[ERR] Socket create failed");
        return -1;
    }
    
    if (!socket.Bind(atoi(PROCESS_NO))) {
        log_history(0, 0, "[ERR] Socket bind failed");
        return -1;
    }
    
    if (!socket.Listen()) {
        log_history(0, 0, "[ERR] Socket listen failed");
        return -1;
    }
    
    log_history(0, 0, "[INF] LogonDB listening on port %s", PROCESS_NO);
    
    while (activeProcess) {
        CKSSocket clientSocket;
        
        if (!socket.Accept(clientSocket)) {
            if (activeProcess) {
                log_history(0, 0, "[ERR] Socket accept failed");
            }
            continue;
        }
        
        // 클라이언트 요청 처리
        char buffer[SOCKET_BUFF];
        memset(buffer, 0x00, sizeof(buffer));
        
        int received = clientSocket.Receive(buffer, sizeof(buffer) - 1);
        if (received <= 0) {
            log_history(0, 0, "[ERR] Receive failed");
            clientSocket.Close();
            continue;
        }
        
        log_history(0, 0, "[INF] Received logon request: %d bytes", received);
        
        // 패킷에서 CID, PWD 추출
        char szCID[64] = {0};
        char szPWD[64] = {0};
        
        if (packetUtil.findValue(buffer, "CID", szCID) <= 0 ||
            packetUtil.findValue(buffer, "PWD", szPWD) <= 0) {
            log_history(0, 0, "[ERR] Invalid packet format");
            clientSocket.Close();
            continue;
        }
        
        log_history(0, 0, "[INF] Processing logon for CID[%s]", szCID);
        
        // 데이터베이스 연결 확인
        if (g_database == NULL) {
            if (proc_connect() != 0) {
                log_history(0, 0, "[ERR] Database connection failed");
                clientSocket.Close();
                continue;
            }
        }
        
        // 프로시저 호출 변수들
        char ot_appname[256] = {0};
        char ot_sip[64] = {0};
        int ot_pid = 0;
        int ot_job = 0;
        int ot_c_job = 0;
        int ot_prt = 0;
        int ot_cnt = 0;
        int ot_rpt_cnt = 0;
        char ot_server_info[512] = {0};
        int ot_rpt_sleep_cnt = 3;
        char ot_sender_proc[256] = {0};
        char ot_report_proc[256] = {0};
        char ot_senderdb_info[512] = {0};
        char ot_reportdb_info[512] = {0};
        char ot_logfile_info[512] = {0};
        char ot_etc[512] = {0};
        int ot_rst = -999;
        char ot_rstmsg[512] = {0};
        
        // 첫 번째 프로시저 호출
        int result1 = g_database->procCheckMmsLoginExt2(
            szCID, szPWD,
            ot_appname, ot_sip, &ot_pid, &ot_job, &ot_c_job, &ot_prt, &ot_cnt, &ot_rpt_cnt,
            ot_server_info, &ot_rpt_sleep_cnt, ot_sender_proc, ot_report_proc,
            ot_senderdb_info, ot_reportdb_info, ot_logfile_info, ot_etc,
            &ot_rst, ot_rstmsg
        );
        
        if (result1 <= 0) {
            log_history(0, 0, "[ERR] proc_check_mms_login_ext2 failed");
            clientSocket.Close();
            continue;
        }
        
        log_history(0, 0, "[INF] proc_check_mms_login_ext2 executed successfully, nmRST[%d]", ot_rst);
        
        if (ot_rst == 0) {
            // 두 번째 프로시저 호출
            char szLimitType[256] = {0};
            int nDayWarnCnt = 0;
            int nDayLimitCnt = 0;
            int nMonWarnCnt = 0;
            int nMonLimitCnt = 0;
            char szLimitFlag[256] = {0};
            int nDayAccCnt = 0;
            int nMonAccCnt = 0;
            int nmRST2 = -999;
            char szErrMsg2[512] = {0};
            
            int result2 = g_database->procGetLimitDef(
                ot_pid, szLimitType, &nDayWarnCnt, &nDayLimitCnt, &nMonWarnCnt, &nMonLimitCnt,
                szLimitFlag, &nDayAccCnt, &nMonAccCnt, &nmRST2, szErrMsg2
            );
            
            if (result2 <= 0) {
                log_history(0, 0, "[ERR] proc_get_limit_def failed");
            } else {
                log_history(0, 0, "[INF] proc_get_limit_def executed successfully, nmRST[%d]", nmRST2);
            }
        }
        
        // 응답 패킷 생성
        TypeMsgBindAck response;
        memset(&response, 0x00, sizeof(response));
        
        strcpy(response.header.msgType, "ACK");
        sprintf(response.header.result, "%d", ot_rst);
        
        sprintf(response.data, 
            "RST=%d&MSG=%s&APPNAME=%s&SIP=%s&PID=%d&JOB=%d&CNT=%d&SERVER=%s&SENDER=%s&REPORT=%s",
            ot_rst, ot_rstmsg, ot_appname, ot_sip, ot_pid, ot_job, ot_cnt, 
            ot_server_info, ot_sender_proc, ot_report_proc);
        
        response.header.leng = strlen(response.data);
        
        // 응답 전송
        int sent = clientSocket.Send((char*)&response, sizeof(response.header) + response.header.leng);
        if (sent <= 0) {
            log_history(0, 0, "[ERR] Send response failed");
        } else {
            log_history(0, 0, "[INF] Response sent successfully: %d bytes", sent);
        }
        
        clientSocket.Close();
    }
    
    socket.Close();
    return 0;
}

// 메인 함수
int main(int argc, char* argv[]) {
    if (argc < 4) {
        printf("Usage: %s <process_no> <db_id> <db_pass> [db_sid]\n", argv[0]);
        return -1;
    }
    
    // 프로세스 정보 설정
    strcpy(PROCESS_NO, argv[1]);
    strcpy(PROCESS_NAME, "logonDB");
    strcpy(_DATALOG, "LOGON_DB");
    
    // 데이터베이스 설정
    strcpy(gConf.dbID, argv[2]);
    strcpy(gConf.dbPASS, argv[3]);
    if (argc >= 5) {
        strcpy(gConf.dbSID, argv[4]);
    } else {
        strcpy(gConf.dbSID, "NEO226");
    }
    
    log_history(0, 0, "[INF] LogonDB starting - Process[%s] DB[%s@%s]", 
                PROCESS_NO, gConf.dbID, gConf.dbSID);
    
    // 시그널 핸들러 설정
    signal(SIGTERM, [](int sig) { activeProcess = FALSE; });
    signal(SIGINT, [](int sig) { activeProcess = FALSE; });
    
    // 메인 로직 실행
    int result = checkLogon();
    
    // 정리
    proc_disconnect();
    
    log_history(0, 0, "[INF] LogonDB terminated with code %d", result);
    
    return result;
}
