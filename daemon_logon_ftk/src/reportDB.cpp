#include "reportDB.h"
#include <queue>
#include <list>
#include <semaphore.h> 

EXEC SQL BEGIN DECLARE SECTION;
#define MAX_DB_CONNECTION 4
EXEC SQL END DECLARE SECTION;

void Init_Oracle(sql_context ctx);

void* doService(void* param);
int getReportDB(sql_context pDB,struct sqlca sqlca, char* buff,CReportDbInfo& reportDbInfo);
int setReportDB(sql_context pDB,struct sqlca sqlca, char* buff,CReportDbInfo& reportDbInfo);
void* doDBError(void* param);
int errorDBprocess(void* pDB);
int offerInfo(CKSSocket& newSockfd);



queue<void*, list<void*> > dbConnQ;
std::list <CThreadInfo*> listThreadHandle;
typedef std::list <CThreadInfo*>::iterator listPosition;


sem_t m_sem;

int main(int argc, char* argv[])
{
    int ret;
    char logMsg[SOCKET_BUFF];
    struct timeval outtime;
    CKSSocket svrSockfd;
    CKSSocket newSockfd;
    CKSThread ksthread;
    CThreadInfo* pThreadInfo;
    char buff[SOCKET_BUFF];
    int hNewSocket;
    TypeMsgBindSnd* pLogonData;
    int i;
    CKSConfig conf;

    listPosition pos;
    listPosition posPrev;
    int nThreadCount = 0;



    EXEC SQL BEGIN DECLARE SECTION;
    sql_context db[MAX_DB_CONNECTION];
    EXEC SQL END DECLARE SECTION;



    if (ml_sub_init(PROCESS_NO, PROCESS_NAME,(char*)0,(char*)0)<0) {
        perror("ml_sub_init Error.");
        ml_sub_end();
        exit(1);
    }



    ret = configParse(argv[1]);
    if( ret != 0 )
    {
        exit(1);
    }

    log_history(0,0,"[%s]",
            gConf.reportDBName);


    sprintf(logMsg,"%s START",PROCESS_NAME);
    monitoring(logMsg,0,0);

    Init_Server();









    EXEC SQL ENABLE THREADS;
    for(i=0;i<MAX_DB_CONNECTION;i++)
    {
        EXEC SQL CONTEXT ALLOCATE :db[i];

        Init_Oracle(db[i]);
        if( sqlca.sqlcode !=0 )
        {
            monitoring("db connection fail error",0,errno);
            ml_sub_end();
            return -1;
        }

        log_history(0,0,"db[%x]",db[i]);
        dbConnQ.push(db[i]);
    }




    sem_init(&m_sem,0,1);

    ret = svrSockfd.createDomainNon(gConf.reportDBName);
    if( ret !=0 ) {
        log_history(0,0,"DOMAIN_REPORTDB 생성실패 [%s]",
                strerror(errno),
                gConf.reportDBName);
        goto END;
    }

    

    while(activeProcess)
    {
        wait_a_moment(1000);
        pos = listThreadHandle.begin();
        nThreadCount = 0;
        while( pos != listThreadHandle.end() )
        {
            nThreadCount++;
            posPrev = pos++;

            if( (*posPrev)->tid <= 0 )
            {
                log_history(0,0,"bug--------------------------[%d]",(*posPrev)->tid);
                    listThreadHandle.erase(posPrev);
                continue;
            }

            ret =  pthread_kill((*posPrev)->tid,0);

            switch (ret) {
                case 0 : 
                    break;
                case ESRCH:
                    /*
            log_history(0,0,"pthread join 1-1 Error [%d] [%s] [%d][%d]",
                    ret,
                    strerror(errno),
                    nThreadCount,
                    listThreadHandle.size());
                    */


                    pthread_join((*posPrev)->tid,NULL);
                    listThreadHandle.erase(posPrev);
                    delete (CThreadInfo*)(*posPrev);
                    
/*                    log_history(0,0,"pthread join 1-1 End");
 */
                    break;
                case EINVAL:
/*            log_history(0,0,"pthread join 2 Error [%d] [%s]",
                    ret,
                    strerror(errno));
                    */

                    pthread_join((*posPrev)->tid,NULL);
                    listThreadHandle.erase(posPrev);
                    delete (CThreadInfo*)(*posPrev);
                    break;
                default:
            log_history(0,0,"pthread check  Error [%d] [%s]",
                    ret,
                    strerror(errno));

                    pthread_join((*posPrev)->tid,NULL);
                    listThreadHandle.erase(posPrev);
                    delete (CThreadInfo*)(*posPrev);
                    break;
            }
        }


        hNewSocket = svrSockfd.accept();
        if( hNewSocket <= 0 )
            continue;


        /* new connection  */
        pThreadInfo = NULL;
        pThreadInfo = new CThreadInfo;
        if( pThreadInfo == NULL )
        {
            log_history(0,0,"new ThreadInfo Error [%s]",
                    strerror(errno));
            close(hNewSocket);
            continue;
        }

/*    log_history(0,0,"org[%x]",pThreadInfo); */
        pThreadInfo->sock = hNewSocket;
        ret = ksthread.create(&(pThreadInfo->tid),NULL,doService,(void*)pThreadInfo);
        if( ret != 0 )
        {
            log_history(0,0,"create thread Error [%s]",
                    strerror(errno));
            close(pThreadInfo->sock);
            delete pThreadInfo;
            continue;
        }
/*
        log_history(0,0,"pthread_t tid [%d] ret[%d]",
                pThreadInfo->tid,
                ret);
*/

        listThreadHandle.push_back( pThreadInfo);
/*        delete pThreadInfo; */

    }

END:
    svrSockfd.close();
        pos = listThreadHandle.begin();
        nThreadCount = 0;
        while( pos != listThreadHandle.end() )
        {
            nThreadCount++;
            posPrev = pos++;
/*            if( (pthread_t)(*posPrev) <= 0 ) continue; */
            ret =  pthread_kill((*posPrev)->tid,0);

            switch (ret) {
                case 0 : /* thread is alive */     
            log_history(0,0,"thread closing [%d]",(pthread_t)(*posPrev));
                    pthread_join((*posPrev)->tid,NULL);
                    listThreadHandle.erase(posPrev);
                    delete (CThreadInfo*)(*posPrev);
                    break;
                case ESRCH:
                case EINVAL:
                    /* close thread */

                    pthread_join((*posPrev)->tid,NULL);
                    listThreadHandle.erase(posPrev);

                    delete (CThreadInfo*)(*posPrev);
                    break;
                default:

                    pthread_join((*posPrev)->tid,NULL);
                    listThreadHandle.erase(posPrev);

                    delete (CThreadInfo*)(*posPrev);
                    break;
            }
        }




    sem_destroy(&m_sem);

    EXEC SQL BEGIN DECLARE SECTION;
    sql_context pDB;
    EXEC SQL END DECLARE SECTION;


    sleep(2);
    while( dbConnQ.size()>0 )
    {

        pDB = dbConnQ.front();
        if( pDB == NULL ) break;
        EXEC SQL CONTEXT USE :pDB;
        EXEC SQL COMMIT WORK RELEASE;
        EXEC SQL CONTEXT FREE :pDB;
        dbConnQ.pop();
    }

    
    sprintf(logMsg,"%s CLOSE",PROCESS_NAME);
    monitoring(logMsg,0,0);
    ml_sub_end();

    return 0;
}


void* doService(void* param)
{
    int ret;
    CKSSocket newSockfd;
    char buff[SOCKET_BUFF];
    CThreadInfo* info = (CThreadInfo*)param;
    CReportDbInfo reportDbInfo;
    CReportDbInfo* pReportDbInfo;

    void* pDB;
    struct sqlca sqlca;


    newSockfd.attach(info->sock);
    CCL(buff);

    ret = newSockfd.rcvmsg(buff);
    if( ret == 0 ) {
        newSockfd.close();
        log_history(0,0,"connection 후 3초간 데이터가 없어 연결을 close 합니다");
        return NULL;
    }


    if( ret < 0 ) {
        newSockfd.close();
        log_history(0,0,"recv Error");
        return NULL;
    }


    if( memcmp(buff,"getInfo",7) == 0 )
    {
        offerInfo(newSockfd);
        newSockfd.close();
        return NULL;
    }




/*    log_history(0,0,"doService ... start");
 */
REGETDB:
    while( sem_wait(&m_sem) == -1 )
    {
        if(errno != EINTR )
        { 
            newSockfd.close();
            log_history(0,0,"세마포어 wait Error");
            return NULL;
        }
    }


    if( dbConnQ.size() > 0 )
    {
        pDB = dbConnQ.front();
        if( pDB != NULL ) 
        {
            log_history(0,0,"pop NOT NULL pDB[%x] [%d]", pDB,dbConnQ.size()); 
            dbConnQ.pop();
        }
    } else {
        pDB = NULL;
    }


    if( sem_post(&m_sem) == -1 )
    {
        newSockfd.close();
        log_history(0,0,"세마포어 해제 Error [%s]",strerror(errno));
        return NULL;
    }



    if( pDB == NULL )
    {
        ret = 0;
        log_history(0,0,"NODATA[%s]",strerror(errno));
        goto NODATA;
    }

    memset(&reportDbInfo,0x00,sizeof(reportDbInfo));


    alarm(15);

    pReportDbInfo = (CReportDbInfo*)buff;
/*    viewPack(buff,sizeof(buff));
 */

    switch(atoi(pReportDbInfo->header.msgType))
    {
        case 1: /* 전송결과 요청 */
            ret = getReportDB(pDB,sqlca,buff,reportDbInfo);
            if( ret < 0 )
            {
                newSockfd.close();
                log_history(0,0,"getReportDB Error");
                ret = errorDBprocess(pDB);
                alarm(0);
                return NULL;
            }

            break;
        case 3: /* 레포트 재입력 */
            ret = setReportDB(pDB,sqlca,buff,reportDbInfo);
            if( ret < 0 )
            {
                newSockfd.close();
                log_history(0,0,"setReportDB Error");
                ret = errorDBprocess(pDB);
                alarm(0);
                return NULL;
            }
            log_history(0,0,"setReportDB Succ");
            break;
        default:
            ret = -1;
            log_history(0,0,"잘못된 type 요청 CReportDbInfo.header.msgType [%s]",
                    pReportDbInfo->header.msgType);
            break;
    }
    alarm(0);



    if( ret < 0 )
    {
        newSockfd.close();
        while( sem_wait(&m_sem) == -1 )
            if(errno != EINTR )
            { 
                newSockfd.close();
                log_history(0,0,"세마포어 wait Error");
                return NULL;
            }


        dbConnQ.push(pDB);


        if( sem_post(&m_sem) == -1 )
        {
            newSockfd.close();
            log_history(0,0,"세마포어 해제 Error [%s]",strerror(errno));
            return NULL;
        }
        return NULL;
    }


    if( ret == 0 )
        memcpy(reportDbInfo.szResCode,"99",2);



    ret = newSockfd.send((char*)&reportDbInfo,sizeof(reportDbInfo));
    if( ret == sizeof(reportDbInfo))
    {
        /* commit */
        EXEC SQL CONTEXT USE :pDB;
        EXEC SQL COMMIT;

    }
    else 
    {
        log_history(0,0,"domain write Error ret[%d] [%s] szResCode[%s]",
                ret,strerror(errno),
                reportDbInfo.szResCode);
        if( memcmp(reportDbInfo.szResCode,"99",2) != 0 &&  memcmp(reportDbInfo.szResCode,"98",2) != 0 )
        {
            /* rollback */
            EXEC SQL CONTEXT USE :pDB;
            EXEC SQL ROLLBACK;
            log_history(0,0,"rollback [%d][%s]",reportDbInfo.nMsgId,reportDbInfo.szResCode);
        }
    }


    while( sem_wait(&m_sem) == -1 )
        if(errno != EINTR )
        { 
            newSockfd.close();
            log_history(0,0,"세마포어 wait Error");
            return NULL;
        }


    dbConnQ.push(pDB);


    if( sem_post(&m_sem) == -1 )
    {
        newSockfd.close();
        log_history(0,0,"세마포어 해제 Error [%s]",strerror(errno));
        return NULL;
    }


 

    newSockfd.close();

    return NULL;

NODATA:
    if( ret == 0 )
        memcpy(reportDbInfo.szResCode,"99",2);



    ret = newSockfd.send((char*)&reportDbInfo,sizeof(reportDbInfo));
    if( ret != sizeof(reportDbInfo))
    {
        log_history(0,0,"domain write Error is NODATA ret[%d] [%s]",
                ret,strerror(errno));
    }

    newSockfd.close();

    return NULL;
}

int setReportDB(sql_context pDB,struct sqlca sqlca, char* buff ,CReportDbInfo& reportDbInfo)
{
    EXEC SQL BEGIN DECLARE SECTION;
    char szPtnsn[16+1];
    char szResCode[16+1];
    int nTelcoId =-1;
    char szResvData[200+1];
    char szEndTelco[8];
    int nMsgId=-1;
    char szRptDate[16];
    int nJobCode=-1;
    int nSqlCode=-1;

    int nCnt = -1;
    char szSqlErrorMsg[1024];


    char szAppName[16];
    char szSerial[16+1];
    char szDstadr[16+1];
    int nPtnId = -1;

    EXEC SQL END DECLARE SECTION;

    CReportDbInfo* pReportDbInfo = (CReportDbInfo*)buff;

    CCL(szPtnsn);
    CCL(szResCode);
    CCL(szResvData);
    CCL(szDstadr);
    CCL(szEndTelco);
    CCL(szRptDate);
    CCL(szSqlErrorMsg);
    CCL(szAppName);
    
    strcpy(szPtnsn,pReportDbInfo->szPtnsn);
    strcpy(szResCode,pReportDbInfo->szResCode);
    strcpy(szResvData,pReportDbInfo->szResvData);
    strcpy(szDstadr,pReportDbInfo->szDstAdr);
    strcpy(szEndTelco,pReportDbInfo->szEndTelco);
    strcpy(szRptDate,pReportDbInfo->szRptDate);

    nTelcoId = pReportDbInfo->nTelcoId;
    nMsgId = pReportDbInfo->nMsgId;
    nJobCode = pReportDbInfo->nJobCode;
    nCnt = pReportDbInfo->nCnt;
    nSqlCode = -1;
    nPtnId = pReportDbInfo->nPtnId;

    strcpy(szAppName,pReportDbInfo->szAppName);
    
    log_history(0,0,"set report appname[%s]ptnsn[%s]res[%s]tel[%d]resv[%s][%s][%s][%d][%s][%d][%d]       [%x]",
            szAppName,
            szPtnsn,
            szResCode,
            nTelcoId,
            szResvData,
            szDstadr,
            szEndTelco,
            nMsgId,
            szRptDate,
            nPtnId,
            nJobCode,
            pDB);
    
    EXEC SQL CONTEXT USE :pDB;
    EXEC SQL EXECUTE
        BEGIN
        proc_set_report_time_v2(
                in_appname=>:szAppName,
                in_ptn_sn=>:szPtnsn,
                in_res_code=>:szResCode,
                in_telco_id=>:nTelcoId,
                in_resv_data=>:szResvData,
                in_dstaddr=>:szDstadr,
                in_end_telco=>:szEndTelco,
                in_msg_id=>:nMsgId,
                in_telco_rpt_date=>:szRptDate,
                in_ptn_id=>:nPtnId,
                in_job_code=>:nJobCode,
                ot_sqlcode=>:nSqlCode,
                ot_sqlmsg=>:szSqlErrorMsg); 
    END;
    END-EXEC;


    if( nSqlCode != 0 )
    {
        log_history(0,0,"proc_set_report_time_v2 Error :[%d][%s][%x]",
                nSqlCode,
                szSqlErrorMsg,
                pDB);
    }

    strcpy(pReportDbInfo->header.msgType,"4");
    memcpy((char*)&reportDbInfo,(char*)pReportDbInfo,sizeof(CReportDbInfo));

    
    return 1;
}



int getReportDB(sql_context pDB,struct sqlca sqlca, char* buff ,CReportDbInfo& reportDbInfo)
{
    EXEC SQL BEGIN DECLARE SECTION;
    char szPtnsn[16+1];
    char szResCode[16+1];
    int nTelcoId =-1;
    char szResvData[512];
    char szEndTelco[8];
    int nMsgId=-1;
    char szRptDate[16];
    int nJobCode=-1;
    int nSqlCode=-1;

    int nCnt = -1;
    char szSqlErrorMsg[1024];


    char szAppName[16];
    char szSerial[16+1];
    char szDstadr[16+1];

    int nPtnId = -1;

    EXEC SQL END DECLARE SECTION;

    CReportDbInfo* pReportDbInfo = (CReportDbInfo*)buff;

    CCL(szPtnsn);
    CCL(szResCode);
    CCL(szResvData);
    CCL(szDstadr);
    CCL(szEndTelco);
    CCL(szRptDate);
    CCL(szSqlErrorMsg);
    CCL(szAppName);

    strcpy(szAppName,pReportDbInfo->szResvData);
    memcpy(szResCode,"Init",4);
    
    log_history(0,0,"appname[%s][%x]",szAppName,pDB);


    EXEC SQL CONTEXT USE :pDB;
    EXEC SQL EXECUTE
        BEGIN
        proc_get_report_time_v3(
                in_appname=>:szAppName,
                ptn_sn=>:szPtnsn,
                res_code=>:szResCode,
                telco_id=>:nTelcoId,
                resv_data=>:szResvData,
                dstaddr=>:szDstadr,
                end_telco=>:szEndTelco,
                msg_id=>:nMsgId,
                telco_rpt_date=>:szRptDate,
                ot_ptn_id=>:nPtnId,
                ot_job_code=>:nJobCode,
                ot_cnt=>:nCnt,
                ot_sqlcode=>:nSqlCode,
                ot_sqlmsg=>:szSqlErrorMsg); 
    END;
    END-EXEC;

    if( memcmp(szResCode,"Init",4) == 0 )
    {
        log_history(0,0,"proc_get_report_time sqlcode[%d] sqlmsg[%s][%s]", 
                sqlca.sqlcode,
                trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc)),
                    szAppName
                );
        return -1;
    }


    if( memcmp(szResCode,"99",2) == 0 || memcmp(szResCode,"98",2) == 0 )
        return 0;

    trim(szPtnsn,sizeof(szPtnsn));
    trim(szResvData,sizeof(szResvData));
    trim(szDstadr,sizeof(szDstadr));
    trim(szEndTelco,sizeof(szEndTelco));
    trim(szRptDate,sizeof(szRptDate));

    strcpy(reportDbInfo.header.msgType,"2");
    memcpy(reportDbInfo.szPtnsn,szPtnsn,strlen(szPtnsn));
    memcpy(reportDbInfo.szResCode,szResCode,2);
    memcpy(reportDbInfo.szAppName,szAppName,strlen(szAppName));
    reportDbInfo.nTelcoId = nTelcoId;
    memcpy(reportDbInfo.szResvData,szResvData,strlen(szResvData));
    memcpy(reportDbInfo.szDstAdr,szDstadr,strlen(szDstadr));
    memcpy(reportDbInfo.szEndTelco,szEndTelco,strlen(szEndTelco));
    reportDbInfo.nMsgId = nMsgId;
    memcpy(reportDbInfo.szRptDate,szRptDate,strlen(szRptDate));
    reportDbInfo.nJobCode = nJobCode;
    reportDbInfo.nCnt = nCnt;
    reportDbInfo.nSqlCode = nSqlCode;
    reportDbInfo.nPtnId = nPtnId;

    return 1;
}



void Init_Oracle(sql_context ctx)
{
    EXEC SQL BEGIN DECLARE SECTION;
    VARCHAR Username[10];
    VARCHAR Password[10];
    VARCHAR dbstring[10];
    EXEC SQL END DECLARE SECTION;

    strcpy((char*)Username.arr, gConf.dbID);
    Username.len = strlen((char*)Username.arr);
    strcpy((char*)Password.arr, gConf.dbPASS);
    Password.len = strlen((char*)Password.arr);
    strcpy((char*)dbstring.arr, gConf.dbSID);
    dbstring.len = strlen((char*)dbstring.arr);

    EXEC SQL CONTEXT USE :ctx;
    EXEC SQL CONNECT :Username IDENTIFIED BY :Password USING :dbstring ;
}


int errorDBprocess(void* pDB)
{
    CKSThread dbErr;
    int ret;
    pthread_t tid;

    ret = dbErr.create(&(tid),NULL,doDBError,(void*)pDB);
    if( ret != 0 )
    {
        log_history(0,0,"create thread Error [%s] Current queSize[%d]",
                strerror(errno),
                dbConnQ.size());
    }

    return -1;
}


void* doDBError(void* param)
{

     pthread_detach(pthread_self()); 
    struct sqlca sqlca;
    EXEC SQL BEGIN DECLARE SECTION;
    sql_context pDB = (sql_context)param;
    EXEC SQL END DECLARE SECTION;


DBERRCONN:
    EXEC SQL CONTEXT USE :pDB;
    EXEC SQL COMMIT WORK RELEASE;
    EXEC SQL CONTEXT FREE :pDB;


    if( activeProcess == false ) return NULL;

    EXEC SQL CONTEXT ALLOCATE :pDB;

    Init_Oracle(pDB);
    if( sqlca.sqlcode !=0 )
    {
        monitoring("db connection fail error",0,errno);
        wait_a_moment(900000);
        goto DBERRCONN;
    }

    log_history(0,0,"new db[%x]",pDB);
    while( sem_wait(&m_sem) == -1 )
        if(errno != EINTR )
        { 
            log_history(0,0,"세마포어 wait Error");
            wait_a_moment(900000);
            goto DBERRCONN;
        }
    /* 크리티컬 섹션 */
    dbConnQ.push(pDB);

    if( sem_post(&m_sem) == -1 )
    {
        log_history(0,0,"세마포어 해제 Error [%s][%x]",
                strerror(errno),
                pDB);
        return NULL;
    }

    log_history(0,0,"current queSize[%d]",dbConnQ.size());

    return NULL;
}

int offerInfo(CKSSocket& newSockfd)
{
    char szQueSize[4];
    CCL(szQueSize);
    sprintf(szQueSize,"%d",dbConnQ.size());
    newSockfd.send(szQueSize,strlen(szQueSize));
    return 0;
}

int configParse(char* file)
{
    Q_Entry *pEntry;
    int  i;
    int  nRetFlag = TRUE;
    char *pszTmp;
    CKSConfig conf;
//    memset(&gConf,0x00,sizeof(gConf));

    // read mert conf
    if((pEntry = conf.qfDecoder(file)) == NULL) {
        printf("WARNING: Configuration file(%s) not found.\n", file);
        return -1;
    }

    conf.strncpy2(gConf.reportDBName , conf.FetchEntry("domain.reportDB"),64);
    if( gConf.reportDBName == NULL ) strcpy(gConf.reportDBName,"");


    conf.strncpy2(gConf.dbID , conf.FetchEntry("db.id"),16);
    if( gConf.dbID == NULL ) strcpy(gConf.dbID,"");

    conf.strncpy2(gConf.dbPASS , conf.FetchEntry("db.pass"),16);
    if( gConf.dbPASS == NULL ) strcpy(gConf.dbPASS,"");

    conf.strncpy2(gConf.dbSID , conf.FetchEntry("db.sid"),16);
    if( gConf.dbSID == NULL ) strcpy(gConf.dbSID,"");





    return 0;
}



