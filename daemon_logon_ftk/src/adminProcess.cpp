#include "stdafx.h"
#include "ksconfig.h"

#include "ksconfig.h"
#include "kssocket.h"
#include "ksthread.h"
#include "adminProcess.h"
#include "ksbase64.h"


class CConfigAdmin {
    public:
        int port;
        char execPath[64];
        char allowIP[128];
        char id[16];
        char pass[16];
        char configPath[64];
};

CConfigAdmin gConf;

int activeProcess = TRUE;
struct _message_info	message_info;
struct _shm_info *shm_info;
char PROCESS_NO[ 7], PROCESS_NAME[36];


int configParse(char* file);

class CThreadInfo {
public:
	pthread_t tid;
	int sock; /* domain socket */
};

void* doService(void* param);
int removeChar(char* target);

int main(int argc, char* argv[])
{
    int ret;
    char logMsg[SOCKET_BUFF];
    struct timeval outtime;
    CKSSocket svrSockfd;
    CKSSocket newSockfd;
    CKSThread ksthread;
    char buff[SOCKET_BUFF];
    int hNewSocket;
    CThreadInfo* pThreadInfo;
    int i;
    int hOpenSocket=0;

    if (ml_sub_init(PROCESS_NO, PROCESS_NAME,(char*)0,(char*)0)<0) {
        perror("ml_sub_init Error.");
        ml_sub_end();
        exit(1);
    }

    ret = configParse(argv[1]);
    if( ret != 0 )
    {
        exit(1);
    }

    sprintf(logMsg,"%s START",PROCESS_NAME);
    monitoring(logMsg,0,0);

    Init_Server();
    ret=UnixSockOpenNon(&hOpenSocket,gConf.port);// socket open
    if ( ret!=0 ) {
        monitoring("SOCKET OPEN ERROR.",0,errno);
        ml_sub_end();
        if ( hOpenSocket ) close( hOpenSocket );
        return -1;
    }

    svrSockfd.attach(hOpenSocket);

    while(activeProcess)
    {
        wait_a_moment(1000);

        hNewSocket = svrSockfd.accept_in();
        if( hNewSocket <= 0 )
            continue;

        memset(buff,0x00,sizeof(buff));//CCL(buff);
        strcpy(buff,svrSockfd.getPeerIP());
        if( strstr(gConf.allowIP,buff) == NULL)
        {
            log_history(0,0,"deny IP : allowIP List[%s] thisIP[%s]",
                    gConf.allowIP,buff);
            close(hNewSocket);
            continue;
        }

        /* new connection  */
        pThreadInfo = NULL;
        pThreadInfo = new CThreadInfo;
        if( pThreadInfo == NULL )
        {
            log_history(0,0,"new ThreadInfo Error [%s]",strerror(errno));
            close(hNewSocket);
            continue;
        }

/*    log_history(0,0,"org[%x]",pThreadInfo); */
        pThreadInfo->sock = hNewSocket;
        ret = ksthread.create(&(pThreadInfo->tid),NULL,doService,(void*)pThreadInfo);
        if( ret != 0 )
        {
            log_history(0,0,"create thread Error [%s]",strerror(errno));
            close(pThreadInfo->sock);
            delete pThreadInfo;
            continue;
        }

    }

END:
    svrSockfd.close();
    
    sprintf(logMsg,"%s CLOSE",PROCESS_NAME);
    monitoring(logMsg,0,0);
    ml_sub_end();

    return 0;
}


void* doService(void* param)
{
    pthread_detach(pthread_self());
    int ret;
    CKSSocket newSockfd;
    char buff[SOCKET_BUFF];
    CThreadInfo* info = (CThreadInfo*)param;
    newSockfd.attach(info->sock);
    memset(buff,0x00,sizeof(buff));//CCL(buff);
    int sock;
    CAdmin2WebPacket admin2WebPacket;
    CAdmin2WebPacketHeader packetHeader;
    char szExec[1024];
    char szType[8];
    char szConfigFile[16+1];
    char szCmd[16+1];

    ret = newSockfd.rcvmsg(buff);
    if( ret == 0 ) 
	{
        newSockfd.close();
        log_history(0,0,"connection 후 3초간 데이터가 없어 연결을 close 합니다");
        delete info;
        return NULL;
    }

    if( ret < 0 ) 
	{
        newSockfd.close();
        log_history(0,0,"recv Error");
        delete info;
        return NULL;
    }

    memcpy((char*)&admin2WebPacket,buff,sizeof(CAdmin2WebPacket));


    if( strncmp(admin2WebPacket.cid,gConf.id,strlen(gConf.id)) != 0 )
    {
         newSockfd.close();
        log_history(0,0,"id  Error [%s]",admin2WebPacket.cid);
        delete info;
        return NULL;
       
    }

    if( strncmp(admin2WebPacket.pass,gConf.pass,strlen(gConf.pass)) != 0 )
    {
         newSockfd.close();
        log_history(0,0,"pass  Error [%s]",admin2WebPacket.pass);
        delete info;
        return NULL;
       
    }


    memset(szType		,0x00	,sizeof(szType));//CCL(szType);
    memset(szConfigFile	,0x00	,sizeof(szConfigFile));//CCL(szConfigFile);
    memset(szCmd		,0x00	,sizeof(szCmd));//CCL(szCmd);
    memcpy(szType,admin2WebPacket.header.type,sizeof(admin2WebPacket.header.type));
    memcpy(szConfigFile,admin2WebPacket.configFile,sizeof(admin2WebPacket.configFile));
    memcpy(szCmd,admin2WebPacket.cmd,sizeof(admin2WebPacket.cmd));

    removeChar(szConfigFile);
    removeChar(szCmd);

    memset(szExec,0x00,sizeof(szExec));//CCL(szExec);
    sprintf(szExec,"%s/admin -c %s/%s  -%s %s "
            ,gConf.execPath
            ,gConf.configPath
            ,szConfigFile
            ,szType
            ,szCmd);

    memset(buff,0x00,sizeof(buff));//CCL(buff);
    FILE* fp=NULL;
    char szSize[12];
    printf("szExec[%s]\n",szExec);
    char* str;
    int size;
    
    if( (fp = popen(szExec,"r")) != NULL )
//    if( (fp = popen("/bin/ls -al","r")) != NULL )
    {
        while( fgets(buff,SOCKET_BUFF,fp) )
        {
            size = 0;
            str = NULL;
            str = (char*)__base64_encode((unsigned char *)buff, strlen(buff), &size);

            memset((char*)&packetHeader,0x20,sizeof(CAdmin2WebPacketHeader));
            memcpy(packetHeader.type,"res",3);
            sprintf(szSize,"%d",size);
            memcpy(packetHeader.leng,szSize,strlen(szSize));
            newSockfd.send((char*)&packetHeader,sizeof(packetHeader));
            newSockfd.send(str,size);
            free(str);
        }
        pclose(fp);
    } 
	else 
	{
        log_history(0,0,"Error [%s]",strerror(errno));
    }

/*    viewPack(buff,sizeof(CMonitor));
 */
                      
    newSockfd.close();
    delete info;

    return NULL;
}

int configParse(char* file)
{
    Q_Entry *pEntry;
    int  i;
    int  nRetFlag = TRUE;
    char *pszTmp;
    CKSConfig conf;
//    memset(&gConf,0x00,sizeof(gConf));

    // read mert conf
    if((pEntry = conf.qfDecoder(file)) == NULL) 
	{
        printf("WARNING: Configuration file(%s) not found.\n", file);
        return -1;
    }



/*      char id[16];
        char pass[16];
        char configPath[64];
*/

    gConf.port = conf.FetchEntryInt("gw.port");

    conf.strncpy2(gConf.execPath , conf.FetchEntry("process.path"),64);
    if( gConf.execPath == NULL )
	{
		strcpy(gConf.execPath,"");
	} 
	conf.strncpy2(gConf.allowIP , conf.FetchEntry("gw.allowIP"),128);
    if( gConf.allowIP == NULL )
	{
		strcpy(gConf.allowIP,"");
	}

    conf.strncpy2(gConf.id , conf.FetchEntry("gw.id"),16);
    if( gConf.id == NULL )
	{
		strcpy(gConf.id,"");
	}
    conf.strncpy2(gConf.pass , conf.FetchEntry("gw.pass"),16);
    if( gConf.pass == NULL )
	{
		strcpy(gConf.pass,"");
	}
    conf.strncpy2(gConf.configPath , conf.FetchEntry("config.path"),64);
    if( gConf.configPath == NULL )
	{
		strcpy(gConf.configPath,"");
	}

    return 0;
}




/* 필요 없는 char 삭제 */
int removeChar(char* target)
{

    int i=0;

    while(1){
        if( target[i] == 0x00 )        
            break;
        if( target[i] == '\n' || target[i] == '\r' ||  target[i] == ';' ) 
            target[i] = 0x20;
        if(i++ > 5000) break;
    }

    return 0;

}




