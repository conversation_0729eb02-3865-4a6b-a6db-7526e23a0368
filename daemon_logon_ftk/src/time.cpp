#include <sys/time.h>
#include <stdio.h>
#include <string.h>
#include <string>
#include <map>
#include <set>
#include <stdlib.h>
#include <time.h>

int main(){
    char	pch[30];
	char	pchlast[30];
	char	pchlast2[30];
	
	time_t	the_time;
	struct	timeval val;
	struct	tm	*tm_ptr;
	
	long long ulltm;
	long long ulltm2;
	long long ullrd;
	int nRand;
	int nPch;
	
	time(&the_time);					//불필요한 초기 시간 제거용으로 사용
	tm_ptr = localtime(&val.tv_sec);	//불필요한 초기 시간 제거용으로 사용	
	gettimeofday(&val,NULL);			//불필요한 초기 시간 제거용으로 사용
	
	time(&the_time);
	tm_ptr = localtime(&val.tv_sec);
	gettimeofday(&val,NULL);

	memset(pch				,0x00		,sizeof(pch));	
	
	struct timespec tmv;
	struct tm	tp;

	//clock_gettime(CLOCK_REALTIME, &tmv);
	//gettimeofday(&val,NULL);
	localtime_r(&tmv.tv_sec, &tp);
	/*sprintf(pch, "%d%2d%2d%2d%2d%2d%06ld"
				,2
				,tm_ptr.tm_mon+1
				,tm_ptr.tm_mday
				,tm_ptr.tm_hour
				,tm_ptr.tm_min
				,tm_ptr.tm_sec
				,val.tv_usec
				);*/
				
	sprintf(pch, "%.2d%.2d%.2d%.2d%.2d%"
				,tm_ptr->tm_mon+2
				,tm_ptr->tm_mday
				,tm_ptr->tm_hour
				,tm_ptr->tm_min
				,tm_ptr->tm_sec
				//,val.tv_usec
				);
	
	printf("pch[%s]\n",pch);
	
	
//	ulltm = atoll(pch);

	//srand(val.tv_usec);
	srand(val.tv_usec);
	//nRand = rand();
	
	ullrd = (nRand+val.tv_usec)%1000000;
	
	printf("rand [%d] ullrd [%lld]\n",rand(),ullrd);
	
	
	nRand = (rand()%1000000);
	
	printf("nRand[%.6d]\n",nRand);
		
	memset(pchlast		,0x00		,sizeof(pchlast));
	
	sprintf(pchlast,"%10s%.6d",pch,nRand);
	
	printf("pchlast[%s]\n",pchlast);
	
	sprintf(pchlast2,"%10s%.6lld",pch,ullrd);
	
	printf("pchlast2[%s]\n",pchlast2);
	
	ulltm = atoll(pchlast);
	
	printf("ulltm[%lld]\n",ulltm);
	
	ulltm2 = atoll(pchlast2);
	
	printf("ulltm2[%lld]\n",ulltm2);
	
		
//	free(pch);
	return ulltm;
}