#include "admin.h"

void displayAdminInfo(CAdminInfo adminInfo);

int usage(const char *argv) {
    printf("usage: %s <-d id> <-t id>\n", argv);

    printf("\t-d\t 프로세스 상태 표시\n");
    printf("\t-t\t 프로세스 정지\n");

    return 1;
}


void displayInfoHeader();

void wait_a_moment(int interval);

void displayInfo(const char* id);
void displayInfoAll();
void displayInfoId(const char* id);

void modifyInfo(const char* id);
void modifyInfoAll();
void modifyInfoId(const char* id);

void killProcess(const char* id);
void killProcessAll();
void killProcessId(const char* id);
void killSenderProcessId(const char* id);
void killReportProcessId(const char* id);

void modifyCallbackInfo(const char* id);
void modifyCallbackInfoAll();
void modifyCallbackInfoId(const char* id);

void modifyChkCallbackInfo(const char* id);
void modifyChkCallbackInfoAll();
void modifyChkCallbackInfoId(const char* id);

const char *cfg="";

int main(int argc, char* argv[])
{
    int ret;
    CKSSocket conn;
    char buff[SOCKET_BUFF];
    int nType;
    const char *id="";
    int cmd=0;

    char c; /* d : display info & t : terminal */
    while ((c = getopt(argc, argv, "d:t:m:s:r:k:l:c:")) != EOF)
    {
        switch (c) {
            case 'd': id=optarg;cmd=1;  break;
            case 't': id=optarg;cmd=2;  break;
            case 'm': id=optarg;cmd=3;  break;
            case 's': id=optarg;cmd=4;  break;
            case 'r': id=optarg;cmd=5;  break;
			case 'k': id=optarg;cmd=6;  break;
            case 'l': id=optarg;cmd=7;  break;
            case 'c': cfg=optarg; break;
            default: return usage(argv[0]);
        }

    }

    if( strlen(cfg) <= 0 )
    {
        printf("check -c : [%s]\nrequired config file \n",cfg);
        return -1;
    }


    ret = configParse((char*)cfg);
    if( ret != 0 )
    {
        exit(1);
    }

    switch(cmd) {
        case 1: 
            displayInfo(id);
            break;
        case 2:
            killProcess(id);
            break;
        case 3:
            modifyInfo(id);
            break;
        case 4:
            killSenderProcessId(id);
            break;
        case 5:
            killReportProcessId(id);
			break;
        case 6:
			modifyCallbackInfo(id);
			break;
		case 7:
            modifyChkCallbackInfo(id);
			break; 
        default:
            break;
    }


    return 0;

/*
    nType = atoi(argv[1]);
    ret = conn.connectDomain("/home/<USER>/domain/ID_S_type");
    if( ret != 0 )
    {
        printf("Admin conn Error[%s]\n",strerror(ret));
        return -1;
    }

    sprintf(buff,"%d   ",nType);
    ret = conn.send(buff,strlen(buff));
    if( ret != strlen(buff) )
    {
        printf("Admin send Error[%s]\n",strerror(ret));
        conn.close();
        return -1;
    }

    memset(buff,0x00,sizeof(buff));
    ret = conn.rcvmsg(buff);
    if( ret <= 0 )
    {
        printf("Admin recv Error[%s]\n",conn.getErrMsg());
        conn.close();
        return -1;
    }

    CAdminInfo* pAdminInfo = (CAdminInfo*)buff;
    displayAdminInfo(*pAdminInfo);


    conn.close();


    return 0;
    */
}




void displayAdminInfo(CAdminInfo adminInfo)
{
    printf("[%s] [%c] [%s] [%s] [%s] [%s] [%d] [%d] [%d] [%d] [%d] [%d] [%d] [%s] [%s] [%s] [%s] [%s] [%s] [%d] [%s]\n",
            adminInfo.logonDbInfo.szCID,
            adminInfo.logonDbInfo.classify,
            adminInfo.logonDbInfo.szSenderDBName,
            adminInfo.logonDbInfo.szReportDBName,
            adminInfo.logonDbInfo.szSIP,
            adminInfo.logonDbInfo.szAPPName,
            adminInfo.logonDbInfo.nmPID,
            adminInfo.logonDbInfo.nmJOB,
            adminInfo.logonDbInfo.nmPRT,
            adminInfo.logonDbInfo.nmCNT,
            adminInfo.logonDbInfo.nmRST,
            adminInfo.logonDbInfo.nUrlJob,
            adminInfo.logonDbInfo.nRptWait,
            adminInfo.processInfo.processName,
            adminInfo.processInfo.startTime,
            adminInfo.processInfo.linkTime,
            adminInfo.processInfo.dataCnt,
            adminInfo.processInfo.szPid,
            adminInfo.logonDbInfo.szIP,
            adminInfo.logonDbInfo.nRptNoDataSleep,
            adminInfo.logonDbInfo.szReserve
          );


/*
    printf("szType[%s]szCmd[%s]szResult[%s]cid[%s]pass[%s]szSIP[%s]svrAPP[%s]pid[%d]job[%d]prt[%d]cnt[%d]rst[%d]UrlJOB[%d]nRptWait[%d]classfy[%c]processName[%s]startTime[%s]linkTime[%s]dataCnt[%s]pid[%s]customIP[%s]RptNoDataSleep[%d]telcoInfo[%s]\n",
            adminInfo.szType,
            adminInfo.szCmd,
            adminInfo.szResult,
            adminInfo.logonDbInfo.szCID,
            adminInfo.logonDbInfo.szPWD,
            adminInfo.logonDbInfo.szSIP,
            adminInfo.logonDbInfo.szAPPName,
            adminInfo.logonDbInfo.nmPID,
            adminInfo.logonDbInfo.nmJOB,
            adminInfo.logonDbInfo.nmPRT,
            adminInfo.logonDbInfo.nmCNT,
            adminInfo.logonDbInfo.nmRST,
            adminInfo.logonDbInfo.nUrlJob,
            adminInfo.logonDbInfo.nRptWait,
            adminInfo.logonDbInfo.classify,
            adminInfo.processInfo.processName,
            adminInfo.processInfo.startTime,
            adminInfo.processInfo.linkTime,
            adminInfo.processInfo.dataCnt,
            adminInfo.processInfo.szPid,
            adminInfo.logonDbInfo.szIP,
            adminInfo.logonDbInfo.nRptNoDataSleep,
            adminInfo.logonDbInfo.szReserve
          );
*/


/*
    printf("[%s][%s][%s][%s][%s][%s][%s][%s][%s][%d][%d][%d][%d][%d][%d][%d][%d][%c]\n",
            adminInfo.szType,
            adminInfo.szCmd,
            adminInfo.szResult,
            adminInfo.logonDbInfo.szCID,
            adminInfo.logonDbInfo.szPWD,
            adminInfo.logonDbInfo.szSIP,
            adminInfo.logonDbInfo.szAPPName,
            adminInfo.logonDbInfo.szServerIp,
            adminInfo.logonDbInfo.szErrMsg,
            adminInfo.logonDbInfo.nmPID,
            adminInfo.logonDbInfo.nmJOB,
            adminInfo.logonDbInfo.nmPRT,
            adminInfo.logonDbInfo.nmCNT,
            adminInfo.logonDbInfo.nmRST,
            adminInfo.logonDbInfo.nServerPort,
            adminInfo.logonDbInfo.nUrlJob,
            adminInfo.logonDbInfo.nRptWait,
            adminInfo.logonDbInfo.classify
          );
*/

    return;
}



void displayInfo(const char* id)
{
    if( strcmp(id,"all") == 0 )
        displayInfoAll();
    else
        displayInfoId(id);

    return;
}

void modifyInfo(const char* id)
{
    if( strcmp(id,"all") == 0 )
	{
        modifyInfoAll();
	}
    else
	{
        modifyInfoId(id);
	}

    return;
}

void modifyInfoAll()
{
    int ret;
    CKSSocket conn;
    char buff[SOCKET_BUFF];

    CAdminUtil util;
    char* fileName = NULL;
    char filePath[256];

displayInfoHeader();
    util.findFileInit(gConf.domainPath);
    memset(filePath,0x00,sizeof(filePath));

    while( (fileName = util.findFileNext() ) != NULL )
    {
        sprintf(filePath,"%s/%s",gConf.domainPath,fileName); 
        ret = conn.connectDomain(filePath);
        if( ret != 0 )
        {
//            printf("file[%s] not connect[%s]\n",filePath,strerror(errno));
            continue ;
        }
printf("filePath[%s]\n", filePath);

        sprintf(buff,"%d   ",5);
        ret = conn.send(buff,strlen(buff));
        if( ret != strlen(buff) )
        {
            printf("Admin send Error[%s]file[%s]\n",strerror(ret),filePath);
            conn.close();
            return ;
        }

        conn.close();
        printf("[%s] modify ok\n",fileName);

    }
    util.findFileClose();

    return;
}

void modifyInfoId(const char* id)
{
    int ret;
    CKSSocket conn;
    char buff[SOCKET_BUFF];

    CAdminUtil util;
    char* fileName = NULL;
    char filePath[256];
    util.findFileInit(gConf.domainPath);
    memset(filePath,0x00,sizeof(filePath));
    while( (fileName = util.findFileNext() ) != NULL )
    {
        if( memcmp(id,fileName+5,strlen(id)) != 0 )
            continue;
       sprintf(filePath,"%s/%s",gConf.domainPath,fileName); 
        ret = conn.connectDomain(filePath);
        if( ret != 0 )
        {
            printf("file[%s] not connect[%s]\n",filePath,strerror(errno));
            continue ;
        }

        sprintf(buff,"%d   ",5);
        ret = conn.send(buff,strlen(buff));
        if( ret != strlen(buff) )
        {
            printf("Admin send Error[%s]file[%s]\n",strerror(ret),filePath);
            conn.close();
            return ;
        }

        conn.close();

    }

	printf("modify Info ID[%s] sucess\n", id);
    util.findFileClose();

    return;

}


void modifyCallbackInfo(const char* id)
{
    if( strcmp(id,"all") == 0 )
	{
        modifyCallbackInfoAll();
	}
    else
	{
        modifyCallbackInfoId(id);
	}

    return;
}

void modifyCallbackInfoAll()
{
    int ret;
    CKSSocket conn;
    char buff[SOCKET_BUFF];


    CAdminUtil util;
    char* fileName = NULL;
    char filePath[256];

	displayInfoHeader();
    util.findFileInit(gConf.domainPath);
    memset(filePath,0x00,sizeof(filePath));

    while( (fileName = util.findFileNext() ) != NULL )
    {
        sprintf(filePath,"%s/%s", gConf.domainPath, fileName); 
        ret = conn.connectDomain(filePath);
        if( ret != 0 )
        {
//            printf("file[%s] not connect[%s]\n",filePath,strerror(errno));
            continue ;
        }
printf("filePath[%s]\n", filePath);

        sprintf(buff,"%d   ", 6);
        ret = conn.send(buff,strlen(buff));
        if( ret != strlen(buff) )
        {
            printf("Admin send Error[%s]file[%s]\n", strerror(ret), filePath);
            conn.close();
            return ;
        }

        conn.close();
        printf("[%s] modify ok\n",fileName);

    }
    util.findFileClose();

    return;
}

void modifyCallbackInfoId(const char* id)
{
    int ret;
    CKSSocket conn;
    char buff[SOCKET_BUFF];

    CAdminUtil util;
    char* fileName = NULL;
    char filePath[256];
    util.findFileInit(gConf.domainPath);
    memset(filePath,0x00,sizeof(filePath));
    while((fileName = util.findFileNext() ) != NULL)
    {
        if( memcmp(id,fileName+5,strlen(id)) != 0 )
            continue;
       sprintf(filePath,"%s/%s",gConf.domainPath,fileName); 
        ret = conn.connectDomain(filePath);
        if( ret != 0 )
        {
            printf("file[%s] not connect[%s]\n", filePath, strerror(errno));
            continue ;
        }

        sprintf(buff,"%d   ",6);
        ret = conn.send(buff,strlen(buff));
        if( ret != strlen(buff) )
        {
            printf("Admin send Error[%s]file[%s]\n", strerror(ret), filePath);
            conn.close();
            return ;
        }

        conn.close();

    }

    util.findFileClose();

    return;
}

void modifyChkCallbackInfo(const char* id)
{
    if( strcmp(id,"all") == 0 )
	{
        modifyChkCallbackInfoAll();
	}
    else
	{
        modifyChkCallbackInfoId(id);
	}

    return;
}

void modifyChkCallbackInfoAll()
{
    int ret;
    CKSSocket conn;
    char buff[SOCKET_BUFF];


    CAdminUtil util;
    char* fileName = NULL;
    char filePath[256];

	displayInfoHeader();
    util.findFileInit(gConf.domainPath);
    memset(filePath,0x00,sizeof(filePath));

    while( (fileName = util.findFileNext() ) != NULL )
    {
        sprintf(filePath,"%s/%s", gConf.domainPath, fileName); 
        ret = conn.connectDomain(filePath);
        if( ret != 0 )
        {
//            printf("file[%s] not connect[%s]\n",filePath,strerror(errno));
            continue ;
        }
printf("filePath[%s]\n", filePath);

        sprintf(buff,"%d   ", 7);
        ret = conn.send(buff,strlen(buff));
        if( ret != strlen(buff) )
        {
            printf("Admin send Error[%s]file[%s]\n", strerror(ret), filePath);
            conn.close();
            return ;
        }

        conn.close();
        printf("[%s] modify ok\n",fileName);

    }
    util.findFileClose();

    return;
}

void modifyChkCallbackInfoId(const char* id)
{
    int ret;
    CKSSocket conn;
    char buff[SOCKET_BUFF];

    CAdminUtil util;
    char* fileName = NULL;
    char filePath[256];
    util.findFileInit(gConf.domainPath);
    memset(filePath,0x00,sizeof(filePath));
    while((fileName = util.findFileNext() ) != NULL)
    {
        if( memcmp(id,fileName+5,strlen(id)) != 0 )
            continue;
       sprintf(filePath,"%s/%s",gConf.domainPath,fileName); 
        ret = conn.connectDomain(filePath);
        if( ret != 0 )
        {
            printf("file[%s] not connect[%s]\n", filePath, strerror(errno));
            continue ;
        }

        sprintf(buff,"%d   ", 7);
        ret = conn.send(buff,strlen(buff));
        if( ret != strlen(buff) )
        {
            printf("Admin send Error[%s]file[%s]\n", strerror(ret), filePath);
            conn.close();
            return ;
        }

        conn.close();

    }

    util.findFileClose();

    return;
}


void displayInfoAll()
{
    int ret;
    CKSSocket conn;
    char buff[SOCKET_BUFF];


    CAdminUtil util;
    char* fileName = NULL;
    char filePath[256];

	displayInfoHeader();

    util.findFileInit(gConf.domainPath);
    memset(filePath,0x00,sizeof(filePath));

    while( (fileName = util.findFileNext() ) != NULL )
    {
 
        sprintf(filePath,"%s/%s",gConf.domainPath,fileName);
        ret = conn.connectDomain(filePath);
        if( ret != 0 )
        {
//            printf("file[%s] not connect[%s]\n",filePath,strerror(errno));
            continue ;
        }

        sprintf(buff,"%d   ",1);
        ret = conn.send(buff,strlen(buff));
        if( ret != strlen(buff) )
        {
            printf("Admin send Error ID[%s]file[%s]\n",strerror(ret),filePath);
            conn.close();
            return ;
        }

//        memset(buff,0x00,sizeof(buff));
//        ret = conn.rcvmsg(buff);
//        if( ret <= 0 )
//        {
//            printf("Admin recv Error ID[%s]file[%s]\n",conn.getErrMsg(),filePath);
//            conn.close();
//            continue ;
//        }  
		memset(buff,0x00,sizeof(buff));
        while(1)
        {
        	ret = conn.select();
        	if (ret == 0)
        		continue;
        	else if (ret < 0)
	        {
	            printf("Admin recv Error ID1[%s]file[%s]\n",conn.getErrMsg(),filePath);
	            conn.close();
	            return ;
	        }
	        else
	        {
		        ret = conn.recv(buff,4096);
		        if( ret <= 0 )
		        {
		            printf("Admin recv Error ID2[%s]file[%s]\n",conn.getErrMsg(),filePath);
		            conn.close();
		            return ;
		        }
		        else
		        	break ;
		    }
		}
		
        CAdminInfo* pAdminInfo = (CAdminInfo*)buff;
        displayAdminInfo(*pAdminInfo);


        conn.close();

    }
    util.findFileClose();


    util.findFileInit(gConf.domainPath);
    memset(filePath,0x00,sizeof(filePath));

    while( (fileName = util.findFileNext("DB") ) != NULL )
    {
        sprintf(filePath,"%s/%s",gConf.domainPath,fileName); 
        ret = conn.connectDomain(filePath);
        if( ret != 0 )
        {
            printf("file[%s] not connect[%s]\n",filePath,strerror(errno));
            continue ;
        }

        sprintf(buff,"getInfo");
        ret = conn.send(buff,strlen(buff));
        if( ret != strlen(buff) )
        {
            printf("Admin send Error DB[%s]file[%s]\n",strerror(ret),filePath);
            conn.close();
            return ;
        }

//        memset(buff,0x00,sizeof(buff));
//        ret = conn.rcvmsg(buff);
//        if( ret <= 0 )
//        {
//            printf("Admin recv Error DB[%s]file[%s]\n",conn.getErrMsg(),filePath);
//            conn.close();
//            return ;
//        }

		memset(buff,0x00,sizeof(buff));
        while(1)
        {
        	ret = conn.select();
        	if (ret == 0)
        		continue;
        	else if (ret < 0)
	        {
	            printf("Admin recv Error DB1[%s]file[%s]\n",conn.getErrMsg(),filePath);
	            conn.close();
	            return ;
	        }
	        else
	        {
		        ret = conn.recv(buff,4096);
		        if( ret <= 0 )
		        {
		            printf("Admin recv Error DB2[%s]file[%s]\n",conn.getErrMsg(),filePath);
		            conn.close();
		            return ;
		        }
		        else
		        	break ;
		    }
		}
		
        printf("[%s] dbConn Count [%s]\n",fileName,buff);

        conn.close();

    }
    util.findFileClose();

    return;
}

void displayInfoId(const char* id)
{
    int ret;
    CKSSocket conn;
    char buff[SOCKET_BUFF];

    CAdminUtil util;
    char* fileName = NULL;
    char filePath[256];
    util.findFileInit(gConf.domainPath);
    memset(filePath,0x00,sizeof(filePath));
    while( (fileName = util.findFileNext() ) != NULL )
    {
        if( memcmp(id,fileName+5,strlen(id)) != 0 )
		{
            continue;
		}
		sprintf(filePath,"%s/%s",gConf.domainPath,fileName); 
        ret = conn.connectDomain(filePath);
        if( ret != 0 )
        {
            printf("file[%s] not connect[%s]\n",filePath,strerror(errno));
            continue ;
        }

        sprintf(buff,"%d   ",1);
        ret = conn.send(buff,strlen(buff));
        if( ret != strlen(buff) )
        {
            printf("Admin send Error[%s]file[%s]\n",strerror(ret),filePath);
            conn.close();
            return ;
        }

        memset(buff,0x00,sizeof(buff));
        while(1)
        {
        	ret = conn.select();
        	if (ret == 0)
        		continue;
        	else if (ret < 0)
	        {
	            printf("Admin recv Error U1[%s]file[%s]\n",conn.getErrMsg(),filePath);
	            conn.close();
	            return ;
	        }
	        else
	        {
		        ret = conn.recv(buff,4096);
		        if( ret <= 0 )
		        {
		            printf("Admin recv Error U2[%s]file[%s]\n",conn.getErrMsg(),filePath);
		            conn.close();
		            return ;
		        }
		        else
		        	break ;
		    }
		}

        CAdminInfo* pAdminInfo = (CAdminInfo*)buff;
        displayAdminInfo(*pAdminInfo);

        conn.close();

    }
    util.findFileClose();

    return;
}




void killProcess(const char* id)
{
    if( strcmp(id,"all") == 0 )
        killProcessAll();
    else
        killProcessId(id);

    return;
}

void killProcessAll()
{

    int ret;
    CKSSocket conn;
    char buff[SOCKET_BUFF];

    CAdminUtil util;
    char* fileName = NULL;
    char filePath[256];

	displayInfoHeader();
    util.findFileInit(gConf.domainPath);
    
	memset(filePath,0x00,sizeof(filePath));

    while( (fileName = util.findFileNext() ) != NULL )
    {
       sprintf(filePath,"%s/%s",gConf.domainPath,fileName); 
        ret = conn.connectDomain(filePath);
        if( ret != 0 )
        {
//            printf("file[%s] not connect[%s]\n",filePath,strerror(errno));
            continue ;
        }

        sprintf(buff,"%d   ",3);
        ret = conn.send(buff,strlen(buff));
        if( ret != strlen(buff) )
        {
            printf("Admin send Error[%s]file[%s]\n",strerror(ret),filePath);
            conn.close();
            return ;
        }

//        memset(buff,0x00,sizeof(buff));
//        ret = conn.rcvmsg(buff);
//        if( ret <= 0 )
//        {
//            printf("Admin recv Error[%s]file[%s]\n",conn.getErrMsg(),filePath);
//            conn.close();
//            return ;
//        }
		memset(buff,0x00,sizeof(buff));
        while(1)
        {
        	ret = conn.select();
        	if (ret == 0)
        		continue;
        	else if (ret < 0)
	        {
	            printf("Admin recv Error AK1[%s]file[%s]\n",conn.getErrMsg(),filePath);
	            conn.close();
	            return ;
	        }
	        else
	        {
		        ret = conn.recv(buff,4096);
		        if( ret <= 0 )
		        {
		            printf("Admin recv Error AK2[%s]file[%s]\n",conn.getErrMsg(),filePath);
		            conn.close();
		            return ;
		        }
		        else
		        	break ;
		    }
		}

        CAdminInfo* pAdminInfo = (CAdminInfo*)buff;
        displayAdminInfo(*pAdminInfo);

        conn.close();

    }
    util.findFileClose();

    return;
}

void killReportProcessId(const char* id)
{
    int ret;
    CKSSocket conn;
    char buff[SOCKET_BUFF];


    CAdminUtil util;
    char* fileName = NULL;
    char filePath[256];
    util.findFileInit(gConf.domainPath);
    memset(filePath,0x00,sizeof(filePath));
    while( (fileName = util.findFileNext() ) != NULL )
    {
        if( memcmp(id,fileName+5,strlen(id)) != 0 )
            continue;
        if( fileName[3] == 'S' )
            continue;

       sprintf(filePath,"%s/%s",gConf.domainPath,fileName); 
        ret = conn.connectDomain(filePath);
        if( ret != 0 )
        {
            printf("file[%s] not connect[%s]\n",filePath,strerror(errno));
            continue ;
        }

        sprintf(buff,"%d   ",3);
        ret = conn.send(buff,strlen(buff));
        if( ret != strlen(buff) )
        {
            printf("Admin send Error[%s]file[%s]\n",strerror(ret),filePath);
            conn.close();
            return ;
        }

//        memset(buff,0x00,sizeof(buff));
//        ret = conn.rcvmsg(buff);
//        if( ret <= 0 )
//        {
//            printf("Admin recv Error[%s]file[%s]\n",conn.getErrMsg(),filePath);
//            conn.close();
//            return ;
//        }
		memset(buff,0x00,sizeof(buff));
        while(1)
        {
        	ret = conn.select();
        	if (ret == 0)
        		continue;
        	else if (ret < 0)
	        {
	            printf("Admin recv Error RE1[%s]file[%s]\n",conn.getErrMsg(),filePath);
	            conn.close();
	            return ;
	        }
	        else
	        {
		        ret = conn.recv(buff,4096);
		        if( ret <= 0 )
		        {
		            printf("Admin recv Error RE2[%s]file[%s]\n",conn.getErrMsg(),filePath);
		            conn.close();
		            return ;
		        }
		        else
		        	break ;
		    }
		}

        CAdminInfo* pAdminInfo = (CAdminInfo*)buff;
        displayAdminInfo(*pAdminInfo);

       /* 
        printf("checking process pid [%s]\n",pAdminInfo->processInfo.szPid);
        while(  kill(atoi(pAdminInfo->processInfo.szPid), 0 ) == 0 )
        {
            wait_a_moment(10000);
        }

        printf("process pid [%s] kill ok\n",pAdminInfo->processInfo.szPid);

        */

        conn.close();

    }
    util.findFileClose();


    return;
}



void killSenderProcessId(const char* id)
{
    int ret;
    CKSSocket conn;
    char buff[SOCKET_BUFF];


    CAdminUtil util;
    char* fileName = NULL;
    char filePath[256];
    util.findFileInit(gConf.domainPath);
    memset(filePath,0x00,sizeof(filePath));
    while( (fileName = util.findFileNext() ) != NULL )
    {
        if( memcmp(id,fileName+5,strlen(id)) != 0 )
            continue;
        if( fileName[3] == 'R' )
            continue;

       sprintf(filePath,"%s/%s",gConf.domainPath,fileName); 
        ret = conn.connectDomain(filePath);
        if( ret != 0 )
        {
            printf("file[%s] not connect[%s]\n",filePath,strerror(errno));
            continue ;
        }

        sprintf(buff,"%d   ",3);
        ret = conn.send(buff,strlen(buff));
        if( ret != strlen(buff) )
        {
            printf("Admin send Error[%s]file[%s]\n",strerror(ret),filePath);
            conn.close();
            return ;
        }

//        memset(buff,0x00,sizeof(buff));
//        ret = conn.rcvmsg(buff);
//        if( ret <= 0 )
//        {
//            printf("Admin recv Error[%s]file[%s]\n",conn.getErrMsg(),filePath);
//            conn.close();
//            return ;
//        }

		memset(buff,0x00,sizeof(buff));
        while(1)
        {
        	ret = conn.select();
        	if (ret == 0)
        		continue;
        	else if (ret < 0)
	        {
	            printf("Admin recv Error SE1[%s]file[%s]\n",conn.getErrMsg(),filePath);
	            conn.close();
	            return ;
	        }
	        else
	        {
		        ret = conn.recv(buff,4096);
		        if( ret <= 0 )
		        {
		            printf("Admin recv Error SE2[%s]file[%s]\n",conn.getErrMsg(),filePath);
		            conn.close();
		            return ;
		        }
		        else
		        	break ;
		    }
		}
		        
        CAdminInfo* pAdminInfo = (CAdminInfo*)buff;
        displayAdminInfo(*pAdminInfo);

        /*
        printf("checking process pid [%s]\n",pAdminInfo->processInfo.szPid);
        while(  kill(atoi(pAdminInfo->processInfo.szPid), 0 ) == 0 )
        {
            wait_a_moment(10000);
        }

        printf("process pid [%s] kill ok\n",pAdminInfo->processInfo.szPid);

        */



        conn.close();

    }
    util.findFileClose();


    return;
}



void killProcessId(const char* id)
{
    int ret;
    CKSSocket conn;
    char buff[SOCKET_BUFF];


    CAdminUtil util;
    char* fileName = NULL;
    char filePath[256];
    util.findFileInit(gConf.domainPath);
    memset(filePath,0x00,sizeof(filePath));
    while( (fileName = util.findFileNext() ) != NULL )
    {
        if( memcmp(id,fileName+5,strlen(id)) != 0 )
            continue;

       sprintf(filePath,"%s/%s",gConf.domainPath,fileName); 
        ret = conn.connectDomain(filePath);
        if( ret != 0 )
        {
            printf("file[%s] not connect[%s]\n",filePath,strerror(errno));
            continue ;
        }

        sprintf(buff,"%d   ",3);
        ret = conn.send(buff,strlen(buff));
        if( ret != strlen(buff) )
        {
            printf("Admin send Error[%s]file[%s]\n",strerror(ret),filePath);
            conn.close();
            return ;
        }

//        memset(buff,0x00,sizeof(buff));
//        ret = conn.rcvmsg(buff);
//        if( ret <= 0 )
//        {
//            printf("Admin recv Error[%s]file[%s]\n",conn.getErrMsg(),filePath);
//            conn.close();
//            return ;
//        }
		memset(buff,0x00,sizeof(buff));
        while(1)
        {
        	ret = conn.select();
        	if (ret == 0)
        		continue;
        	else if (ret < 0)
	        {
	            printf("Admin recv Error KI1[%s]file[%s]\n",conn.getErrMsg(),filePath);
	            conn.close();
	            return ;
	        }
	        else
	        {
		        ret = conn.recv(buff,4096);
		        if( ret <= 0 )
		        {
		            printf("Admin recv Error KI2[%s]file[%s]\n",conn.getErrMsg(),filePath);
		            conn.close();
		            return ;
		        }
		        else
		        	break ;
		    }
		}
		
        printf("??\n");
        CAdminInfo* pAdminInfo = (CAdminInfo*)buff;
        displayAdminInfo(*pAdminInfo);
/*
        printf("checking process pid [%s]\n",pAdminInfo->processInfo.szPid);
        while(  kill(atoi(pAdminInfo->processInfo.szPid), 0 ) == 0 )
        {
            wait_a_moment(10000);
        }

        printf("process pid [%s] kill ok\n",pAdminInfo->processInfo.szPid);
        */

        conn.close();

    }
    util.findFileClose();


    return;
}



void displayInfoHeader()
{
//    printf("process_name :  id  :  pass  :  version  : app_name \n");
printf("-------------------------------------------------------------------------------------------------------------------\n");
    printf("cid classify szSIP svrAPP pid job prt cnt rst UrlJOB nRptWait processName startTime linkTime dataCnt pid customIP RptNoDataSleep telcoInfo\n");
printf("-------------------------------------------------------------------------------------------------------------------\n");
    return;
}

int configParse(char* file)
{
    Q_Entry *pEntry;
    int  i;
    int  nRetFlag = TRUE;
    char *pszTmp;
    CKSConfig conf;
//    memset(&gConf,0x00,sizeof(gConf));

    // read mert conf
    if((pEntry = conf.qfDecoder(file)) == NULL) {
        printf("WARNING: Configuration file(%s) not found.\n", file);
        return -1;
    }

    conf.strncpy2(gConf.domainPath , conf.FetchEntry("domain.path"),64);
    if( gConf.domainPath == NULL ) strcpy(gConf.domainPath,"");

    return 0;
}


void wait_a_moment(int interval)
{
    struct timeval subtime;
    subtime.tv_sec = 0;
    subtime.tv_usec = interval;
    select(0,(fd_set*)0,(fd_set*)0,(fd_set*)0,&subtime);
}


