/*
 * logonDB_simple.pc - Pro*C version of logonDB (C style)
 * Converted from ODPI-C to Pro*C for better Oracle compatibility
 */

#include "logonDB_proc_c.h"
#include <sqlca.h>

// Pro*C SQL Communication Area - using direct include instead of EXEC SQL INCLUDE

// Pro*C 전역 변수들
EXEC SQL BEGIN DECLARE SECTION;
    // 연결 정보
    char db_userid[64];
    char db_passwd[64]; 
    char db_database[64];
    
    // proc_check_mms_login_ext2 변수들
    char proc1_in_cid[64];
    char proc1_in_pwd[64];
    char proc1_ot_appname[256];
    char proc1_ot_sip[64];
    int proc1_ot_pid;
    int proc1_ot_job;
    int proc1_ot_c_job;
    int proc1_ot_prt;
    int proc1_ot_cnt;
    int proc1_ot_rpt_cnt;
    char proc1_ot_server_info[512];
    int proc1_ot_rpt_sleep_cnt;
    char proc1_ot_sender_proc[256];
    char proc1_ot_report_proc[256];
    char proc1_ot_senderdb_info[512];
    char proc1_ot_reportdb_info[512];
    char proc1_ot_logfile_info[512];
    char proc1_ot_etc[512];
    int proc1_ot_rst;
    char proc1_ot_rstmsg[512];
    
    // proc_get_limit_def 변수들
    int proc2_in_pid;
    char proc2_ot_limit_type[256];
    int proc2_ot_day_warn_cnt;
    int proc2_ot_day_limit_cnt;
    int proc2_ot_mon_warn_cnt;
    int proc2_ot_mon_limit_cnt;
    char proc2_ot_limit_flag[256];
    int proc2_ot_day_acc_cnt;
    int proc2_ot_mon_acc_cnt;
    int proc2_ot_rst;
    char proc2_ot_rstmsg[512];
    
    // SELECT 쿼리 변수들
    int select_ptn_id;
    char select_callback[512];
    char select_dial_code_type[64];
    char select_dial_code[64];
EXEC SQL END DECLARE SECTION;

// 기존 전역 변수들
extern int activeProcess;
extern struct _message_info message_info;
extern struct _shm_info *shm_info;
extern char PROCESS_NO[7], PROCESS_NAME[36];

int hNewSocket = -1;
int ret = 0;

// 설정 정보
CConfigLogonDB gConf;

// 시그널 핸들러들
void alarm_handler(int sig) {
    log_history(0,0,"[WARN] Alarm signal received (signal %d)", sig);
}

void segv_handler(int sig) {
    log_history(0,0,"[FATAL] Segmentation fault detected (signal %d)", sig);
    log_history(0,0,"[FATAL] Process will terminate");
    activeProcess = 0;
    exit(1);
}

void term_handler(int sig) {
    log_history(0,0,"[INFO] Termination signal received (signal %d)", sig);
    log_history(0,0,"[INFO] Initiating graceful shutdown...");
    activeProcess = 0;
}

// Pro*C 데이터베이스 연결 함수
int proc_connect() {
    // 연결 정보 설정
    strcpy(db_userid, gConf.dbID);
    strcpy(db_passwd, gConf.dbPASS);
    strcpy(db_database, gConf.dbSID);
    
    log_history(0,0,"[INF] Attempting Pro*C connection to %s", db_database);
    
    EXEC SQL CONNECT :db_userid IDENTIFIED BY :db_passwd USING :db_database;
    
    if (sqlca.sqlcode != 0) {
        log_history(0,0,"[ERR] Pro*C connect failed - SQLCODE: %d", sqlca.sqlcode);
        log_history(0,0,"[ERR] SQLERRM: %.70s", sqlca.sqlerrm.sqlerrmc);
        return FALSE;
    }
    
    log_history(0,0,"[INF] Pro*C db connect success");
    return TRUE;
}

// Pro*C 데이터베이스 연결 해제 함수
void proc_disconnect() {
    EXEC SQL COMMIT WORK RELEASE;
    
    if (sqlca.sqlcode != 0) {
        log_history(0,0,"[WARN] Pro*C disconnect warning - SQLCODE: %d", sqlca.sqlcode);
    } else {
        log_history(0,0,"[INF] Pro*C db disconnect success");
    }
}

// proc_check_mms_login_ext2 호출 함수
int call_proc_check_mms_login_ext2(const char* cid, const char* pwd, 
                                   char* szAPPName, char* szSIP, int* nmPID, int* nmJOB,
                                   int* nUrlJob, int* nmPRT, int* nmCNT, int* nRptWait,
                                   char* szServerInfo, int* nRptNoDataSleep,
                                   char* szSenderName, char* szReportName,
                                   char* szSenderDBName, char* szReportDBName,
                                   char* szLogFilePath, char* szReserve,
                                   int* nmRST, char* szErrMsg) {
    
    // 입력 파라미터 설정
    strcpy(proc1_in_cid, cid);
    strcpy(proc1_in_pwd, pwd);
    
    // 출력 파라미터 초기화
    memset(proc1_ot_appname, 0, sizeof(proc1_ot_appname));
    memset(proc1_ot_sip, 0, sizeof(proc1_ot_sip));
    proc1_ot_pid = 0;
    proc1_ot_job = 0;
    proc1_ot_c_job = 0;
    proc1_ot_prt = 0;
    proc1_ot_cnt = 0;
    proc1_ot_rpt_cnt = 0;
    memset(proc1_ot_server_info, 0, sizeof(proc1_ot_server_info));
    proc1_ot_rpt_sleep_cnt = *nRptNoDataSleep;
    memset(proc1_ot_sender_proc, 0, sizeof(proc1_ot_sender_proc));
    memset(proc1_ot_report_proc, 0, sizeof(proc1_ot_report_proc));
    memset(proc1_ot_senderdb_info, 0, sizeof(proc1_ot_senderdb_info));
    memset(proc1_ot_reportdb_info, 0, sizeof(proc1_ot_reportdb_info));
    memset(proc1_ot_logfile_info, 0, sizeof(proc1_ot_logfile_info));
    memset(proc1_ot_etc, 0, sizeof(proc1_ot_etc));
    proc1_ot_rst = -999;
    memset(proc1_ot_rstmsg, 0, sizeof(proc1_ot_rstmsg));
    
    log_history(0,0,"[INF] Calling proc_check_mms_login_ext2 for CID[%s] PWD[%s]", cid, pwd);
    
    // 프로시저 호출
    EXEC SQL CALL proc_check_mms_login_ext2(
        :proc1_in_cid,
        :proc1_in_pwd,
        :proc1_ot_appname,
        :proc1_ot_sip,
        :proc1_ot_pid,
        :proc1_ot_job,
        :proc1_ot_c_job,
        :proc1_ot_prt,
        :proc1_ot_cnt,
        :proc1_ot_rpt_cnt,
        :proc1_ot_server_info,
        :proc1_ot_rpt_sleep_cnt,
        :proc1_ot_sender_proc,
        :proc1_ot_report_proc,
        :proc1_ot_senderdb_info,
        :proc1_ot_reportdb_info,
        :proc1_ot_logfile_info,
        :proc1_ot_etc,
        :proc1_ot_rst,
        :proc1_ot_rstmsg
    );
    
    if (sqlca.sqlcode != 0) {
        log_history(0,0,"[ERR] proc_check_mms_login_ext2 failed - SQLCODE: %d", sqlca.sqlcode);
        log_history(0,0,"[ERR] SQLERRM: %.70s", sqlca.sqlerrm.sqlerrmc);
        return FALSE;
    }
    
    // 결과 복사
    strcpy(szAPPName, proc1_ot_appname);
    strcpy(szSIP, proc1_ot_sip);
    *nmPID = proc1_ot_pid;
    *nmJOB = proc1_ot_job;
    *nUrlJob = proc1_ot_c_job;
    *nmPRT = proc1_ot_prt;
    *nmCNT = proc1_ot_cnt;
    *nRptWait = proc1_ot_rpt_cnt;
    strcpy(szServerInfo, proc1_ot_server_info);
    *nRptNoDataSleep = proc1_ot_rpt_sleep_cnt;
    strcpy(szSenderName, proc1_ot_sender_proc);
    strcpy(szReportName, proc1_ot_report_proc);
    strcpy(szSenderDBName, proc1_ot_senderdb_info);
    strcpy(szReportDBName, proc1_ot_reportdb_info);
    strcpy(szLogFilePath, proc1_ot_logfile_info);
    strcpy(szReserve, proc1_ot_etc);
    *nmRST = proc1_ot_rst;
    strcpy(szErrMsg, proc1_ot_rstmsg);
    
    log_history(0,0,"[INF] proc_check_mms_login_ext2 executed successfully, nmRST[%d] ErrMsg[%s]", *nmRST, szErrMsg);
    
    return TRUE;
}

// proc_get_limit_def 호출 함수  
int call_proc_get_limit_def(int pid, char* szLimitType, int* nDayWarnCnt, int* nDayLimitCnt,
                            int* nMonWarnCnt, int* nMonLimitCnt, char* szLimitFlag,
                            int* nDayAccCnt, int* nMonAccCnt, int* nmRST, char* szErrMsg) {
    
    // 입력 파라미터 설정
    proc2_in_pid = pid;
    
    // 출력 파라미터 초기화
    memset(proc2_ot_limit_type, 0, sizeof(proc2_ot_limit_type));
    proc2_ot_day_warn_cnt = 0;
    proc2_ot_day_limit_cnt = 0;
    proc2_ot_mon_warn_cnt = 0;
    proc2_ot_mon_limit_cnt = 0;
    memset(proc2_ot_limit_flag, 0, sizeof(proc2_ot_limit_flag));
    proc2_ot_day_acc_cnt = 0;
    proc2_ot_mon_acc_cnt = 0;
    proc2_ot_rst = -999;
    memset(proc2_ot_rstmsg, 0, sizeof(proc2_ot_rstmsg));
    
    log_history(0,0,"[INF] Calling proc_get_limit_def for PID[%d]", pid);
    
    // 프로시저 호출
    EXEC SQL CALL proc_get_limit_def(
        :proc2_in_pid,
        :proc2_ot_limit_type,
        :proc2_ot_day_warn_cnt,
        :proc2_ot_day_limit_cnt,
        :proc2_ot_mon_warn_cnt,
        :proc2_ot_mon_limit_cnt,
        :proc2_ot_limit_flag,
        :proc2_ot_day_acc_cnt,
        :proc2_ot_mon_acc_cnt,
        :proc2_ot_rst,
        :proc2_ot_rstmsg
    );
    
    if (sqlca.sqlcode != 0) {
        log_history(0,0,"[ERR] proc_get_limit_def failed - SQLCODE: %d", sqlca.sqlcode);
        log_history(0,0,"[ERR] SQLERRM: %.70s", sqlca.sqlerrm.sqlerrmc);
        return FALSE;
    }
    
    // 결과 복사
    strcpy(szLimitType, proc2_ot_limit_type);
    *nDayWarnCnt = proc2_ot_day_warn_cnt;
    *nDayLimitCnt = proc2_ot_day_limit_cnt;
    *nMonWarnCnt = proc2_ot_mon_warn_cnt;
    *nMonLimitCnt = proc2_ot_mon_limit_cnt;
    strcpy(szLimitFlag, proc2_ot_limit_flag);
    *nDayAccCnt = proc2_ot_day_acc_cnt;
    *nMonAccCnt = proc2_ot_mon_acc_cnt;
    *nmRST = proc2_ot_rst;
    strcpy(szErrMsg, proc2_ot_rstmsg);
    
    log_history(0,0,"[INF] proc_get_limit_def executed successfully, nmRST[%d]", *nmRST);
    
    return TRUE;
}

// SELECT 쿼리 함수들 (C 스타일)
int select_callback_data(int pid, char callback_list[][512], int* count) {
    log_history(0,0,"[INF] Selecting callback data for PID[%d]", pid);

    select_ptn_id = pid;
    *count = 0;

    EXEC SQL DECLARE callback_cursor CURSOR FOR
        SELECT PTN_ID, CALLBACK
        FROM TBL_CALLBACK
        WHERE PTN_ID = :select_ptn_id;

    EXEC SQL OPEN callback_cursor;

    if (sqlca.sqlcode != 0) {
        log_history(0,0,"[ERR] Failed to open callback cursor - SQLCODE: %d", sqlca.sqlcode);
        return FALSE;
    }

    while (1) {
        EXEC SQL FETCH callback_cursor INTO :select_ptn_id, :select_callback;

        if (sqlca.sqlcode == 1403) { // No data found
            break;
        }

        if (sqlca.sqlcode != 0) {
            log_history(0,0,"[ERR] Failed to fetch callback data - SQLCODE: %d", sqlca.sqlcode);
            EXEC SQL CLOSE callback_cursor;
            return FALSE;
        }

        // null-terminate strings
        select_callback[sizeof(select_callback)-1] = '\0';

        // trim whitespace
        char* trimmed = trim(select_callback, strlen(select_callback));
        if (trimmed && strlen(trimmed) > 0 && *count < 100) {
            strcpy(callback_list[*count], trimmed);
            (*count)++;
            log_history(0,0,"[INF] Added callback: %s", trimmed);
        }
    }

    EXEC SQL CLOSE callback_cursor;

    log_history(0,0,"[INF] Loaded %d callback entries", *count);
    return TRUE;
}

int select_dialcode_data(const char* dial_code_type, char dialcode_list[][64], int* count) {
    log_history(0,0,"[INF] Selecting dial code data for type[%s]", dial_code_type);

    strcpy(select_dial_code_type, dial_code_type);
    *count = 0;

    EXEC SQL DECLARE dialcode_cursor CURSOR FOR
        SELECT DIAL_CODE_TYPE, DIAL_CODE
        FROM TBL_ALLOW_DIAL_CODE
        WHERE DIAL_CODE_TYPE = :select_dial_code_type;

    EXEC SQL OPEN dialcode_cursor;

    if (sqlca.sqlcode != 0) {
        log_history(0,0,"[ERR] Failed to open dialcode cursor - SQLCODE: %d", sqlca.sqlcode);
        return FALSE;
    }

    while (1) {
        EXEC SQL FETCH dialcode_cursor INTO :select_dial_code_type, :select_dial_code;

        if (sqlca.sqlcode == 1403) { // No data found
            break;
        }

        if (sqlca.sqlcode != 0) {
            log_history(0,0,"[ERR] Failed to fetch dialcode data - SQLCODE: %d", sqlca.sqlcode);
            EXEC SQL CLOSE dialcode_cursor;
            return FALSE;
        }

        // null-terminate strings
        select_dial_code[sizeof(select_dial_code)-1] = '\0';

        // trim whitespace
        char* trimmed = trim(select_dial_code, strlen(select_dial_code));
        if (trimmed && strlen(trimmed) > 0 && *count < 100) {
            strcpy(dialcode_list[*count], trimmed);
            (*count)++;
            log_history(0,0,"[INF] Added dial code: %s", trimmed);
        }
    }

    EXEC SQL CLOSE dialcode_cursor;

    log_history(0,0,"[INF] Loaded %d dial code entries", *count);
    return TRUE;
}

// Pro*C 버전 checkLogon 함수
int checkLogon(char* buff, int type) {
    if (!buff) {
        log_history(0,0,"[ERR] checkLogon: Invalid buffer pointer");
        return -1;
    }

    CPacketUtil packetUtil;
    CLogonDbInfo logonDbInfo;

    char szSIP[100+1];
    char szAPPName[16];
    char szErrMsg[512];
    int nmPID;
    int nmJOB;
    int nmPRT;
    int nmCNT;
    int nmRST;
    int nUrlJob = 0;
    int nRptWait = 0;
    int nRptNoDataSleep = 3;
    char szSenderName[64];
    char szReportName[64];
    char szSenderDBName[64];
    char szReportDBName[64];
    char szLogFilePath[128];
    char szReserve[128];
    char szServerInfo[512];

    // 발송제한 관련 변수들
    char szLimitType[256];
    char szLimitFlag[256];
    int nDayWarnCnt = 0;
    int nDayLimitCnt = 0;
    int nMonWarnCnt = 0;
    int nMonLimitCnt = 0;
    int nDayAccCnt = 0;
    int nMonAccCnt = 0;

    char szCID[64];
    char szPWD[64];
    char szClassify[2];

    // 패킷에서 데이터 추출
    if (type == 1) {
        packetUtil.findValue(buff, "ID", szCID);
        packetUtil.findValue(buff, "PASSWORD", szPWD);
        packetUtil.findValue(buff, "REPORT", szClassify);
    }

    // 첫 번째 프로시저 호출
    alarm(60);
    if (!call_proc_check_mms_login_ext2(szCID, szPWD, szAPPName, szSIP, &nmPID, &nmJOB,
                                        &nUrlJob, &nmPRT, &nmCNT, &nRptWait,
                                        szServerInfo, &nRptNoDataSleep,
                                        szSenderName, szReportName,
                                        szSenderDBName, szReportDBName,
                                        szLogFilePath, szReserve,
                                        &nmRST, szErrMsg)) {
        log_history(0,0,"[ERR] call_proc_check_mms_login_ext2 failed");
        activeProcess = 0;
        alarm(0);
        return -1;
    }
    alarm(0);

    if (nmRST != 0) {
        log_history(0,0,"[ERR] proc_check_mms_login_ext2 failed - nmRST[%d][%s] ErrMsg[%s]", nmRST, szCID, szErrMsg);
        return nmRST;
    }

    // 두 번째 프로시저 호출
    alarm(60);
    if (!call_proc_get_limit_def(nmPID, szLimitType, &nDayWarnCnt, &nDayLimitCnt,
                                 &nMonWarnCnt, &nMonLimitCnt, szLimitFlag,
                                 &nDayAccCnt, &nMonAccCnt, &nmRST, szErrMsg)) {
        log_history(0,0,"[ERR] call_proc_get_limit_def failed");
        alarm(0);
        return -1;
    }
    alarm(0);

    if (nmRST != 0) {
        log_history(0,0,"[ERR] proc_get_limit_def failed - nmRST[%d] ErrMsg[%s]", nmRST, szErrMsg);
        return nmRST;
    }

    // 결과 데이터 설정
    memset(&logonDbInfo, 0x00, sizeof(logonDbInfo));

    // 안전한 문자열 복사 및 trim
    char* trimmed_ptr;

    trimmed_ptr = trim(szAPPName, strlen(szAPPName));
    if (trimmed_ptr) {
        strncpy(logonDbInfo.szAPPName, trimmed_ptr, sizeof(logonDbInfo.szAPPName)-1);
    }

    trimmed_ptr = trim(szSIP, strlen(szSIP));
    if (trimmed_ptr) {
        strncpy(logonDbInfo.szSIP, trimmed_ptr, sizeof(logonDbInfo.szSIP)-1);
    }

    logonDbInfo.nmPID = nmPID;
    logonDbInfo.nmJOB = nmJOB;
    logonDbInfo.nUrlJob = nUrlJob;
    logonDbInfo.nmPRT = nmPRT;
    logonDbInfo.nmCNT = nmCNT;
    logonDbInfo.nRptWait = nRptWait;

    trimmed_ptr = trim(szServerInfo, strlen(szServerInfo));
    if (trimmed_ptr) {
        strncpy(logonDbInfo.szServerInfo, trimmed_ptr, sizeof(logonDbInfo.szServerInfo)-1);
    }

    logonDbInfo.nRptNoDataSleep = nRptNoDataSleep;

    trimmed_ptr = trim(szSenderName, strlen(szSenderName));
    if (trimmed_ptr) {
        strncpy(logonDbInfo.szSenderName, trimmed_ptr, sizeof(logonDbInfo.szSenderName)-1);
    }

    trimmed_ptr = trim(szReportName, strlen(szReportName));
    if (trimmed_ptr) {
        strncpy(logonDbInfo.szReportName, trimmed_ptr, sizeof(logonDbInfo.szReportName)-1);
    }

    trimmed_ptr = trim(szSenderDBName, strlen(szSenderDBName));
    if (trimmed_ptr) {
        strncpy(logonDbInfo.szSenderDBName, trimmed_ptr, sizeof(logonDbInfo.szSenderDBName)-1);
    }

    trimmed_ptr = trim(szReportDBName, strlen(szReportDBName));
    if (trimmed_ptr) {
        strncpy(logonDbInfo.szReportDBName, trimmed_ptr, sizeof(logonDbInfo.szReportDBName)-1);
    }

    trimmed_ptr = trim(szLogFilePath, strlen(szLogFilePath));
    if (trimmed_ptr) {
        strncpy(logonDbInfo.szLogFilePath, trimmed_ptr, sizeof(logonDbInfo.szLogFilePath)-1);
    }

    trimmed_ptr = trim(szReserve, strlen(szReserve));
    if (trimmed_ptr) {
        strncpy(logonDbInfo.szReserve, trimmed_ptr, sizeof(logonDbInfo.szReserve)-1);
    }

    // 발송제한 정보 설정
    trimmed_ptr = trim(szLimitType, strlen(szLimitType));
    if (trimmed_ptr) {
        logonDbInfo.szLimitType[0] = trimmed_ptr[0];
    }

    trimmed_ptr = trim(szLimitFlag, strlen(szLimitFlag));
    if (trimmed_ptr) {
        logonDbInfo.szLimitFlag[0] = trimmed_ptr[0];
    }

    logonDbInfo.nDayWarnCnt = nDayWarnCnt;
    logonDbInfo.nDayLimitCnt = nDayLimitCnt;
    logonDbInfo.nMonWarnCnt = nMonWarnCnt;
    logonDbInfo.nMonLimitCnt = nMonLimitCnt;
    logonDbInfo.nDayAccCnt = nDayAccCnt;
    logonDbInfo.nMonAccCnt = nMonAccCnt;

    strcpy(logonDbInfo.szCID, szCID);
    strcpy(logonDbInfo.szPWD, szPWD);
    logonDbInfo.classify = szClassify[0];

    memset(buff, 0x00, SOCKET_BUFF);
    memcpy(buff, &logonDbInfo, sizeof(logonDbInfo));

    log_history(0,0,"[INF] checkLogon completed successfully");
    return 0;
}

// 간단한 프로토콜 처리 함수들
int classifyProtocol(CKSSocket& newSockfd, int size, char* bindPacket) {
    if (strstr(bindPacket, "BEGIN CONNECT") != NULL) {
        log_history(0,0,"[INF] socket_domain read - [%s]", bindPacket);
        logonTypeMMS(newSockfd, 1, bindPacket);
        return 0;
    } else if (strstr(bindPacket, "BEGIN CALLBACK") != NULL) {
        log_history(0,0,"[INF] socket_domain read - [%s]", bindPacket);
        getCallback(newSockfd, 1, bindPacket);
        return 0;
    } else if (strstr(bindPacket, "BEGIN DIALCODE") != NULL) {
        log_history(0,0,"[INF] socket_domain read - [%s]", bindPacket);
        getDialCode(newSockfd, 1, bindPacket);
        return 0;
    } else {
        log_history(0,0,"[INF] socket_domain read - [%s]", bindPacket);
        logonType(newSockfd, 1, bindPacket);
        return 0;
    }
}

int logonTypeMMS(CKSSocket& newSockfd, int type, char* bindPacket) {
    int ret = -1;
    TypeMsgBindAck ack;
    memset(&ack, 0x00, sizeof(ack));
    strcpy(ack.header.msgType, "2");

    ret = checkLogon(bindPacket, type);
    if (ret == 0) {
        strcpy(ack.header.result, "0000");
        ack.header.leng = sizeof(TypeMsgBindAck) - sizeof(Header);
        memcpy(ack.data, bindPacket, sizeof(CLogonDbInfo));
    } else {
        strcpy(ack.header.result, "9999");
        ack.header.leng = sizeof(TypeMsgBindAck) - sizeof(Header);
        memset(ack.data, 0x00, sizeof(ack.data));
    }

    ret = newSockfd.send((char*)&ack, sizeof(ack));
    if (ret < 0) {
        log_history(0,0,"[ERR] socket_domain send error[%s]", strerror(errno));
    }
    return 0;
}

int logonType(CKSSocket& newSockfd, int type, char* bindPacket) {
    int ret = -1;
    TypeMsgBindAck ack;
    memset(&ack, 0x00, sizeof(ack));
    strcpy(ack.header.msgType, "2");

    ret = checkLogon(bindPacket, type);
    if (ret == 0) {
        strcpy(ack.header.result, "0000");
        ack.header.leng = sizeof(TypeMsgBindAck) - sizeof(Header);
        memcpy(ack.data, bindPacket, sizeof(CLogonDbInfo));
    } else {
        strcpy(ack.header.result, "9999");
        ack.header.leng = sizeof(TypeMsgBindAck) - sizeof(Header);
        memset(ack.data, 0x00, sizeof(ack.data));
    }

    ret = newSockfd.send((char*)&ack, sizeof(ack));
    if (ret < 0) {
        log_history(0,0,"[ERR] socket_domain send error[%s]", strerror(errno));
    }
    return 0;
}

int getCallback(CKSSocket& newSockfd, int type, char* bindPacket) {
    int ret = -1;
    TypeMsgBindAck ack;
    memset(&ack, 0x00, sizeof(ack));
    strcpy(ack.header.msgType, "2");

    char callback_list[100][512];
    int count = 0;
    ret = loadCallback(bindPacket, type, callback_list, &count);

    if (ret == 0) {
        strcpy(ack.header.result, "0000");
        ack.header.leng = sizeof(TypeMsgBindAck) - sizeof(Header);
        // 콜백 데이터를 ack.data에 설정하는 로직 필요
    } else {
        strcpy(ack.header.result, "9999");
        ack.header.leng = sizeof(TypeMsgBindAck) - sizeof(Header);
        memset(ack.data, 0x00, sizeof(ack.data));
    }

    ret = newSockfd.send((char*)&ack, sizeof(ack));
    if (ret < 0) {
        log_history(0,0,"[ERR] socket_domain send error[%s]", strerror(errno));
    }
    return 0;
}

int loadCallback(char* buff, int type, char callback_list[][512], int* count) {
    TypeMsgGetCallback* pGetCallback = (TypeMsgGetCallback*)buff;

    if (!select_callback_data(pGetCallback->pid, callback_list, count)) {
        log_history(0,0,"[ERR] Failed to load callback data");
        activeProcess = 0;
        return -1;
    }

    return 0;
}

int getDialCode(CKSSocket& newSockfd, int type, char* bindPacket) {
    int ret = -1;
    TypeMsgBindAck ack;
    memset(&ack, 0x00, sizeof(ack));
    strcpy(ack.header.msgType, "2");

    char dialcode_list[100][64];
    int count = 0;
    ret = loadDialCode(bindPacket, type, dialcode_list, &count);

    if (ret == 0) {
        strcpy(ack.header.result, "0000");
        ack.header.leng = sizeof(TypeMsgBindAck) - sizeof(Header);
        // 다이얼 코드 데이터를 ack.data에 설정하는 로직 필요
    } else {
        strcpy(ack.header.result, "9999");
        ack.header.leng = sizeof(TypeMsgBindAck) - sizeof(Header);
        memset(ack.data, 0x00, sizeof(ack.data));
    }

    ret = newSockfd.send((char*)&ack, sizeof(ack));
    if (ret < 0) {
        log_history(0,0,"[ERR] socket_domain send error[%s]", strerror(errno));
    }
    return 0;
}

int loadDialCode(char* buff, int type, char dialcode_list[][64], int* count) {
    TypeMsgGetDialCode* pGetDialCode = (TypeMsgGetDialCode*)buff;

    if (!select_dialcode_data(pGetDialCode->dial_code_type, dialcode_list, count)) {
        log_history(0,0,"[ERR] Failed to load dial code data");
        activeProcess = 0;
        return -1;
    }

    return 0;
}

int configParse(char* confFile) {
    CKSConfig conf;

    if (conf.loadConf(confFile) < 0) {
        return -1;
    }

    conf.strncpy2(gConf.logonDBName, conf.FetchEntry("domain.logondb"), 64);
    if (gConf.logonDBName == NULL) {
        strcpy(gConf.logonDBName, "");
    }
    conf.strncpy2(gConf.dbID, conf.FetchEntry("db.id"), 16);
    if (gConf.dbID == NULL) {
        strcpy(gConf.dbID, "");
    }
    conf.strncpy2(gConf.dbPASS, conf.FetchEntry("db.pass"), 16);
    if (gConf.dbPASS == NULL) {
        strcpy(gConf.dbPASS, "");
    }
    conf.strncpy2(gConf.dbSID, conf.FetchEntry("db.sid"), 16);
    if (gConf.dbSID == NULL) {
        strcpy(gConf.dbSID, "");
    }

    return 0;
}

// main 함수
int main(int argc, char* argv[]) {
    char logMsg[256];
    char buff[SOCKET_BUFF];
    CKSSocket svrSockfd;
    CKSSocket newSockfd;

    if (ml_sub_init(PROCESS_NO, PROCESS_NAME, (char*)0, (char*)0) < 0) {
        perror("ml_sub_init Error.");
        ml_sub_end();
        return -1;
    }

    if (configParse(argv[1]) < 0) {
        log_history(0,0,"[ERR] config file parse error");
        goto END;
    }

    Init_Server();

    // 시그널 핸들러 등록 (Init_Server() 이후에 등록하여 CloseProcess를 덮어쓰기)
    signal(SIGALRM, alarm_handler);
    signal(SIGSEGV, segv_handler);
    signal(SIGABRT, segv_handler);
    signal(SIGHUP, term_handler);
    signal(SIGTERM, term_handler);
    signal(SIGINT, term_handler);
    signal(SIGQUIT, term_handler);
    signal(SIGUSR1, term_handler);

    log_history(0,0,"[INFO] Pro*C signal handlers registered after Init_Server(), overriding all CloseProcess signals");

    ret = svrSockfd.createDomainNon(gConf.logonDBName);
    if (ret < 0) {
        log_history(0,0,"[ERR] socket_domain logonDB create failed %s", strerror(errno));
        goto END;
    }

    if (!proc_connect()) {
        log_history(0,0,"[ERR] Pro*C db connect failed\n- SID[%s]\n- ID[%s]\n- PW[%s]", gConf.dbSID, gConf.dbID, gConf.dbPASS);
        goto END;
    }

    log_history(0,0,"[DEBUG] About to enter main loop, activeProcess=%d", activeProcess);

    while (activeProcess) {
        log_history(0,0,"[DEBUG] Loop iteration start, activeProcess=%d", activeProcess);

        hNewSocket = -1;

        wait_a_moment(10000);
        log_history(0,0,"[DEBUG] After wait_a_moment");

        ret = svrSockfd.select(0, 50000);
        log_history(0,0,"[DEBUG] select returned [%d]", ret);

        if (ret < 0) {
            if (errno == EINTR) {
                log_history(0,0,"[WARN] select interrupted by signal (EINTR), continuing...");
                continue;
            } else {
                log_history(0,0,"[ERR] select failed with errno %d: %s", errno, strerror(errno));
                log_history(0,0,"[ERR] Critical select error, terminating process");
                activeProcess = 0;
                break;
            }
        } else if (ret > 0) {
            hNewSocket = svrSockfd.accept();
            log_history(0,0,"[INF] accept returned socket [%d]", hNewSocket);
        }

        if (hNewSocket <= 0) {
            continue;
        }

        // new connection
        newSockfd.attach(hNewSocket);
        memset(buff, 0x00, sizeof(buff));
        log_history(0,0,"[INF] New connection established, waiting for data");

        ret = newSockfd.rcvmsg(buff);
        log_history(0,0,"[INF] rcvmsg returned [%d]", ret);
        if (ret == 0) {
            log_history(0,0,"[INF] Client disconnected");
            newSockfd.close();
            continue;
        }

        if (ret < 0) {
            log_history(0,0,"[ERR] rcvmsg error[%s]", strerror(errno));
            newSockfd.close();
            continue;
        }

        log_history(0,0,"[INF] Received data, classifying protocol");
        classifyProtocol(newSockfd, ret, buff);

        newSockfd.send("1", 2);
        newSockfd.close();
    }

END:
    svrSockfd.close();
    proc_disconnect();
    sprintf(logMsg, "close [%s]", PROCESS_NAME);
    monitoring(logMsg, 0, 0);
    ml_sub_end();

    return 0;
}
