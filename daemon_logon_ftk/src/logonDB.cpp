#include "logonDB.h"
#include <errno.h>
#include <string.h>
#include "string.h"
#include <iostream>
#include <signal.h>
using namespace std;
using namespace KSKYB;

// Global variables used in DatabaseORA_MMS
char _DATALOG[64] = "LOGON_DB";
extern char tmpLog3[1024];  // defined in DatabaseORA_MMS.cpp

// Signal handlers
void alarm_handler(int sig) {
    log_history(0,0,"[WARN] Database operation timeout (signal %d)", sig);
    // perform appropriate cleanup operations
}

void segv_handler(int sig) {
    log_history(0,0,"[FATAL] Segmentation fault detected (signal %d)", sig);
    log_history(0,0,"[FATAL] Process will terminate");
    // emergency cleanup operations
    activeProcess = false;
    exit(1);
}

void term_handler(int sig) {
    log_history(0,0,"[INFO] Termination signal received (signal %d)", sig);
    log_history(0,0,"[INFO] Initiating graceful shutdown...");
    activeProcess = false;
    // graceful shutdown - allow loop to terminate naturally
}

void logfunc(const char *s)
{
    // For debugging long SQL statements, write directly to file
    if (strstr(s, "Executing SQL") || strstr(s, "SQL prepared")) {
        FILE* debug_file = fopen("/tmp/logondb_sql_debug.log", "a");
        if (debug_file) {
            time_t now = time(NULL);
            struct tm* tm_info = localtime(&now);
            char timestamp[64];
            strftime(timestamp, sizeof(timestamp), "%Y%m%d,%H:%M:%S", tm_info);
            fprintf(debug_file, "[%s] - Original length: %zu - [%s]\n", timestamp, strlen(s), s);
            fclose(debug_file);
        }
    }

    // Debug: Check if string gets truncated in log_history
    size_t original_len = strlen(s);
    log_history(0,0,"[%s]",s);

    if (strstr(s, "Executing SQL") || strstr(s, "SQL prepared")) {
        FILE* debug_file = fopen("/tmp/logondb_sql_debug.log", "a");
        if (debug_file) {
            fprintf(debug_file, "After log_history call - original length was: %zu\n", original_len);
            fclose(debug_file);
        }
    }
}


int main(int argc, char* argv[])
{
    // signal handlers are registered after Init_Server()

    int ret;
    int hNewSocket;
    char logMsg[SOCKET_BUFF];
    char buff[SOCKET_BUFF];
    struct timeval outtime;
    CKSSocket svrSockfd;
    CKSSocket newSockfd;

	TypeMsgBindSnd* pLogonData;

    // use global DatabaseORA_MMS instance

    if (ml_sub_init(PROCESS_NO, PROCESS_NAME,(char*)0,(char*)0)<0) 
    {
        perror("ml_sub_init Error.");
        ml_sub_end();
        exit(1);
    }

    ret = configParse(argv[1]);
    
    if( ret != 0 )
    {
        printf("err\n");
        ml_sub_end();
        exit(1);
    }

    printf("[%s][%s]\n",PROCESS_NAME,gConf.logonDBName);

    sprintf(logMsg,"start [%s]",PROCESS_NAME);
    monitoring(logMsg,0,0);

    Init_Server();

    // register signal handlers (after Init_Server() to override CloseProcess)
    signal(SIGALRM, alarm_handler);
    signal(SIGSEGV, segv_handler);
    signal(SIGABRT, segv_handler);
    signal(SIGHUP, term_handler);   // HUP signal (override CloseProcess)
    signal(SIGTERM, term_handler);  // process termination request (override CloseProcess)
    signal(SIGINT, term_handler);   // Ctrl+C (override CloseProcess)
    signal(SIGQUIT, term_handler);  // Quit signal (override CloseProcess)
    signal(SIGUSR1, term_handler);  // User signal (override CloseProcess)

    log_history(0,0,"[INFO] Custom signal handlers registered after Init_Server(), overriding all CloseProcess signals");

    log_history(0,0,"[DEBUG] Creating domain socket: %s", gConf.logonDBName);
    ret = svrSockfd.createDomainNon(gConf.logonDBName);
    if( ret !=0 )
    {
        log_history(0,0,"[ERR] socket_domain logonDB create failed: %s", strerror(errno));
        goto END;
    }
    log_history(0,0,"[DEBUG] Domain socket created successfully");

    log_history(0,0,"[DEBUG] About to call oracle_connect()");
    if (!oracle_connect())
    {
        log_history(0,0,"[ERR] db connect failed\n- SID[%s]\n- ID[%s]\n- PW[%s]", gConf.dbSID, gConf.dbID, gConf.dbPASS);
        goto END;
    }
    log_history(0,0,"[DEBUG] oracle_connect() succeeded");

    log_history(0,0,"[DEBUG] About to enter main loop, activeProcess=%d", activeProcess);

    while(activeProcess)
    {
        //log_history(0,0,"[DEBUG] Loop iteration start, activeProcess=%d", activeProcess);

    		// 20170621 select ADD
        hNewSocket = -1;

        // select func
        wait_a_moment(10000);
        //log_history(0,0,"[DEBUG] After wait_a_moment");

        ret = svrSockfd.select(0,50000);
        //log_history(0,0,"[DEBUG] select returned [%d]", ret);

        if(ret < 0)
        {
            if(errno == EINTR)
            {
                log_history(0,0,"[WARN] select interrupted by signal (EINTR), continuing...");
                continue;
            }
            else
            {
                log_history(0,0,"[ERR] select failed with errno %d: %s", errno, strerror(errno));
                log_history(0,0,"[ERR] Critical select error, terminating process");
                activeProcess = false;
                break;
            }
        }
        else if(ret > 0)
        {
            hNewSocket = svrSockfd.accept();
            log_history(0,0,"[INF] accept returned socket [%d]", hNewSocket);
        }

        if( hNewSocket <= 0 )
        {
            continue;
        }

        // new connection
        newSockfd.attach(hNewSocket);
        memset(buff,0x00,sizeof(buff));
        log_history(0,0,"[INF] New connection established, waiting for data");

        ret = newSockfd.rcvmsg(buff);
        log_history(0,0,"[INF] rcvmsg returned [%d]", ret);
        if( ret == 0 )
        {
            newSockfd.close();
            log_history(0,0,"[ERR] socket_domain logonDB read failed - close by peer");
            continue;
        }

        if( ret < 0 )
        {
            newSockfd.close();
            log_history(0,0,"[ERR] socket_domain logonDB read failed - read Error");
            continue;
        }

        log_history(0,0,"[INF] Received data [%d bytes]: [%s]", ret, buff);


/* admin communication */
        viewPack(buff,ret);
        if( memcmp(buff,"getInfo",7) == 0 )
        {
            newSockfd.send("1",2);
            newSockfd.close();
            continue;
        }
/* admin communication */
        classifyProtocol(newSockfd,ret,buff);
        newSockfd.close();
    }

END:
    svrSockfd.close();
    oracle_disconnect();
    sprintf(logMsg,"close [%s]",PROCESS_NAME);
    monitoring(logMsg,0,0);
    ml_sub_end();

    return 0;
}

bool oracle_connect()
{
    int result = g_oracle.connectToOracle(gConf.dbuid, gConf.dbdsn);
    if (result <= 0)
    {
        log_history(0,0,"[ERR] connectToOracle Failed");
        return false;
    }
    log_history(0,0,"[INF] ORACLE CONNECT");

    return true;
}

bool oracle_disconnect() {
    g_oracle.closeFromOracle();
    log_history(0,0,"db disconnect success");
    return true;
}

int checkLogon(char* buff,int type) {
    // input parameter validation
    if (!buff) {
        log_history(0,0,"[ERR] checkLogon: Invalid buffer pointer");
        return -1;
    }

    CPacketUtil packetUtil;
    CLogonDbInfo logonDbInfo;

    // use DatabaseORA_MMS methods - no need for ORAPP query objects
    log_history(0,0,"[INF] checkLogon: Using DatabaseORA_MMS methods");

    char szSIP[100+1]; /* client's server IP list */
    char szAPPName[16]; /* appname for report */
    char szErrMsg[512]; /* procedure result string */
    int  nmPID; /* client pid */
    int  nmJOB;  /* client jobcode */
    int  nmPRT; /* client priority */
    int  nmCNT;  /* sender Sleep : rest time after sending one message */
    int  nmRST; /* succ : 0 fail: negative -2 : no data -1 : others */
    int  nUrlJob; /* jobcode when using url : -99 if not used */
    int  nRptWait; /* report Sleep : rest time after sending one message */

    char szServerInfo[128]; /* xxx.xxx.xxx.xxx:xxxxx => 21 + 1(|:delimiter) = 22 * 5 = 110 approximately 128 */
    int  nRptNoDataSleep; /* default 3 sec */
    char szSenderName[64]; /* relative path from home directory */
    char szReportName[64]; /* relative path from home directory */
    char szSenderDBName[64]; /* relative path from home directory localhost or ip:port */
    char szReportDBName[64]; /* relative path from home directory localhost or ip:port */
    char szLogFilePath[128]; /* full path */
    char szReserve[128]; /* additional reserved field etc */

        //* < brief sending limit related variable declaration
	char szLimitType[1+1];
	char szLimitFlag[1+1];
	int  nDayWarnCnt = 0;
	int  nMonWarnCnt = 0;
	int  nDayLimitCnt = 0;
	int  nMonLimitCnt = 0;
	int  nDayAccCnt = 0;
	int  nMonAccCnt = 0;
		
	char szPID[9];
    char szCID[16];
    char szPWD[16];
    char szClassify[4];

	memset(szCID, 0x00, sizeof(szCID));
	memset(szPWD, 0x00, sizeof(szPWD));
	memset(szClassify, 0x00, sizeof(szClassify));

	TypeMsgBindSnd *pSend = nullptr;
	if (type == 1) {
		printf(buff);
		packetUtil.findValue(buff, "ID", szCID);
		packetUtil.findValue(buff, "PASSWORD", szPWD);
		packetUtil.findValue(buff, "REPORT", szClassify);
		memset(&logonDbInfo, 0x00, sizeof(logonDbInfo));

		// safe string copy
		strncpy(logonDbInfo.szCID, szCID, sizeof(logonDbInfo.szCID) - 1);
		strncpy(logonDbInfo.szPWD, szPWD, sizeof(logonDbInfo.szPWD) - 1);
		logonDbInfo.szCID[sizeof(logonDbInfo.szCID) - 1] = '\0';
		logonDbInfo.szPWD[sizeof(logonDbInfo.szPWD) - 1] = '\0';

		log_history(0, 0, "ID[%s] PASSWORD[%s] REPORT[%s]", szCID, szPWD, szClassify);
	} else {
		// pointer validity verification
		pSend = (TypeMsgBindSnd *) buff;
		if (!pSend) {
			log_history(0, 0, "[ERR] checkLogon: Invalid TypeMsgBindSnd pointer");
			return -1;
		}

		// safe memory copy
		memset(&logonDbInfo, 0x00, sizeof(logonDbInfo));
		strncpy(logonDbInfo.szCID, pSend->szCID, sizeof(logonDbInfo.szCID) - 1);
		strncpy(logonDbInfo.szPWD, pSend->szPWD, sizeof(logonDbInfo.szPWD) - 1);
		logonDbInfo.szCID[sizeof(logonDbInfo.szCID) - 1] = '\0';
		logonDbInfo.szPWD[sizeof(logonDbInfo.szPWD) - 1] = '\0';

		log_history(0, 0, "volume conversion ID[] PASSWORD[] REPORT[%s]", szClassify);
	}

	// safe trim call
	if (strlen(logonDbInfo.szCID) > 0) {
		trim(logonDbInfo.szCID, strlen(logonDbInfo.szCID));
	}
	if (strlen(logonDbInfo.szPWD) > 0) {
		trim(logonDbInfo.szPWD, strlen(logonDbInfo.szPWD));
	}

	memset(szSIP			,0x00	,sizeof(szSIP));			//CCL(szSIP);
    memset(szAPPName		,0x00	,sizeof(szAPPName));		//CCL(szAPPName);
    memset(szErrMsg			,0x00	,sizeof(szErrMsg));			//CCL(szErrMsg);

    memset(szServerInfo		,0x00	,sizeof(szServerInfo));		//CCL(szServerInfo);
    memset(szSenderName		,0x00	,sizeof(szSenderName));		//CCL(szSenderName);
    memset(szReportName		,0x00	,sizeof(szReportName));		//CCL(szReportName);
    memset(szSenderDBName	,0x00	,sizeof(szSenderDBName));	//CCL(szSenderDBName);
    memset(szReportDBName	,0x00	,sizeof(szReportDBName));	//CCL(szReportDBName);
    memset(szLogFilePath	,0x00	,sizeof(szLogFilePath));	//CCL(szLogFilePath);
    memset(szReserve		,0x00	,sizeof(szReserve));		//CCL(szReserve);

    // Initialize numeric bind variables
    nmPID = 0;
    nmJOB = 0;
    nUrlJob = 0;
    nmPRT = 0;
    nmCNT = 0;
    nRptWait = 0;
	nRptNoDataSleep = 3;
    nmRST = -999;

    alarm(60);
    log_history(0,0,"[INF] Executing proc_check_mms_login_ext2 for CID[%s] PWD[%s]", logonDbInfo.szCID, logonDbInfo.szPWD);
    log_history(0,0,"[DEBUG] Before calling g_oracle.procCheckMmsLoginExt2 (class-based - parameters reduced from 20 to 2)");

    // use class-based methods - parameter count reduced from 20 to 2
    KSKYB::CMmsLoginInput input(logonDbInfo.szCID, logonDbInfo.szPWD);
    KSKYB::CMmsLoginOutput output;

    int result1 = g_oracle.procCheckMmsLoginExt2(input, output);

    // copy output values to existing variables
    strncpy(szAPPName, output.appname, sizeof(szAPPName) - 1);
    szAPPName[sizeof(szAPPName) - 1] = '\0';
    strncpy(szSIP, output.sip, sizeof(szSIP) - 1);
    szSIP[sizeof(szSIP) - 1] = '\0';
    nmPID = output.pid;
    nmJOB = output.job;
    nUrlJob = output.c_job;
    nmPRT = output.prt;
    nmCNT = output.cnt;
    nRptWait = output.rpt_cnt;
    strncpy(szServerInfo, output.server_info, sizeof(szServerInfo) - 1);
    szServerInfo[sizeof(szServerInfo) - 1] = '\0';
    nRptNoDataSleep = output.rpt_sleep_cnt;
    strncpy(szSenderName, output.sender_proc, sizeof(szSenderName) - 1);
    szSenderName[sizeof(szSenderName) - 1] = '\0';
    strncpy(szReportName, output.report_proc, sizeof(szReportName) - 1);
    szReportName[sizeof(szReportName) - 1] = '\0';
    strncpy(szSenderDBName, output.senderdb_info, sizeof(szSenderDBName) - 1);
    szSenderDBName[sizeof(szSenderDBName) - 1] = '\0';
    strncpy(szReportDBName, output.reportdb_info, sizeof(szReportDBName) - 1);
    szReportDBName[sizeof(szReportDBName) - 1] = '\0';
    strncpy(szLogFilePath, output.logfile_info, sizeof(szLogFilePath) - 1);
    szLogFilePath[sizeof(szLogFilePath) - 1] = '\0';
    strncpy(szReserve, output.etc, sizeof(szReserve) - 1);
    szReserve[sizeof(szReserve) - 1] = '\0';
    nmRST = output.rst;
    strncpy(szErrMsg, output.rstmsg, sizeof(szErrMsg) - 1);
    szErrMsg[sizeof(szErrMsg) - 1] = '\0';

#if (DEBUG >= 5)
    log_history(0,0,"[DEBUG] After calling g_oracle.procCheckMmsLoginExt2 (class-based), result1[%d]", result1);
    log_history(0,0,"[DEBUG] Class-based method returned values: nmPID[%d] nmJOB[%d] nmPRT[%d] nmCNT[%d]", nmPID, nmJOB, nmPRT, nmCNT);
    log_history(0,0,"[INFO] Successfully used class-based procCheckMmsLoginExt2 - parameters reduced from 20 to 2");
    log_history(0,0,"[INFO] Class-based method eliminates pointer issues and improves maintainability");

	//output all parameter values here
	log_history(0,0,"[DEBUG] Values after procedure: szAPPName[%s] szSIP[%s] szServerInfo[%s] szSenderName[%s] szReportName[%s] szSenderDBName[%s] szReportDBName[%s] szLogFilePath[%s] szReserve[%s]",
		szAPPName, szSIP, szServerInfo, szSenderName, szReportName, szSenderDBName, szReportDBName, szLogFilePath, szReserve);
	log_history(0,0,"[DEBUG] Values after procedure: nmPID[%d] nmJOB[%d] nUrlJob[%d] nmPRT[%d] nmCNT[%d] nRptWait[%d] nRptNoDataSleep[%d]",
		nmPID, nmJOB, nUrlJob, nmPRT, nmCNT, nRptWait, nRptNoDataSleep);
#endif
	

    if (result1 <= 0)
    {
        log_history(0,0,"[ERR] activeProcess = false, proc_check_mms_login_ext2 failed");
        activeProcess = false;
        alarm(0);
        return -1;
    }
    alarm(0);

    log_history(0,0,"[INF] proc_check_mms_login_ext2 executed successfully, nmRST[%d] ErrMsg[%s]", nmRST, szErrMsg);

    if( nmRST != 0 )
    {
		log_history(0,0,"[ERR] db select failed - proc_check_logon_ext2 nmRST[%d][%s] ErrMsg[%s]",nmRST,logonDbInfo.szCID,szErrMsg);		
		return nmRST;
    }

	// safe string copy and trim
	char *trimmed_ptr;

	trimmed_ptr = trim(szSIP, strlen(szSIP));
	if (trimmed_ptr) {
		strncpy(logonDbInfo.szSIP, trimmed_ptr, sizeof(logonDbInfo.szSIP) - 1);
		logonDbInfo.szSIP[sizeof(logonDbInfo.szSIP) - 1] = '\0';
	}

	trimmed_ptr = trim(szAPPName, strlen(szAPPName));
	if (trimmed_ptr) {
		strncpy(logonDbInfo.szAPPName, trimmed_ptr, sizeof(logonDbInfo.szAPPName) - 1);
		logonDbInfo.szAPPName[sizeof(logonDbInfo.szAPPName) - 1] = '\0';
	}

	trimmed_ptr = trim(szServerInfo, strlen(szServerInfo));
	if (trimmed_ptr && strlen(trimmed_ptr) > 0) {
		strncpy(logonDbInfo.szServerInfo, trimmed_ptr, sizeof(logonDbInfo.szServerInfo) - 1);
		logonDbInfo.szServerInfo[sizeof(logonDbInfo.szServerInfo) - 1] = '\0';
	} else {
		// use actual returned value from procedure (when it becomes empty string due to NULL handling)
		//strncpy(logonDbInfo.szServerInfo, "*************:43000", sizeof(logonDbInfo.szServerInfo) - 1);
		//logonDbInfo.szServerInfo[sizeof(logonDbInfo.szServerInfo) - 1] = '\0';
		//log_history(0, 0, "[INFO] Applied actual procedure value for szServerInfo: [%s]", logonDbInfo.szServerInfo);
	}

	logonDbInfo.nRptNoDataSleep = nRptNoDataSleep;

	trimmed_ptr = trim(szSenderName, strlen(szSenderName));
	if (trimmed_ptr && strlen(trimmed_ptr) > 0 && strcmp(trimmed_ptr, "") != 0) {
		strncpy(logonDbInfo.szSenderName, trimmed_ptr, sizeof(logonDbInfo.szSenderName) - 1);
		logonDbInfo.szSenderName[sizeof(logonDbInfo.szSenderName) - 1] = '\0';
	} 

	trimmed_ptr = trim(szReportName, strlen(szReportName));
	if (trimmed_ptr && strlen(trimmed_ptr) > 0 && strcmp(trimmed_ptr, "") != 0) {
		strncpy(logonDbInfo.szReportName, trimmed_ptr, sizeof(logonDbInfo.szReportName) - 1);
		logonDbInfo.szReportName[sizeof(logonDbInfo.szReportName) - 1] = '\0';
	} 

	trimmed_ptr = trim(szSenderDBName, strlen(szSenderDBName));
	if (trimmed_ptr && strlen(trimmed_ptr) > 0 && strcmp(trimmed_ptr, "") != 0) {
		strncpy(logonDbInfo.szSenderDBName, trimmed_ptr, sizeof(logonDbInfo.szSenderDBName) - 1);
		logonDbInfo.szSenderDBName[sizeof(logonDbInfo.szSenderDBName) - 1] = '\0';
	} 

	trimmed_ptr = trim(szReportDBName, strlen(szReportDBName));
	if (trimmed_ptr && strlen(trimmed_ptr) > 0 && strcmp(trimmed_ptr, "") != 0) {
		strncpy(logonDbInfo.szReportDBName, trimmed_ptr, sizeof(logonDbInfo.szReportDBName) - 1);
		logonDbInfo.szReportDBName[sizeof(logonDbInfo.szReportDBName) - 1] = '\0';
	} 

	trimmed_ptr = trim(szLogFilePath, strlen(szLogFilePath));
	if (trimmed_ptr && strlen(trimmed_ptr) > 0 && strcmp(trimmed_ptr, "") != 0) {
		strncpy(logonDbInfo.szLogFilePath, trimmed_ptr, sizeof(logonDbInfo.szLogFilePath) - 1);
		logonDbInfo.szLogFilePath[sizeof(logonDbInfo.szLogFilePath) - 1] = '\0';
	} 

	trimmed_ptr = trim(szReserve, strlen(szReserve));
	if (trimmed_ptr) {
		strncpy(logonDbInfo.szReserve, trimmed_ptr, sizeof(logonDbInfo.szReserve) - 1);
		logonDbInfo.szReserve[sizeof(logonDbInfo.szReserve) - 1] = '\0';
	}

	logonDbInfo.nmPID 		= nmPID;
	logonDbInfo.nmJOB 		= nmJOB;
	logonDbInfo.nmPRT 		= nmPRT;
	logonDbInfo.nmCNT 		= nmCNT;
	logonDbInfo.nmRST 		= nmRST;
	logonDbInfo.nUrlJob 	= nUrlJob;
	logonDbInfo.nRptWait 	= nRptWait;

	//* < brief sending limit PROC processing
	nmRST = -999;
	memset(szLimitType,0x00,sizeof(szLimitType));
	memset(szLimitFlag,0x00,sizeof(szLimitFlag));

	sprintf(szPID,"%d", logonDbInfo.nmPID);

	alarm(60);

	// call class-based DatabaseORA_MMS method - parameter count reduced from 11 to 2
	KSKYB::CLimitDefInput limitInput(atoi(szPID));
	KSKYB::CLimitDefOutput limitOutput;

	int result2 = g_oracle.procGetLimitDef(limitInput, limitOutput);

	// copy output values to existing variables
	strncpy(szLimitType, limitOutput.limit_type, sizeof(szLimitType) - 1);
	szLimitType[sizeof(szLimitType) - 1] = '\0';
	nDayWarnCnt = limitOutput.day_warn_cnt;
	nDayLimitCnt = limitOutput.day_limit_cnt;
	nMonWarnCnt = limitOutput.mon_warn_cnt;
	nMonLimitCnt = limitOutput.mon_limit_cnt;
	strncpy(szLimitFlag, limitOutput.limit_flag, sizeof(szLimitFlag) - 1);
	szLimitFlag[sizeof(szLimitFlag) - 1] = '\0';
	nDayAccCnt = limitOutput.day_acc_cnt;
	nMonAccCnt = limitOutput.mon_acc_cnt;
	nmRST = limitOutput.rst;
	strncpy(szErrMsg, limitOutput.rstmsg, sizeof(szErrMsg) - 1);
	szErrMsg[sizeof(szErrMsg) - 1] = '\0';

	if (result2 <= 0) {
		log_history(0, 0, "[ERR] proc_get_limit_def (class-based) failed");
		activeProcess = false;
		alarm(0);
		return -1;
	}
	alarm(0);

	if (nmRST != 0) {
		log_history(0, 0, "[ERR] db select failed - proc_get_limit_def (class-based) - nmRST[%d] ErrMsg[%s]", nmRST,
					szErrMsg);
		return nmRST;
	}

	log_history(0, 0, "[INFO] Successfully used procGetLimitDef");
	// safe string copy and trim
	trimmed_ptr = trim(szLimitType, strlen(szLimitType));
	if (trimmed_ptr) {
		strncpy(logonDbInfo.szLimitType, trimmed_ptr, sizeof(logonDbInfo.szLimitType) - 1);
		logonDbInfo.szLimitType[sizeof(logonDbInfo.szLimitType) - 1] = '\0';
	}

	trimmed_ptr = trim(szLimitFlag, strlen(szLimitFlag));
	if (trimmed_ptr) {
		strncpy(logonDbInfo.szLimitFlag, trimmed_ptr, sizeof(logonDbInfo.szLimitFlag) - 1);
		logonDbInfo.szLimitFlag[sizeof(logonDbInfo.szLimitFlag) - 1] = '\0';
	}

	logonDbInfo.nDayWarnCnt = nDayWarnCnt;
	logonDbInfo.nDayLimitCnt = nDayLimitCnt;
	logonDbInfo.nMonWarnCnt = nMonWarnCnt;
	logonDbInfo.nMonLimitCnt = nMonLimitCnt;
	logonDbInfo.nDayAccCnt = nDayAccCnt;
	logonDbInfo.nMonAccCnt = nMonAccCnt;
	logonDbInfo.classify = szClassify[0];

	memset(buff, 0x00,SOCKET_BUFF);
	memcpy(buff, &logonDbInfo, sizeof(logonDbInfo));	

	log_history(0, 0, "[INF] checkLogon completed successfully");
	return 0;
}

int configParse(char* file)
{
	Q_Entry *pEntry;
	int  i;
	int  nRetFlag = TRUE;
	
	char *pszTmp;
	CKSConfig conf;
// read mert conf
	if((pEntry = conf.qfDecoder(file)) == NULL) 
	{
		printf("WARNING: Configuration file(%s) not found.\n", file);
		return -1;
	}

	conf.strncpy2(gConf.logonDBName	,conf.FetchEntry("domain.logondb"),64);
	if( gConf.logonDBName == NULL )
	{
		strcpy(gConf.logonDBName,"");
	}
	conf.strncpy2(gConf.dbID		,conf.FetchEntry("db.id"),16);
	if( gConf.dbID == NULL )
	{	
		strcpy(gConf.dbID,"");
	}
	conf.strncpy2(gConf.dbPASS		,conf.FetchEntry("db.pass"),16);
	if( gConf.dbPASS == NULL )
	{
		strcpy(gConf.dbPASS,"");
	}
	conf.strncpy2(gConf.dbSID		,conf.FetchEntry("db.sid"),16);
	if( gConf.dbSID == NULL )
	{
	   	strcpy(gConf.dbSID,"");
	}

	// add same settings as senderFtalkProDB.cpp
	conf.strncpy2(gConf.dbuid		,conf.FetchEntry("db.uid"),64);
	if( gConf.dbuid == NULL )
	{
	   	strcpy(gConf.dbuid,"");
	}

	conf.strncpy2(gConf.dbdsn		,conf.FetchEntry("db.dsn"),64);
	if( gConf.dbdsn == NULL )
	{
	   	strcpy(gConf.dbdsn,"");
	}

	// add configuration verification log
	log_history(0,0,"[INF] Config loaded - dbuid[%s] dbdsn[%s]", gConf.dbuid, gConf.dbdsn);

	return 0;
}

int classifyProtocol(CKSSocket& newSockfd,int size,char* bindPacket)
{
    int ret;
    if(strstr(bindPacket,"BEGIN CONNECT") != NULL )
    {
        log_history(0,0,"[INF] socket_domain read - [%s]",bindPacket);

        logonTypeMMS(newSockfd,1,bindPacket);
        return 0;
    }

    switch(size)
    {
        case sizeof(TypeMsgBindSnd):
            logonType(newSockfd, 5, bindPacket);
            break;
        case sizeof(TypeMsgBindSnd_v3):
            logonType(newSockfd, 3, bindPacket);
            break;
		case sizeof(TypeMsgGetCallback):
	cerr<<__FILE__<<":"<<__LINE__<<endl;
			getCallback(newSockfd, 4, bindPacket);
			break;
		case sizeof(TypeMsgGetDialCode):
			getDialCode(newSockfd, 4, bindPacket);
			break;
		default:
            log_history(0,0,"classifyProtocol Error : not matching protocol");
            ret = -1;
            break;
    }

    return 0;
}

int logonTypeMMS(CKSSocket& newSockfd,int type,char* bindPacket)
{
    // create separate MMS logon procedure
    // use same procedure for now
    // checkLogonMMS function

    int ret=-1;

    TypeMsgBindAck ack;
    memset(&ack	,0x00	,sizeof(ack));
    strcpy(ack.header.msgType,"2");
    strcpy(ack.header.msgLeng,"2");
    string code;
    string desc;

    code = "";
    desc = "";
    code.reserve(0);
    desc.reserve(0);

	log_history(0,0,"[INF] Calling checkLogon for MMS");
	ret = checkLogon(bindPacket,type);
	log_history(0,0,"[INF] checkLogon returned [%d]", ret);
    if( ret < 0 )
    {
        switch(ret)
		{
            case -1: /* other Error */
                memcpy(ack.szResult,"33",2);
                code = "300";
                desc = "etc";
                log_history(0,0,"[ERR] checkLogon failed with code -1 (other error)");
                break;
            case -2: /* not found */
                memcpy(ack.szResult,"98",2);
                code = "200";
                desc = "id/pass not found";
                log_history(0,0,"[ERR] checkLogon failed with code -2 (not found)");
                break;
            default:
                ret = -3;
                memcpy(ack.szResult,"33",2);
                code = "300";
                desc = "etc";
                log_history(0,0,"[ERR] checkLogon failed with code %d (default)", ret);
                break;
        }
    }
	else
	{
        memcpy(ack.szResult,"00",2);
        code = "100";
        desc = "succ";
        log_history(0,0,"[INF] checkLogon succeeded");
    }

    string strPacket;
    strPacket = "BEGIN ACK\r\nCODE:" + code + "\r\nDESC:" + desc + "\r\nEND\r\n";

    newSockfd.send((char*)strPacket.c_str(),strPacket.length());

    if( ret < 0 )
  	{
        return 0; /* logon Fail */
	}

    /* logonDbInfo send */
    char buff[SOCKET_BUFF];
    memset(buff,0x00,sizeof(buff));//CCL(buff);

    log_history(0,0,"[INF] Waiting for socket_domain data from logonSession");
    ret = newSockfd.rcvmsg(buff);
    log_history(0,0,"[INF] socket_domain rcvmsg returned [%d]", ret);
    if( ret == 0 )
    {
        log_history(0,0,"[ERR] socket_domain read time out");
        return 0;
    }

    if( ret < 0 )
    {
        log_history(0,0,"[ERR] socket_domain read error [%s]", strerror(errno));
        return 0;
    }

    log_history(0,0,"[INF] socket_domain received data [%d bytes]: [%s]", ret, buff);

    if( memcmp(buff,"OK",2) != 0 )
    {
        log_history(0,0,"[ERR] socket_domain read not OK Error[%.2s]",buff);
        return 0;
    }

    ret = newSockfd.send(bindPacket,sizeof(CLogonDbInfo));
    if( ret != sizeof(CLogonDbInfo) )
    {
        log_history(0,0,"[ERR] socket_domain send error[%s]", strerror(errno));
	}
    return 0;
}

int logonType(CKSSocket& newSockfd,int type,char* bindPacket)
{
    int ret=-1;
    TypeMsgBindAck ack;
    memset(&ack,0x00,sizeof(ack));
    strcpy(ack.header.msgType,"2");
    strcpy(ack.header.msgLeng,"2");
    ret = checkLogon(bindPacket,type);
    if( ret < 0 )
    {
        switch(ret){
            case -1: /* other Error */
                memcpy(ack.szResult,"33",2);
                break;
            case -2: /* not found */
                memcpy(ack.szResult,"98",2);
                break;
            default:
                ret = -3;
                memcpy(ack.szResult,"33",2);
                break;
        }
    } else {
        memcpy(ack.szResult,"00",2);
    }

    CLogonDbInfo* pLogonDbInfo = (CLogonDbInfo*)bindPacket;
    printf("logon db [%s]\n",pLogonDbInfo->szCID);
    fflush(stdout);
    if( strcmp(pLogonDbInfo->szCID,"KNBANK") == 0 )
    {
        memcpy(ack.header.msgType,"02",2);
        memcpy(ack.header.msgLeng,"0002",4);
    }

    newSockfd.send((char*)&ack,sizeof(ack));
    if( ret < 0 )
	{
		log_history(0,0,"logonType() send error");
		return 0; /* logon Fail */
	}

    /* logonDbInfo send */
    char buff[SOCKET_BUFF];
    CCL(buff);

    ret = newSockfd.rcvmsg(buff);
    if( ret == 0 )
    {
        log_history(0,0,"logonDbInfo OK waiting session socket close:Error");
        return 0;
    }

    if( ret < 0 )
    {
        log_history(0,0,"logonDbInfo OK waiting session socket Error[%s]",
                strerror(errno));
        return 0;
    }

    if( memcmp(buff,"OK",2) != 0 )
    {
        log_history(0,0,"logonDbInfo OK waiting session socket packet not ok: Error[%.2s]",
                buff);
        return 0;

    }

    ret = newSockfd.send(bindPacket,sizeof(CLogonDbInfo));
    if( ret != sizeof(CLogonDbInfo) )
        log_history(0,0,"logonDbInfo ->session send socket Error[%s]",
                strerror(errno));


    return 0;
}

int getCallback(CKSSocket& newSockfd, int type, char* bindPacket)
{
    int ret=-1;
    TypeMsgBindAck ack;
    memset(&ack,0x00,sizeof(ack));
    strcpy(ack.header.msgType,"2");
    strcpy(ack.header.msgLeng,"2");

	cerr<<__FILE__<<":"<<__LINE__<<endl;
	set<string> set_callback_list;
    // TODO: need to change loadCallback function to DatabaseORA_MMS
    // ret = loadCallback(bindPacket, type, set_callback_list);
    ret = -1; // temporarily handle as failure
	cerr<<__FILE__<<":"<<__LINE__<<endl;
    if( ret < 0 )
    {
        switch(ret){
            case -1: /* other Error */
                memcpy(ack.szResult,"33",2);
                break;
            case -2: /* not found */
                memcpy(ack.szResult,"98",2);
                break;
            default:
                ret = -3;
                memcpy(ack.szResult,"33",2);
                break;
        }
    } 
	else 
	{
        memcpy(ack.szResult,"00",2);
	cerr<<__FILE__<<":"<<__LINE__<<endl;
    }

    newSockfd.send((char*)&ack,sizeof(ack));
    if( ret < 0 )
	{
		log_history(0,0,"logonType() send error");
		return 0; /* logon Fail */
	}

    /* logonDbInfo send */
    char buff[SOCKET_BUFF];
    CCL(buff);

    ret = newSockfd.rcvmsg(buff);
    if( ret == 0 )
    {
        log_history(0,0,"logonDbInfo OK waiting session socket close:Error");
        return 0;
    }

    if( ret < 0 )
    {
        log_history(0,0,"logonDbInfo OK waiting session socket Error[%s]",
                strerror(errno));
        return 0;
    }

    if( memcmp(buff,"OK",2) != 0 )
    {
        log_history(0,0,"logonDbInfo OK waiting session socket packet not ok: Error[%.2s]",
                buff);
        return 0;

    }

	cerr<<__FILE__<<":"<<__LINE__<<endl;
	set<string>::iterator itr;
	for(itr = set_callback_list.begin(); itr != set_callback_list.end(); itr++)
	{
		string tmp = *itr;
    	ret = newSockfd.send((char*)tmp.c_str(), tmp.size());

		CCL(buff);
    	ret = newSockfd.rcvmsg(buff);

		if( memcmp(buff,"OK",2) != 0 )
		{
			log_history(0,0,"logonDbInfo callback info individual transmission ok reception failed: Error[%.2s]", buff);
			return 0;
		}
	}

    return 0;
}

int loadCallback(char* buff, int type, set<string> &set_callback_list)
{

    CPacketUtil packetUtil;
    // TODO: need to change ORAPP::Query to DatabaseORA_MMS
    // ORAPP::Query *query = db.query();
    CLogonDbInfo logonDbInfo;

	// TODO: need to change to DatabaseORA_MMS
	/*
	TypeMsgGetCallback* pGetCallback = (TypeMsgGetCallback*)buff;

	char qry[1024];
	sprintf(qry, "SELECT PTN_ID, CALLBACK FROM TBL_CALLBACK WHERE PTN_ID = %d", pGetCallback->pid);
	*query << qry;

	log_history(0, 0,"Executing %s", qry);

	if (!query->execute())
    {
        log_history(0,0,"[ERR] activeProcess = false, db select failed - [%s]\n", db.error().c_str());
		activeProcess = false;
        alarm(0);
        return -1;
    }

	alarm(0);

	ORAPP::Row *r;
	cerr<<__FILE__<<":"<<__LINE__<<endl;

	while ((r = query->fetch())) {
	cerr<<__FILE__<<":"<<__LINE__<<endl;
		printf("::: rows fetched = %u, width = %u\n", query->rows(), r->width());

		int i;
		for (i = 0; i < r->width(); i++)
			printf(":::    row%u named %s\n", i, r->name(i));

		int ptn_id = (int)(*r)["ptn_id"];
		char *callback = (char*)(*r)["callback"];
		trim(callback, strlen(callback));

		printf("ptn_id = [%d], callback = [%s]", ptn_id, callback);

		char key[128];

		sprintf(key, "%s", callback);

		set_callback_list.insert(key);
	}
	cerr<<__FILE__<<":"<<__LINE__<<endl;
	*/

    return 0;
}

int getDialCode(CKSSocket& newSockfd, int type, char* bindPacket)
{
    int ret=-1;
    TypeMsgBindAck ack;
    memset(&ack,0x00,sizeof(ack));
    strcpy(ack.header.msgType,"2");
    strcpy(ack.header.msgLeng,"2");

	set<string> set_dialcode_list;
    // TODO: need to change loadDialCode function to DatabaseORA_MMS
    // ret = loadDialCode(bindPacket, type, set_dialcode_list);
    ret = -1; // temporarily handle as failure
    if( ret < 0 )
    {
        switch(ret){
            case -1: /* other Error */
                memcpy(ack.szResult,"33",2);
                break;
            case -2: /* not found */
                memcpy(ack.szResult,"98",2);
                break;
            default:
                ret = -3;
                memcpy(ack.szResult,"33",2);
                break;
        }
    } else {
        memcpy(ack.szResult,"00",2);
    }

    newSockfd.send((char*)&ack,sizeof(ack));
    if( ret < 0 )
	{
		log_history(0,0,"logonType() send error");
		return 0; /* logon Fail */
	}

    /* logonDbInfo send */
    char buff[SOCKET_BUFF];
    CCL(buff);

    ret = newSockfd.rcvmsg(buff);
    if( ret == 0 )
    {
        log_history(0,0,"logonDbInfo OK waiting session socket close:Error");
        return 0;
    }

    if( ret < 0 )
    {
        log_history(0,0,"logonDbInfo OK waiting session socket Error[%s]",
                strerror(errno));
        return 0;
    }

    if( memcmp(buff,"OK",2) != 0 )
    {
        log_history(0,0,"logonDbInfo OK waiting session socket packet not ok: Error[%.2s]",
                buff);
        return 0;

    }

	set<string>::iterator itr;
	for(itr = set_dialcode_list.begin(); itr != set_dialcode_list.end(); itr++)
	{
		string tmp = *itr;
    	ret = newSockfd.send((char*)tmp.c_str(), tmp.size());

		CCL(buff);
    	ret = newSockfd.rcvmsg(buff);

		if( memcmp(buff,"OK",2) != 0 )
		{
			log_history(0,0,"logonDbInfo callback info individual transmission ok reception failed: Error[%.2s]", buff);
			return 0;
		}
	}

    return 0;
}

int loadDialCode(char* buff, int type, set<string> &set_dialcode_list)
{
    CPacketUtil packetUtil;
    // TODO: need to change ORAPP::Query to DatabaseORA_MMS
    // ORAPP::Query *query = db.query();
    CLogonDbInfo logonDbInfo;

	// TODO: need to change to DatabaseORA_MMS
	/*
	TypeMsgGetDialCode* pGetDialCode = (TypeMsgGetDialCode*)buff;

	char qry[1024];
	sprintf(qry, "SELECT DIAL_CODE_TYPE, DIAL_CODE FROM TBL_ALLOW_DIAL_CODE WHERE DIAL_CODE_TYPE = %s"
			, pGetDialCode->dial_code_type);

	*query << qry;

	log_history(0, 0,"Executing %s", qry);

	if (!query->execute())
    {
        log_history(0,0,"[ERR] activeProcess = false, db select failed - [%s]\n", db.error().c_str());
		activeProcess = false;
        alarm(0);
        return -1;
    }

	alarm(0);

	ORAPP::Row *r;

	while ((r = query->fetch())) {
		printf("::: rows fetched = %u, width = %u\n", query->rows(), r->width());

		int i;
		for (i = 0; i < r->width(); i++)
			printf(":::    row%u named %s\n", i, r->name(i));

		char *dial_code_type = (char*)(*r)["dial_code_type"];
		char *dial_code = (char*)(*r)["dial_code"];
		trim(dial_code, strlen(dial_code));

		printf("dial_code_type = [%s], dial_code = [%s]", dial_code_type, dial_code);

		char key[128];

		sprintf(key, "%s", dial_code);

		set_dialcode_list.insert(key);
	}
	*/

    return 0;
}


