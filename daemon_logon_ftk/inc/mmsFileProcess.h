#ifndef _MMS_FILE_PROCESS_H_
#define _MMS_FILE_PROCESS_H_


#include "mmsPacketSend.h"
#include "kssocket.h"
#include "dbUtil.h"
#include "senderDbInfo.h"


class CMMSCtnTbl {
    public:
        int nCtnId;
        char szCtnName[50+1];
        char szCtnMime[50+1];
        int nCtnSeq;
        char szCtnSvc[5+1];
        char szFileName[50+1];
};


class CMMSFileProcess {
    public:
      int write(CKSSocket& db
         				,CMMSPacketSend& mmsPacketSend
        				,char* path
        				,long long mmsid
        				,char* szYYYYMM
        				,char* senderID
        				,int ctnid
        				,int timeout
        				,char* senderDbDomainName
        				,int bBcastFlag = false
        				);
		int writeTalk(CKSSocket& db
         				,CMMSPacketSend& mmsPacketSend
        				,char* path
        				,long long mmsid
        				,char* szYYYYMM
        				,char* senderID
        				,int ctnid
        				,int timeout
        				,char* senderDbDomainName
        				,int bBcastFlag = false
        				);
		int write(CKSSocket& db
        				,CMMSPacketSend& mmsPacketSend
        				,char* path
        				,int mmsid
        				,char* szYYYYMM
        				,char* senderID
        				,int ctnid
        				,int timeout
        				,char* senderDbDomainName
        				,int bBcastFlag = false
        				);
        int classifyMData(char* path,long long mmsid ,char* szYYYYMM,
            void* pData, int size,char* classify,
            char* szFileName,
            char* szSenderID,
            int ctnid,
            int ctnseq,
            CKSSocket& db,
            int timeout,
            char* senderDbDomainName,
            char* szCtnSvc,
            int bBcastFlag);
		int classifyMDataTalk(char* path,long long mmsid ,char* szYYYYMM,
            void* pData, int size,char* classify,
            char* szFileName,
            char* szSenderID,
            int ctnid,
            int ctnseq,
            CKSSocket& db,
            int timeout,
            char* senderDbDomainName,
            char* szCtnSvc,
            int bBcastFlag,
			CMMSPacketSend &mmsPacketSend);
		int classifyMData(char* path,int mmsid ,char* szYYYYMM,
            void* pData, int size,char* classify,
            char* szFileName,
            char* szSenderID,
            int ctnid,
            int ctnseq,
            CKSSocket& db,
            int timeout,
            char* senderDbDomainName,
            char* szCtnSvc,
            int bBcastFlag);
        int createFile(char* szDefaultPath, char* szCtnType,char* szYYYYMM, char* szFileName,
            void* pData,int size,char* szSenderID);
        char* getExtension(char* fileName);
        int getCTNID2DB(CKSSocket& db, CSenderDbMMSID& senderDbMMSID,int timeout, char* senderDbDomainName);
        int getMMSCtnTblFirst(CMMSCtnTbl& mmsCtnTbl);
        int getMMSCtnTblNext(CMMSCtnTbl& mmsCtnTbl);
        char* getTxtPath();
		int getTxtColorYN();
		void setTxtColorYN(int nYN);
    private:
        typedef list<CMMSCtnTbl>::iterator listMMSCtnTblPosition;
        list<CMMSCtnTbl> listMMSCtnTbl;
        char szTxtPath[256];
		int nColorYN;	// 1 : 컬러 text, 0 :일반 text
        void clearListMMSCtnTbl();
        listMMSCtnTblPosition m_MMSCtnTblPos;
        int parsingSKT(string& strParsingData, void* pData, int size);
        int parsingKTF(string& strParsingData, void* pData, int size);

        char* matchString2(char* szOrg, char* szTag, string& strVal);
        string strTxt;
        bool bKTFTXT;
        char szErrorMsg[128];
};
#endif 

