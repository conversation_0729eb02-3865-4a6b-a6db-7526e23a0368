#ifndef _LOGON_DB_PROC_H_
#define _LOGON_DB_PROC_H_

#include "stdafx.h"
#include "kssocket.h"
#include "smsPacketStruct.h"
#include "logonDbInfo.h"
#include "ksconfig.h"
#include "packetUtil.h"

// Pro*C version - no C++ STL
// using namespace std;

extern int activeProcess;
extern struct _message_info message_info;
extern struct _shm_info *shm_info;
extern char PROCESS_NO[7], PROCESS_NAME[36];

// CConfigLogonDB는 logonDB.h에서 정의됨
extern CConfigLogonDB gConf;

// Pro*C function declarations
int proc_connect();
void proc_disconnect();

int call_proc_check_mms_login_ext2(const char* cid, const char* pwd, 
                                   char* szAPPName, char* szSIP, int* nmPID, int* nmJOB,
                                   int* nUrlJob, int* nmPRT, int* nmCNT, int* nRptWait,
                                   char* szServerInfo, int* nRptNoDataSleep,
                                   char* szSenderName, char* szReportName,
                                   char* szSenderDBName, char* szReportDBName,
                                   char* szLogFilePath, char* szReserve,
                                   int* nmRST, char* szErrMsg);

int call_proc_get_limit_def(int pid, char* szLimitType, int* nDayWarnCnt, int* nDayLimitCnt,
                            int* nMonWarnCnt, int* nMonLimitCnt, char* szLimitFlag,
                            int* nDayAccCnt, int* nMonAccCnt, int* nmRST, char* szErrMsg);

int select_callback_data(int pid, char callback_list[][512], int* count);
int select_dialcode_data(const char* dial_code_type, char dialcode_list[][64], int* count);

int checkLogon(char* buff, int type);
int configParse(char* confFile);

int classifyProtocol(CKSSocket& newSockfd, int size, char* bindPacket);
int logonTypeMMS(CKSSocket& newSockfd, int type, char* bindPacket);
int logonType(CKSSocket& newSockfd, int type, char* bindPacket);
int getCallback(CKSSocket& newSockfd, int type, char* bindPacket);
int loadCallback(char* buff, int type, char callback_list[][512], int* count);
int getDialCode(CKSSocket& newSockfd, int type, char* bindPacket);
int loadDialCode(char* buff, int type, char dialcode_list[][64], int* count);

#endif
