#ifndef _CHECK_CALLBACK_H_
#define _CHECK_CALLBACK_H_

#include <iostream>
#include <string>
#include <map>
#include <set>

using namespace std;

#include "stdafx.h"
#include "processInfo.h"
#include "logonDbInfo.h"
#include "kssocket.h"
#include "logonUtil.h"
#include "adminInfo.h"
#include "adminUtil.h"

class CCheckCallback 
{
public:	
	int loadCallbackList(CProcessInfo& processInfo, CLogonDbInfo& logonDbInfo);
	int loadDialCodeList(CProcessInfo& processInfo, CLogonDbInfo& logonDbInfo, set<string>& set_dialcode_list, char *_dial_code_type);
	int loadDialCodeAll(CProcessInfo& processInfo, CLogonDbInfo& logonDbInfo);

	int findCallback(char *_callback);
	int examineCallback(string _callback);		

	void set_errorMsg(string _msg){errorMsg = _msg;}
	string get_errorMsg(){return errorMsg;}

	set<string> set_callback_list;
	set<string> set_dialcode_list_0101;  //이동통신및부가통신망
	set<string> set_dialcode_list_0102; //공통서비스
	set<string> set_dialcode_list_0103; //지역번호
	set<string> set_dialcode_list_0104; //기간통신사업자 공통부가 서비스

	string errorMsg;
};

#endif
