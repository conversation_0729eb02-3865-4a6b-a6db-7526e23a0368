#ifndef _LOGON_DB_PROC_C_H_
#define _LOGON_DB_PROC_C_H_

/* Pro*C compatible header file - C only, no C++ */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>
#include <signal.h>
#include <errno.h>

/* Basic type definitions */
typedef struct {
    char logonDBName[64];
    char dbID[16];
    char dbPASS[16];
    char dbSID[16];
} CConfigLogonDB;

/* LogonDbInfo structure - C version of CLogonDbInfo */
typedef struct {
    char szCID[64];
    char szPWD[64];
    char szAPPName[256];
    char szSIP[64];
    int nmPID;
    int nmJOB;
    int nUrlJob;
    int nmPRT;
    int nmCNT;
    int nRptWait;
    char szServerInfo[512];
    int nRptNoDataSleep;
    char szSenderName[256];
    char szReportName[256];
    char szSenderDBName[512];
    char szReportDBName[512];
    char szLogFilePath[512];
    char szReserve[512];
    int nmRST;
    char szErrMsg[512];
    char szLimitType[256];
    int nDayWarnCnt;
    int nDayLimitCnt;
    int nMonWarnCnt;
    int nMonLimitCnt;
    char szLimitFlag[256];
    int nDayAccCnt;
    int nMonAccCnt;
    char classify;
} LogonDbInfo_t;

/* Socket buffer size - use stdafx.h definition if available */
#ifndef SOCKET_BUFF
#define SOCKET_BUFF 4096
#endif

/* Header structure */
typedef struct {
    char msgType[4];
    char result[8];
    int leng;
} Header;

/* Message structures */
typedef struct {
    Header header;
    char data[SOCKET_BUFF];
} TypeMsgBindAck;

/* Message info structure */
struct _message_info {
    int msgid;
    int shmid;
    char process_name[36];
};

/* Shared memory info structure */
struct _shm_info {
    int shmid;
    void *shmaddr;
    int size;
};

/* Global variables */
extern int activeProcess;
extern struct _message_info message_info;
extern struct _shm_info *shm_info;
extern char PROCESS_NO[7];
extern char PROCESS_NAME[36];
extern CConfigLogonDB gConf;

/* Function declarations */
int proc_connect(void);
int proc_disconnect(void);
int checkLogon(void);
void log_history(int level, int error_code, const char *format, ...);

/* Constants */
#define MAX_BUFFER_SIZE 4096
#define MAX_SQL_SIZE 2048
#define TRUE 1
#define FALSE 0

/* Error codes */
#define SUCCESS 0
#define ERROR_DB_CONNECT -1
#define ERROR_DB_EXECUTE -2
#define ERROR_NO_DATA -3

/* Utility functions */
char* trim(char* szOrg, int leng);
void _logPrint(const char* logType, const char* message);

/* Socket functions - C versions */
int socket_create(void);
int socket_bind(int sockfd, int port);
int socket_listen(int sockfd);
int socket_accept(int sockfd);
int socket_send(int sockfd, const char* data, int len);
int socket_recv(int sockfd, char* data, int len);
void socket_close(int sockfd);

/* Packet utility functions - C versions */
int findValue(const char* packet, const char* key, char* value);

/* Log types */
extern char _DATALOG[64];

/* External functions */
extern int ml_sub_init(const char* process_no, const char* process_name, char* arg1, char* arg2);
extern void ml_sub_end(void);

#endif /* _LOGON_DB_PROC_C_H_ */
