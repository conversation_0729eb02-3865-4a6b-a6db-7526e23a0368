#ifndef _LOGON_DB_H_
#define _LOGON_DB_H_

#include <iostream>
#include <set>

#include "stdafx.h"
#include "kssocket.h"
#include "smsPacketStruct.h"
#include "logonDbInfo.h"
#include "ksconfig.h"
#include "packetUtil.h"
#include "DatabaseORA_MMS.h"

// use DatabaseORA_MMS
// #include "../../libsrc/odpi/inc/odpi_wrapper.hh"



using namespace std;


int activeProcess = TRUE;
struct _message_info	message_info;
struct _shm_info *shm_info;
char PROCESS_NO[ 7], PROCESS_NAME[36];



class CConfigLogonDB{
    public:
        char logonDBName[64];
        char dbID[16];
        char dbPASS[16];
        char dbSID[16];
        char dbuid[64];  // add same field as senderFtalkProDB.cpp
        char dbdsn[64];  // add same field as senderFtalkProDB.cpp
};

CConfigLogonDB gConf;
KSKYB::CDatabaseORA g_oracle;


// changed to use DatabaseORA_MMS
bool oracle_connect();
bool oracle_disconnect();
int checkLogon(char* buff,int type);
int checkLogonMMS(char* buff,int type);
int configParse(char* file);
int classifyProtocol(CKSSocket& newSockfd,int size,char* bindPacket);
int logonType(CKSSocket& newSockfd,int type,char* bindPacket);
int logonTypeMMS(CKSSocket& newSockfd,int type,char* bindPacket);

/* additional function for anti-tampering feature development */
int getCallback(CKSSocket& newSockfd,int type,char* bindPacket);
int loadCallback(char* buff, int type, set<string> &set_callback_list);
int getDialCode(CKSSocket& newSockfd, int type, char* bindPacket);
int loadDialCode(char* buff, int type, set<string> &set_dialcode_list);

#endif


