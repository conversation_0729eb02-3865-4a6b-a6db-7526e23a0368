#ifndef _LOGON_UTIL_H_
#define _LOGON_UTIL_H_

#include "stdafx.h"
#include "logonDbInfo.h"
#include "kssocket.h"
#include "smsPacketStruct.h"


class CLogonUtil {
    public:
        void displayLogonDbInfo(const CLogonDbInfo logonDbInfo,char* filename);
        int recvPacket(CKSSocket& hRemoteSock,char* buff,int sec,int usec);
        char* getErrorMsg();
        char* findValueParse(char* szOrg, char* szTag, char* szVal);
    private:
        char m_ErrorMsg[256];
};



#endif 




