#ifndef _SENDER_DB_INFO_H_
#define _SENDER_DB_INFO_H_

#include "smsPacketStruct.h"
#include "logonDbInfo.h"
#include "senderInfo.h"

class CSenderDbInfo {
    public:
        TypeMsgDataSnd smsData;
        CLogonDbInfo logonDbInfo;
        char szReserve[128];
        CSenderInfo senderInfo;
        int result;
};

/*
** unsigned long long mmsid
class CSenderDbInfoAck {
    public:
        int msgid;
        char szResult[4];
};
*/
class CSenderDbInfoAck {
    public:
        unsigned long long msgid;
        char szResult[4];
};

#endif

