#ifndef _REPORT_DB_H_
#define _REPORT_DB_H_

#include <iostream>
#include "stdafx.h"
#include "kssocket.h"
#include "ksthread.h"
#include "smsPacketStruct.h"
#include "logonDbInfo.h"
#include "reportDbInfo.h"
#include "ksconfig.h"


#include        <sqlca.h>

using namespace std;

int activeProcess = TRUE;
struct _message_info	message_info;
struct _shm_info *shm_info;
char PROCESS_NO[ 7], PROCESS_NAME[36];


class CThreadInfo {
    public:
        pthread_t tid;
        int sock; /* domain socket */
};

class CConfigReportDB {
    public:
        char reportDBName[64];
        char dbID[16];
        char dbPASS[16];
        char dbSID[16];

};

CConfigReportDB gConf;



int configParse(char* file);


#endif

