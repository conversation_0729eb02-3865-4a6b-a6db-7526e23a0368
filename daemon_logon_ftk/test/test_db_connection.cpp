#include <iostream>
#include <cstring>
#include <cstdlib>
#include <oci.h>

using namespace std;

int main(int argc, char* argv[]) {
    cout << "=== Oracle Database Connection Test (OCI) ===" << endl;

    // 기본값 설정
    char szUID[64] = "neoftk";
    char szP<PERSON>[64] = "neoftk";
    char szDS<PERSON>[64] = "NEO226";

    // 명령행 인수로 변경 가능
    if (argc >= 2) {
        strncpy(szUID, argv[1], sizeof(szUID) - 1);
        szUID[sizeof(szUID) - 1] = '\0';
    }
    if (argc >= 3) {
        strncpy(szPWD, argv[2], sizeof(szPWD) - 1);
        szPWD[sizeof(szPWD) - 1] = '\0';
    }
    if (argc >= 4) {
        strncpy(szDSN, argv[3], sizeof(szDSN) - 1);
        szDSN[sizeof(szDSN) - 1] = '\0';
    }

    cout << "Testing connection with:" << endl;
    cout << "  Username: " << szUID << endl;
    cout << "  Password: " << szPWD << endl;
    cout << "  DSN: " << szDSN << endl;
    cout << endl;

    // Oracle 환경 변수 출력
    cout << "Oracle Environment Variables:" << endl;
    cout << "  ORACLE_HOME: " << (getenv("ORACLE_HOME") ? getenv("ORACLE_HOME") : "NOT SET") << endl;
    cout << "  TNS_ADMIN: " << (getenv("TNS_ADMIN") ? getenv("TNS_ADMIN") : "NOT SET") << endl;
    cout << "  NLS_LANG: " << (getenv("NLS_LANG") ? getenv("NLS_LANG") : "NOT SET") << endl;
    cout << "  LD_LIBRARY_PATH: " << (getenv("LD_LIBRARY_PATH") ? getenv("LD_LIBRARY_PATH") : "NOT SET") << endl;
    cout << endl;

    // OCI 변수들
    OCIEnv *envhp = NULL;
    OCIError *errhp = NULL;
    OCISvcCtx *svchp = NULL;
    OCIServer *srvhp = NULL;
    OCISession *usrhp = NULL;
    sword status;

    cout << "Initializing OCI..." << endl;

    // OCI 환경 초기화
    status = OCIEnvCreate(&envhp, OCI_DEFAULT, NULL, NULL, NULL, NULL, 0, NULL);
    if (status != OCI_SUCCESS) {
        cout << "FAILED: OCIEnvCreate failed with status: " << status << endl;
        return 1;
    }

    // Error handle 할당
    status = OCIHandleAlloc(envhp, (void**)&errhp, OCI_HTYPE_ERROR, 0, NULL);
    if (status != OCI_SUCCESS) {
        cout << "FAILED: OCIHandleAlloc for error handle failed" << endl;
        return 1;
    }

    // Server handle 할당
    status = OCIHandleAlloc(envhp, (void**)&srvhp, OCI_HTYPE_SERVER, 0, NULL);
    if (status != OCI_SUCCESS) {
        cout << "FAILED: OCIHandleAlloc for server handle failed" << endl;
        return 1;
    }

    // Service context handle 할당
    status = OCIHandleAlloc(envhp, (void**)&svchp, OCI_HTYPE_SVCCTX, 0, NULL);
    if (status != OCI_SUCCESS) {
        cout << "FAILED: OCIHandleAlloc for service context handle failed" << endl;
        return 1;
    }

    // Session handle 할당
    status = OCIHandleAlloc(envhp, (void**)&usrhp, OCI_HTYPE_SESSION, 0, NULL);
    if (status != OCI_SUCCESS) {
        cout << "FAILED: OCIHandleAlloc for session handle failed" << endl;
        return 1;
    }

    cout << "Connecting to Oracle server..." << endl;

    // 서버에 연결
    status = OCIServerAttach(srvhp, errhp, (text*)szDSN, strlen(szDSN), 0);
    if (status != OCI_SUCCESS) {
        text errbuf[512];
        sb4 errcode;
        OCIErrorGet(errhp, 1, NULL, &errcode, errbuf, sizeof(errbuf), OCI_HTYPE_ERROR);
        cout << "FAILED: OCIServerAttach failed" << endl;
        cout << "Error: " << errbuf << endl;
        return 1;
    }

    // Service context에 server handle 설정
    status = OCIAttrSet(svchp, OCI_HTYPE_SVCCTX, srvhp, 0, OCI_ATTR_SERVER, errhp);
    if (status != OCI_SUCCESS) {
        cout << "FAILED: OCIAttrSet for server failed" << endl;
        return 1;
    }

    // 사용자명과 비밀번호 설정
    status = OCIAttrSet(usrhp, OCI_HTYPE_SESSION, szUID, strlen(szUID), OCI_ATTR_USERNAME, errhp);
    if (status != OCI_SUCCESS) {
        cout << "FAILED: OCIAttrSet for username failed" << endl;
        return 1;
    }

    status = OCIAttrSet(usrhp, OCI_HTYPE_SESSION, szPWD, strlen(szPWD), OCI_ATTR_PASSWORD, errhp);
    if (status != OCI_SUCCESS) {
        cout << "FAILED: OCIAttrSet for password failed" << endl;
        return 1;
    }

    cout << "Starting user session..." << endl;

    // 세션 시작
    status = OCISessionBegin(svchp, errhp, usrhp, OCI_CRED_RDBMS, OCI_DEFAULT);
    if (status != OCI_SUCCESS) {
        text errbuf[512];
        sb4 errcode;
        OCIErrorGet(errhp, 1, NULL, &errcode, errbuf, sizeof(errbuf), OCI_HTYPE_ERROR);
        cout << "FAILED: OCISessionBegin failed" << endl;
        cout << "Error: " << errbuf << endl;
        return 1;
    }

    // Service context에 session handle 설정
    status = OCIAttrSet(svchp, OCI_HTYPE_SVCCTX, usrhp, 0, OCI_ATTR_SESSION, errhp);
    if (status != OCI_SUCCESS) {
        cout << "FAILED: OCIAttrSet for session failed" << endl;
        return 1;
    }

    cout << "SUCCESS: Connected to Oracle database!" << endl;

    // 연결 해제
    cout << "Disconnecting..." << endl;
    OCISessionEnd(svchp, errhp, usrhp, OCI_DEFAULT);
    OCIServerDetach(srvhp, errhp, OCI_DEFAULT);

    // Handle 해제
    OCIHandleFree(usrhp, OCI_HTYPE_SESSION);
    OCIHandleFree(svchp, OCI_HTYPE_SVCCTX);
    OCIHandleFree(srvhp, OCI_HTYPE_SERVER);
    OCIHandleFree(errhp, OCI_HTYPE_ERROR);
    OCIHandleFree(envhp, OCI_HTYPE_ENV);

    cout << "Disconnected successfully." << endl;

    return 0;
}
