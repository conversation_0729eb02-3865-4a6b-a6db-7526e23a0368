# Oracle Database Connection Test Makefile (OCI)

# Compiler and flags
CXX = g++
CXXFLAGS = -g -Wall -std=gnu++11

# Oracle paths
ORACLE_HOME = /usr/lib/oracle/21/client64
ORA_INC = /usr/include/oracle/21/client64
ORA_LIB = $(ORACLE_HOME)/lib

# Include paths
INCLUDES = -I$(ORA_INC)

# Library paths and libraries
LIBPATHS = -L$(ORA_LIB)
LIBS = -lclntsh

# Targets
TARGET = test_db_connection
CLIENT_TARGET = test_logon_client

# Default target
all: $(TARGET) $(CLIENT_TARGET)

# Compile and link in one step
$(TARGET): test_db_connection.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $< $(LIBPATHS) $(LIBS)

# Compile client test
$(CLIENT_TARGET): test_logon_client.cpp
	$(CXX) $(CXXFLAGS) -o $@ $<

# Clean
clean:
	rm -f $(TARGET) $(CLIENT_TARGET)

# Test run
test: $(TARGET)
	@echo "=== Running Oracle Connection Test ==="
	@export ORACLE_HOME=$(ORACLE_HOME) && \
	export TNS_ADMIN=$(ORACLE_HOME)/network/admin && \
	export NLS_LANG=KOREAN_KOREA.AL32UTF8 && \
	export LD_LIBRARY_PATH=$(ORA_LIB):$$LD_LIBRARY_PATH && \
	./$(TARGET)

# Test with custom parameters (username password dsn)
test-custom: $(TARGET)
	@echo "=== Running Oracle Connection Test with Custom Parameters ==="
	@echo "Usage: make test-custom UID=username PWD=password DSN=dsn"
	@export ORACLE_HOME=$(ORACLE_HOME) && \
	export TNS_ADMIN=$(ORACLE_HOME)/network/admin && \
	export NLS_LANG=KOREAN_KOREA.AL32UTF8 && \
	export LD_LIBRARY_PATH=$(ORA_LIB):$$LD_LIBRARY_PATH && \
	./$(TARGET) $(UID) $(PWD) $(DSN)

# Test client
test-client: $(CLIENT_TARGET)
	@echo "=== Running LogonDB Client Test ==="
	./$(CLIENT_TARGET)

.PHONY: all clean test test-custom test-client
