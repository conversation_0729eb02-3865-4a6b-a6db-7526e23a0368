#include <iostream>
#include <cstring>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>

using namespace std;

int main() {
    cout << "=== LogonDB Client Test ===" << endl;
    
    // 소켓 생성
    int sock = socket(AF_INET, SOCK_STREAM, 0);
    if (sock < 0) {
        cout << "Socket creation failed" << endl;
        return 1;
    }
    
    // 서버 주소 설정 (logonDB는 보통 포트 36000에서 실행)
    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(36000);
    server_addr.sin_addr.s_addr = inet_addr("127.0.0.1");
    
    cout << "Connecting to logonDB at 127.0.0.1:36000..." << endl;
    
    // 서버에 연결
    if (connect(sock, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        cout << "Connection failed" << endl;
        close(sock);
        return 1;
    }
    
    cout << "Connected successfully!" << endl;
    
    // 로그온 요청 패킷 생성 (간단한 형태)
    char request[1024];
    sprintf(request, "ID=testuser&PASSWORD=testpass&REPORT=Y");
    
    cout << "Sending logon request: " << request << endl;
    
    // 요청 전송
    int sent = send(sock, request, strlen(request), 0);
    if (sent < 0) {
        cout << "Send failed" << endl;
        close(sock);
        return 1;
    }
    
    cout << "Request sent (" << sent << " bytes)" << endl;
    
    // 응답 수신
    char response[1024];
    memset(response, 0, sizeof(response));
    int received = recv(sock, response, sizeof(response) - 1, 0);
    
    if (received > 0) {
        cout << "Response received (" << received << " bytes): " << response << endl;
    } else {
        cout << "No response received or connection closed" << endl;
    }
    
    // 연결 종료
    close(sock);
    cout << "Connection closed" << endl;
    
    return 0;
}
