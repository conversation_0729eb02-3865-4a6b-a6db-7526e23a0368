# Test Makefile for charset detection
# Usage: make -f test_charset_makefile

# Oracle environment variables
ORACLE_HOME=/usr/lib/oracle/21/client64
TNS_ADMIN=$(ORACLE_HOME)/network/admin
NLS_LANG=KOREAN_KOREA.AL32UTF8

# Compiler settings
CXX=g++
CXXFLAGS=-I$(ORACLE_HOME)/include -I./inc -DLINUX
LDFLAGS=-L$(ORACLE_HOME)/lib -lclntsh

# Source files
TEST_SRC=test_charset_detection.cpp
DB_SRC=lib/DatabaseORA_MMS.cpp

# Object files
TEST_OBJ=$(TEST_SRC:.cpp=.o)
DB_OBJ=$(DB_SRC:.cpp=.o)

# Target
TARGET=test_charset

# Build rules
$(TARGET): $(TEST_OBJ) $(DB_OBJ)
	$(CXX) -o $@ $^ $(LDFLAGS)

%.o: %.cpp
	$(CXX) $(CXXFLAGS) -c $< -o $@

# Clean
clean:
	rm -f $(TEST_OBJ) $(DB_OBJ) $(TARGET)

# Export Oracle environment variables
export ORACLE_HOME TNS_ADMIN NLS_LANG

.PHONY: clean
