/*
 * DatabaseORA_MMS.cpp
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#include "DatabaseORA_MMS.h"
#include <sqlca.h>
#include <iostream>

using namespace std;

// Global variable definition (accessible from other files)
char tmpLog3[1024];

// Global variables for database charset information
char g_db_charset[50] = {0};
bool g_db_is_euckr = false;
bool g_charset_checked = false;

namespace KSKYB
{
int CDatabaseORA::connectToOracle(char* szUID, char* szDSN)
{
	EXEC SQL BEGIN DECLARE SECTION;
	char szConnInf[64], szConnDsn[64];
	EXEC SQL END DECLARE SECTION;
	
	if ((szUID == NULL) || (szDSN == NULL)) {
		sprintf(tmpLog3, "CDatabaseORA::connectToOracle() ERROR[szUID or szDSN is NULL]");
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
	// Add debugging log
	sprintf(tmpLog3, "CDatabaseORA::connectToOracle() Attempting connection - UID[%s] DSN[%s]", szUID, szDSN);
	_logPrint(_DATALOG, tmpLog3);

	memset(szConnInf, 0x00, sizeof(szConnInf));
	memset(szConnDsn, 0x00, sizeof(szConnDsn));
	strcpy(szConnInf, szUID);
	strcpy(szConnDsn, szDSN);

	// Connection string verification log
	sprintf(tmpLog3, "CDatabaseORA::connectToOracle() Connection strings - szConnInf[%s] szConnDsn[%s]", szConnInf, szConnDsn);
	_logPrint(_DATALOG, tmpLog3);

	// Attempt Pro*C CONNECT
	sprintf(tmpLog3, "CDatabaseORA::connectToOracle() Executing EXEC SQL CONNECT");
	_logPrint(_DATALOG, tmpLog3);

	EXEC SQL CONNECT :szConnInf USING :szConnDsn;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::connectToOracle() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
	sprintf(tmpLog3, "CDatabaseORA::connectToOracle() Success");
	_logPrint(_DATALOG, tmpLog3);

	// Check database charset after successful connection
	if (!g_charset_checked) {
		char charset_buffer[50];
		if (checkDatabaseCharset(charset_buffer, sizeof(charset_buffer)) > 0) {
			sprintf(tmpLog3, "CDatabaseORA::connectToOracle() Database charset detected: %s", charset_buffer);
			_logPrint(_DATALOG, tmpLog3);
		} else {
			sprintf(tmpLog3, "CDatabaseORA::connectToOracle() Warning: Failed to detect database charset, assuming non-EUC-KR");
			_logPrint(_DATALOG, tmpLog3);
		}
	}

	return 1;
}

int CDatabaseORA::closeFromOracle()
{
	EXEC SQL COMMIT WORK RELEASE;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::closeFromOracle() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
	sprintf(tmpLog3, "CDatabaseORA::closeFromOracle() Success");
	_logPrint(_DATALOG, tmpLog3);
	
	return 1;
}

int CDatabaseORA::commitOracle()
{
	EXEC SQL COMMIT WORK;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::commitOracle() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
	
	sprintf(tmpLog3, "CDatabaseORA::commitOracle() Success");
	_logPrint(_DATALOG, tmpLog3);
	
	
	return 1;
}

int CDatabaseORA::rollbackOracle()
{
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL ROLLBACK WORK;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::rollbackOracle() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
	
	sprintf(tmpLog3, "CDatabaseORA::rollbackOracle() Success");
	_logPrint(_DATALOG, tmpLog3);

	return 1;
}

int CDatabaseORA::checkDatabaseCharset(char* charset, int charset_size)
{
	EXEC SQL BEGIN DECLARE SECTION;
	char db_charset[50];
	EXEC SQL END DECLARE SECTION;

	if (charset == NULL || charset_size <= 0) {
		sprintf(tmpLog3, "CDatabaseORA::checkDatabaseCharset() ERROR[Invalid parameters]");
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}

	memset(db_charset, 0x00, sizeof(db_charset));

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL SELECT VALUE INTO :db_charset
	         FROM V$NLS_PARAMETERS
	         WHERE PARAMETER = 'NLS_CHARACTERSET';

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::checkDatabaseCharset() ERROR[%d][%s]",
		        sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}

	// Trim whitespace and copy result
	char* trimmed_charset = trim(db_charset, sizeof(db_charset));
	strncpy(charset, trimmed_charset, charset_size - 1);
	charset[charset_size - 1] = '\0';

	// Update global variables
	strncpy(g_db_charset, trimmed_charset, sizeof(g_db_charset) - 1);
	g_db_charset[sizeof(g_db_charset) - 1] = '\0';

	// Check if database uses EUC-KR compatible charset
	g_db_is_euckr = (strcmp(trimmed_charset, "KO16MSWIN949") == 0 ||
	                 strcmp(trimmed_charset, "KO16KSC5601") == 0);

	g_charset_checked = true;

	sprintf(tmpLog3, "CDatabaseORA::checkDatabaseCharset() Success - Charset[%s] EUC-KR_Compatible[%s]",
	        trimmed_charset, g_db_is_euckr ? "YES" : "NO");
	_logPrint(_DATALOG, tmpLog3);

	return 1;
}

int CDatabaseORA::getCTNID()
{
	EXEC SQL BEGIN DECLARE SECTION;
	char szSqlErrorMsg[1024];
	char szCid[10+1];
	int nCTNID = 0;
	int nSqlCode = -999;
	EXEC SQL END DECLARE SECTION;

	memset(szSqlErrorMsg,0x00,sizeof(szSqlErrorMsg));		//CCL(szSqlErrorMsg);
	memset(szCid        ,0x00,sizeof(szCid        ));		//CCL(szCid);
	
	memset(szCid, 0x00, 10);
	
	EXEC SQL EXECUTE
		BEGIN
			proc_get_ctn_id( in_cid     =>:szCid
							,ot_cnt_id  =>:nCTNID
							,ot_sqlcode =>:nSqlCode
							,ot_sqlmsg  =>:szSqlErrorMsg
   							);
		END;
	END-EXEC;
	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			sprintf(tmpLog3, "[ERR] CDatabaseORA::getCTNID failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc)));
			_logPrint(_DATALOG, tmpLog3);
			return -1;
		}
		sprintf(tmpLog3, "[ERR] dCDatabaseORA::getCTNID invaled failed - otReuslt[%d]errMsg[%s]",nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
		
	return nCTNID;
}

int CDatabaseORA::setMMSCTNTBL(CSenderDbMMSCTNTBL &ctn_data)
{	
	EXEC SQL BEGIN DECLARE SECTION;
	int nSqlCode = -999;
	int nCtnId   = 0   ;
	int nCtnSeq  = 0   ;
	char szSqlErrorMsg[1024];
	char szCtnName    [50+1];
	char szCtnMime    [50+1];
	char szCtnSvc     [ 5+1];
	EXEC SQL END DECLARE SECTION;
	
	memset(szSqlErrorMsg, 0x00, sizeof(szSqlErrorMsg));
	memset(szCtnName    , 0x00, sizeof(szCtnName    ));
	memset(szCtnMime    , 0x00, sizeof(szCtnMime    ));
	memset(szCtnSvc     , 0x00, sizeof(szCtnSvc     ));

	memcpy(szCtnName, ctn_data.szCtnName, sizeof(ctn_data.szCtnName));
	memcpy(szCtnMime, ctn_data.szCtnMime, sizeof(ctn_data.szCtnMime));
	memcpy(szCtnSvc , ctn_data.szCtnSvc , sizeof(ctn_data.szCtnSvc ));
	
	nCtnId  = ctn_data.nCtnId ;
	nCtnSeq = ctn_data.nCtnSeq;

	EXEC SQL EXECUTE
		BEGIN
			proc_set_mms_ctn(in_cid      => :nCtnId
							,in_ctn_seq  => :nCtnSeq
							,in_ctn_name => :szCtnName
							,in_ctn_mime => :szCtnMime
							,in_ctn_svc  => :szCtnSvc
							,ot_sqlcode  => :nSqlCode
							,ot_sqlmsg   => :szSqlErrorMsg
							);
		END;
	END-EXEC;
	/* here~ */
	//sprintf(tmpLog3, "[%s]: CDatabaseORA::setMMSCTNTBL-[%d][%d] [%s][%s][%s] [%d][%s]",__func__,nCtnId,nCtnSeq,szCtnName,szCtnMime,szCtnSvc,nSqlCode,"");
	//_logPrint(_DATALOG, tmpLog3);
	
	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSCTNTBL exec failed - nCtnId[%d] sqlcode[%d] sqlmsg[%s]",nCtnId,sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc)));
			_logPrint(_DATALOG, tmpLog3);
			return -1;
		}
		sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSCTNTBL invaled failed - nCtnId[%d]otReuslt[%d]errMsg[%s]",nCtnId,nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
	
	return 1;
}

int CDatabaseORA::setMMSTBL(CSenderDbMMSTBL &mms_data)
{
	EXEC SQL BEGIN DECLARE SECTION;
	char szSqlErrorMsg[1024];
	int nSqlCode = -999;

	char szDstAddr[16+1];
	char szCallBack[16+1];
	char szMsgTitle[200+1];  // Fixed: Changed from 64+1 to 200+1 to match CSenderDbMMSTBL structure
	char szPtnSn[16+1];
	char szResvData[200+1];
	char szCid[10+1];
	int nMsgType = 0;
	int nPriority = 0;
	int nCtnId = 0;
	int nCtnType = 0;
	int nRgnRate = 0;
	int nInterval = 0;
	int nTextCnt = 0;
	int nImgCnt = 0;
	int nAugCnt = 0;
	int nMpCnt = 0;
	char cMMSId[20];
	char szSenderKey[40+1];
	char szChatBubbleType[30+1];
	char szTargeting[1+1];
	char szTmplCd    [20+1];
	char szRepFlag	 [1+1];
	char szRepTitle [64+1];
	char szRepText	 [500+1];
	char szAppUserId [20+1];
	char szPushAlarm [1+1];
	char szMessageVariable	[2000+1];
	char szButtonVariable 	[2000+1];
	char szCouponVariable	[1000+1];
	char szImageVariable	[1000+1];
	char szVideoVariable	[1000+1];
	char szCommerceVariable	[1000+1];
	char szCarouselVariable	[2000+1];	
	EXEC SQL END DECLARE SECTION;

	memset(szSqlErrorMsg , 0x00, sizeof(szSqlErrorMsg));
	memset(szCid         , 0x00, sizeof(szCid        ));
	memset(szDstAddr     , 0x00, sizeof(szDstAddr    ));
	memset(szCallBack    , 0x00, sizeof(szCallBack   ));
	memset(szMsgTitle    , 0x00, sizeof(szMsgTitle   ));
	memset(szPtnSn       , 0x00, sizeof(szPtnSn      ));
	memset(szResvData    , 0x00, sizeof(szResvData   ));
	memset(cMMSId        , 0x00, sizeof(cMMSId       ));
	memset(szSenderKey   , 0x00, sizeof(szSenderKey       ));
	memset(szChatBubbleType   , 0x00, sizeof(szChatBubbleType       ));
	memset(szTargeting   , 0x00, sizeof(szTargeting       ));
	memset(szTmplCd    , 0x00, sizeof(szTmplCd       ));
	memset(szRepFlag   , 0x00, sizeof(szRepFlag       ));
	memset(szRepTitle  , 0x00, sizeof(szRepTitle       ));
	memset(szRepText   , 0x00, sizeof(szRepText       ));
	memset(szAppUserId , 0x00, sizeof(szAppUserId       ));
	memset(szPushAlarm , 0x00, sizeof(szPushAlarm       ));
	memset(szMessageVariable, 0x00, sizeof(szMessageVariable));
	memset(szButtonVariable, 0x00, sizeof(szButtonVariable));
	memset(szCouponVariable, 0x00, sizeof(szCouponVariable));
	memset(szImageVariable, 0x00, sizeof(szImageVariable));
	memset(szVideoVariable, 0x00, sizeof(szVideoVariable));
	memset(szCommerceVariable, 0x00, sizeof(szCommerceVariable));
	memset(szCarouselVariable, 0x00, sizeof(szCarouselVariable));
	
	strcpy(szDstAddr  , mms_data.szDstAddr);
	//strcpy(szCallBack , mms_data.szCallBack);
	//sprintf(szMsgTitle, "%.*s", (int)(sizeof(szMsgTitle)-1), mms_data.szMsgTitle);
	strcpy(szPtnSn    , mms_data.szPtnSn);
	strcpy(szResvData , mms_data.szResvData);
	snprintf(szCid, sizeof(szCid), "%s", mms_data.szCid);
	
	sprintf(cMMSId,"%lld", mms_data.nMMSId);
	
	nMsgType 	= mms_data.nMsgType;
	nPriority 	= mms_data.nPriority;
	nCtnId 		= mms_data.nCtnId;
	nCtnType 	= mms_data.nCtnType;
	nRgnRate 	= mms_data.nRgnRate;
	nInterval 	= mms_data.nInterval;
	nTextCnt 	= mms_data.nTextCnt;
	nImgCnt 	= mms_data.nImgCnt;
	nAugCnt 	= mms_data.nAugCnt;
	nMpCnt 		= mms_data.nMpCnt;
	
	//20190708 sender key added
	//sprintf(szSenderKey, mms_data.szSenderKey);
	snprintf(szSenderKey, sizeof(szSenderKey), mms_data.szSenderKey);
	snprintf(szChatBubbleType, sizeof(szChatBubbleType), mms_data.szChatBubbleType);
	snprintf(szTargeting, sizeof(szTargeting), mms_data.szTargeting);
	snprintf(szTmplCd, sizeof(szTmplCd), mms_data.szTmplCd);
	snprintf(szRepFlag, sizeof(szRepFlag), mms_data.szRepFlag);
	snprintf(szRepTitle, sizeof(szRepTitle), mms_data.szRepTitle);
	snprintf(szRepText, sizeof(szRepText), mms_data.szRepText);
	snprintf(szAppUserId, sizeof(szAppUserId), mms_data.szAppUserId);
	snprintf(szPushAlarm, sizeof(szPushAlarm), mms_data.szPushAlarm);
	snprintf(szMessageVariable, sizeof(szMessageVariable), mms_data.szMessageVariable);
	snprintf(szButtonVariable, sizeof(szButtonVariable), mms_data.szButtonVariable);
	snprintf(szCouponVariable, sizeof(szCouponVariable), mms_data.szCouponVariable);
	snprintf(szImageVariable, sizeof(szImageVariable), mms_data.szImageVariable);
	snprintf(szVideoVariable, sizeof(szVideoVariable), mms_data.szVideoVariable);
	snprintf(szCommerceVariable, sizeof(szCommerceVariable), mms_data.szCommerceVariable);
	snprintf(szCarouselVariable, sizeof(szCarouselVariable), mms_data.szCarouselVariable);
	
	//sprintf(tmpLog3, "[DBG] setMMSTBL1 MMSID[%lld][%s]", mms_data.nMMSId, cMMSId);
	//_logPrint(_DATALOG, tmpLog3);

	// Debug log: Print all input parameters before calling proc_set_mms_tbl
	sprintf(tmpLog3, "[DBG] proc_set_mms_tbl INPUT - szPtnSn[%s] szResvData[%s] szCid[%s]",
		szPtnSn, szResvData, szCid);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "[DBG] proc_set_mms_tbl INPUT - cMMSId[%s] szSenderKey[%s] szChatBubbleType[%s]",
		cMMSId, szSenderKey, szChatBubbleType);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "[DBG] proc_set_mms_tbl INPUT - szTargeting[%s] szTmplCd[%s] szRepFlag[%s]",
		szTargeting, szTmplCd, szRepFlag);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "[DBG] proc_set_mms_tbl INPUT - szRepTitle[%s] szRepText[%s] szAppUserId[%s]",
		szRepTitle, szRepText, szAppUserId);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "[DBG] proc_set_mms_tbl INPUT - szPushAlarm[%s] nMsgType[%d] nPriority[%d]",
		szPushAlarm, nMsgType, nPriority);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "[DBG] proc_set_mms_tbl INPUT - szMessageVariable[%.100s...]", szMessageVariable);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "[DBG] proc_set_mms_tbl INPUT - szButtonVariable[%.100s...]", szButtonVariable);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "[DBG] proc_set_mms_tbl INPUT - szCouponVariable[%.100s...]", szCouponVariable);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "[DBG] proc_set_mms_tbl INPUT - szImageVariable[%.100s...]", szImageVariable);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "[DBG] proc_set_mms_tbl INPUT - szVideoVariable[%.100s...]", szVideoVariable);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "[DBG] proc_set_mms_tbl INPUT - szCommerceVariable[%.100s...]", szCommerceVariable);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "[DBG] proc_set_mms_tbl INPUT - szCarouselVariable[%.100s...]", szCarouselVariable);
	_logPrint(_DATALOG, tmpLog3);

	EXEC SQL EXECUTE
		BEGIN
			proc_set_mms_tbl(in_dstaddr =>:szDstAddr
							,in_callback =>:szCallBack
							,in_msgtitle =>:szMsgTitle
							,in_ptn_sn =>:szPtnSn
							,in_resv_data =>:szResvData
							,in_cid =>:szCid
							,in_msg_type =>:nMsgType
							,in_priority =>:nPriority
							,in_ctn_id =>:nCtnId
							,in_ctn_type =>:nCtnType
							,in_rgn_rate =>:nRgnRate
							,in_interval => :nInterval
							,in_text_cnt => :nTextCnt
							,in_img_cnt => :nImgCnt
							,in_aud_cnt => :nAugCnt
							,in_mp_cnt => :nMpCnt
							,in_mms_id => :cMMSId
							,in_sender_key => :szSenderKey
							,in_chat_bubble_type => :szChatBubbleType
							,in_targeting => :szTargeting
							,in_tmpl_cd => :szTmplCd
							,in_rep_flag => :szRepFlag
							,in_rep_title => :szRepTitle
							,in_rep_text => :szRepText
							,in_app_user_id => :szAppUserId
							,in_push_alarm => :szPushAlarm
							,in_message_variable => :szMessageVariable
							,in_button_variable => :szButtonVariable
							,in_coupon_variable => :szCouponVariable
							,in_image_variable => :szImageVariable
							,in_video_variable => :szVideoVariable
							,in_commerce_variable => :szCommerceVariable
							,in_carousel_variable => :szCarouselVariable
							,ot_sqlcode =>:nSqlCode
							,ot_sqlmsg =>:szSqlErrorMsg
               				);
		END;
	END-EXEC;

	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSTBL failed - MMSID[%s] CID[%s] sqlcode[%d] sqlmsg[%s]",
				cMMSId,szCid,sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc)));
			_logPrint(_DATALOG, tmpLog3);
			return -1;
		}
		sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSTBL invaled failed - MMSID[%s]CID[%s]otReuslt[%d]errMsg[%s]",
			cMMSId,szCid,nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
    
	return 1;
}

int CDatabaseORA::setMMSMSG(CSenderDbMMSMSG &que_data)
{
	EXEC SQL BEGIN DECLARE SECTION;
	char szSqlErrorMsg[1024];
	int nSqlCode = -999;

	char szQName[32+1];
	int nQNum;
	int nPriority;
	int nCtnId;
	char szCallBack[ 16+1];
	char szDstAddr [ 16+1];
	char szMsgTitle[ 64+1];
	int nCntType;
	char szTxtPath [256+1];
	int nRgnRate;
	int nInterval;
	
	char cMMSId[20];
	EXEC SQL END DECLARE SECTION;

	memset(szSqlErrorMsg, 0x00, sizeof(szSqlErrorMsg));
	memset(szQName		, 0x00, sizeof(szQName	    ));
	memset(szCallBack	, 0x00, sizeof(szCallBack	));
	memset(szDstAddr	, 0x00, sizeof(szDstAddr	));
	memset(szMsgTitle	, 0x00, sizeof(szMsgTitle	));
	memset(szTxtPath	, 0x00, sizeof(szTxtPath	));
	memset(cMMSId       , 0x00, sizeof(cMMSId		));

	nQNum     = 1;
	nQNum     = atoi(que_data.szQName);
	nPriority = que_data.nPriority;
	nCtnId    = que_data.nCtnId;
	strcpy(szCallBack, que_data.szCallBack);
	strcpy(szDstAddr , que_data.szDstAddr );
	sprintf(szMsgTitle, "%.*s", sizeof(szMsgTitle)-1, que_data.szMsgTitle);
	nCntType  = que_data.nCntType;
	strcpy(szTxtPath , que_data.szTxtPath );
	nRgnRate  = que_data.nRgnRate;
	nInterval = que_data.nInterval;
	
	sprintf(cMMSId   , "%lld", que_data.nMMSId);
	//sprintf(tmpLog3, "[DBG] setMMSMSG MMSID[%s]",cMMSId);
	//_logPrint(_DATALOG, tmpLog3);

	EXEC SQL EXECUTE
		BEGIN
			proc_set_mms_msg2(in_q_num    =>:nQNum
							 ,in_priority =>:nPriority
							 ,in_ctn_id   =>:nCtnId
							 ,in_callback =>:szCallBack
							 ,in_dst_addr =>:szDstAddr
							 ,in_msgtitle =>:szMsgTitle
							 ,in_cnt_type =>:nCntType
							 ,in_txt_path =>:szTxtPath
							 ,in_rgn_rate =>:nRgnRate
							 ,in_interval =>:nInterval
							 ,in_mms_id   =>:cMMSId
							 ,ot_sqlcode  =>:nSqlCode
							 ,ot_sqlmsg   =>:szSqlErrorMsg
							 );

		END;
	END-EXEC;
	//sprintf(tmpLog3, "[INF] CDatabaseORA::setMMSCTNTBL - CallBack[%s]DstAddr[]TxtPath[%s]",szCallBack, szTxtPath);
	//_logPrint(_DATALOG, tmpLog3);
	
	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSCTNTBL exec failed - MMSID[%s] sqlcode[%d] sqlmsg[%s]",cMMSId,sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,strlen(sqlca.sqlerrm.sqlerrmc)));
			_logPrint(_DATALOG, tmpLog3);
			return -1;
		}
		sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSCTNTBL invaled failed - MMSID[%s]otReuslt[%d]errMsg[%s]",cMMSId,nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
		
    return 1;
}

int CDatabaseORA::getReportDB(CReportDbInfo &rpt_data)
{
	EXEC SQL BEGIN DECLARE SECTION;
	int nJobCode = -1;
	int nSqlCode = -1;	
	int nCnt     = -1;	
	int nPtnId   = -1;
	int nTelcoId = -1;
	
	long long nMsgId = -1;
	
	char szPtnsn      [16+1];
	char szResCode    [16+1];
	char szResvData   [ 512];
	char szEndTelco   [   8];
	char szMsgId      [16+1];
	char szRptDate    [  16];
	char szSqlErrorMsg[1024];
	char szAppName    [  16];
	char szSerial     [16+1];
	char szDstadr     [16+1];
	char szCid        [12+1];	/*  ptnid&jobcode ´eA¼ */
	EXEC SQL END DECLARE SECTION;

	memset(szPtnsn       ,0x00, sizeof(szPtnsn      ));
	memset(szResCode     ,0x00, sizeof(szResCode    ));
	memset(szResvData    ,0x00, sizeof(szResvData   ));
	memset(szDstadr      ,0x00, sizeof(szDstadr     ));
	memset(szEndTelco    ,0x00, sizeof(szEndTelco   ));
	memset(szRptDate     ,0x00, sizeof(szRptDate    ));
	memset(szSqlErrorMsg ,0x00, sizeof(szSqlErrorMsg));
	memset(szAppName     ,0x00, sizeof(szAppName    ));
	memset(szCid         ,0x00, sizeof(szCid        ));
    memset(szMsgId       ,0x00, sizeof(szMsgId      ));

	strcpy(szAppName, rpt_data.szResvData);
	memcpy(szResCode, "Init",4);
    
	EXEC SQL EXECUTE
		BEGIN
			proc_get_mms_report( in_appname       =>:szAppName
            					,ot_ptn_sn        =>:szPtnsn
            					,ot_res_code      =>:szResCode
            					,ot_telco_id      =>:nTelcoId
            					,ot_end_telco     =>:szEndTelco
            					,ot_msg_id        =>:szMsgId
            					,ot_resv_data     =>:szResvData
            					,ot_dstaddr       =>:szDstadr
            					,ot_rpt_telco_date=>:szRptDate
            					,ot_cid           =>:szCid 
            					,ot_cnt           =>:nCnt
            					,ot_sqlcode       =>:nSqlCode
            					,ot_sqlmsg        =>:szSqlErrorMsg
            					); 
		END;
	END-EXEC;

	if( memcmp(szResCode,"Init",4) == 0 )
	{
		sprintf(tmpLog3, "[ERR] CDatabaseORA::getReportDB - CID[%s]ptnSn[%s]sqlcode[%d] sqlmsg[%s][%s]"
                ,szCid
                ,szPtnsn
                ,sqlca.sqlcode
                ,trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc))
                ,szAppName);
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
	
	trim(szResCode	,sizeof(szResCode)	);
	if( memcmp(szResCode,"99", strlen(szResCode)) == 0 || memcmp(szResCode,"98", strlen(szResCode)) == 0 )
	{
		memcpy(rpt_data.szResCode	,"99"	,strlen(szResCode));
		return -1;
	}
	
	trim(szPtnsn	,sizeof(szPtnsn)	);
	trim(szResvData	,sizeof(szResvData)	);
	trim(szDstadr	,sizeof(szDstadr)	);
	trim(szEndTelco	,sizeof(szEndTelco)	);
	trim(szRptDate	,sizeof(szRptDate)	);
	
	strcpy(rpt_data.header.msgType,"2");

	memcpy(rpt_data.szPtnsn		,szPtnsn	,strlen(szPtnsn));
	memcpy(rpt_data.szResCode	,szResCode	,strlen(szResCode));
	memcpy(rpt_data.szAppName	,szAppName	,strlen(szAppName));
	
	rpt_data.nTelcoId 	= nTelcoId;
	
	memcpy(rpt_data.szResvData	,szResvData	,strlen(szResvData));
	memcpy(rpt_data.szDstAdr	,szDstadr	,strlen(szDstadr));
	memcpy(rpt_data.szEndTelco	,szEndTelco	,strlen(szEndTelco));
	
	rpt_data.nMsgId 	= atoll(szMsgId);
	
	memcpy(rpt_data.szRptDate	,szRptDate	,strlen(szRptDate));
	
	rpt_data.nCnt 		= nCnt;
	rpt_data.nSqlCode 	= nSqlCode;
	
	memcpy(rpt_data.szCid		,szCid		,strlen(szCid));
	
	/*
	rpt_data.nJobCode = nJobCode;
	rpt_data.nPtnId = nPtnId;
	*/
	
	return 1;
}

int CDatabaseORA::setRPTTBL(CSenderDbMMSRPTTBL &rpt_data)
{
	EXEC SQL BEGIN DECLARE SECTION;

    char szSqlErrorMsg[1024];
    int nSqlCode = -999;

    long long mms_id;
    char msg_id[4096] = {0x00,};
    char snd_numb[4096] = {0x00,};
    char rcv_numb[4096] = {0x00,};
    int res_code;
    char res_text[4096] = {0x00,};
    int telco_id;
    int res_type;
    char end_telco[4096] = {0x00,};

    EXEC SQL END DECLARE SECTION;
	
    mms_id = 0;
    memset(msg_id, 0x00, sizeof(msg_id));
    memset(snd_numb, 0x00, sizeof(snd_numb));
    memset(rcv_numb, 0x00, sizeof(rcv_numb));
    res_code = 0;
    memset(res_text, 0x00, sizeof(res_text));
    telco_id = 0;
    res_type = 0;
    memset(end_telco, 0x00, sizeof(end_telco));
	
    mms_id = rpt_data.nMMSId;
    sprintf(msg_id, "%ld", rpt_data.nMMSId);
    sprintf(snd_numb, rpt_data.szCallBack);
    sprintf(rcv_numb, rpt_data.szDstAddr);
    res_code = rpt_data.res_code;
    sprintf(res_text, rpt_data.res_text);
    telco_id = 0;

	EXEC SQL EXECUTE
		BEGIN
        PROC_SET_RPT_FORCE(:mms_id, :msg_id, :snd_numb, :rcv_numb, :res_code,
                            :res_text, :telco_id, :res_type, :end_telco, :nSqlCode, :szSqlErrorMsg);
        END;
    END-EXEC;

	if( nSqlCode != 0 )
    {
        if( nSqlCode == -999 )
        {
            sprintf(tmpLog3, "[ERR] CDatabaseORA::setRPTTBL exec failed - MMSID[%lld] sqlcode[%d] sqlmsg[%s]", mms_id, sqlca.sqlcode, trim(sqlca.sqlerrm.sqlerrmc, strlen(sqlca.sqlerrm.sqlerrmc)));
            _logPrint(_DATALOG, tmpLog3);
            return -1;
        }

        sprintf(tmpLog3, "[ERR] CDatabaseORA::setRPTTBL invaled failed - MMSID[%lld]otReuslt[%d]errMsg[%s]", mms_id, nSqlCode, trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
        _logPrint(_DATALOG, tmpLog3);
        return -1;
    }

	return 1;

}

char* CDatabaseORA::trim(char* szOrg, int leng)
{
    int i = 0;
    for (i=leng-1;i>=0;i--) {
        if( (szOrg[i]==0x20)||(szOrg[i]==' ')||(szOrg[i]==0x00) ) {
            szOrg[i] = 0x00;
        }
        else break;
    }
    return szOrg;
}

int CDatabaseORA::selectTblCallback(set<string>& set_callback_list, int _pid)
{

	EXEC SQL BEGIN DECLARE SECTION;
	int pid;
	char callback[12+1];

    EXEC SQL END DECLARE SECTION;

	pid = _pid;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	
	EXEC SQL DECLARE cur_data CURSOR FOR
		SELECT CALLBACK
		FROM TBL_CALLBACK
		WHERE PTN_ID = :pid
	;
	
	EXEC SQL OPEN cur_data;

	if(sqlca.sqlcode!= 0)
	{
		sprintf(tmpLog3, "[ERR] CDatabaseORA::selectTblCallback exec failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode, trim(sqlca.sqlerrm.sqlerrmc, strlen(sqlca.sqlerrm.sqlerrmc)));
		_logPrint(_DATALOG, tmpLog3);
	}

	int nRet = -1;

	while(true)
	{
		memset(callback, 0x00, sizeof(callback));

		EXEC SQL FETCH cur_data INTO
			:callback 	
			;

		if(sqlca.sqlcode == 1403)
		{
			nRet = 1;
			break;
		}
		if(sqlca.sqlcode != 0)
		{
            sprintf(tmpLog3, "[ERR] CDatabaseORA::selectTblCallback exec failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode, trim(sqlca.sqlerrm.sqlerrmc, strlen(sqlca.sqlerrm.sqlerrmc)));
            _logPrint(_DATALOG, tmpLog3);
			return sqlca.sqlcode;
		}

		trim(callback, sizeof(callback));				
		set_callback_list.insert(callback);
	}

	EXEC SQL CLOSE cur_data;
	
	return nRet;
}

int CDatabaseORA::selectAllowDialCode(set<string>& set_dialcode_list, char *_dial_code_type)
{
	EXEC SQL BEGIN DECLARE SECTION;
	char dial_code_type[4+1];
	char dial_code[5+1];
    EXEC SQL END DECLARE SECTION;

	memset(dial_code_type, 0x00, sizeof(dial_code_type));
	snprintf(dial_code_type, sizeof(dial_code_type), _dial_code_type);	
	
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL DECLARE cur CURSOR FOR
		SELECT DIAL_CODE
		FROM TBL_ALLOW_DIAL_CODE
		WHERE DIAL_CODE_TYPE = :dial_code_type
	;
	
	EXEC SQL OPEN cur;

	if(sqlca.sqlcode!= 0)
	{
		sprintf(tmpLog3, "[ERR] CDatabaseORA::selectTblCallback exec failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode, trim(sqlca.sqlerrm.sqlerrmc, strlen(sqlca.sqlerrm.sqlerrmc)));
		_logPrint(_DATALOG, tmpLog3);
	}

	int nRet = -1;

	while(true)
	{
		memset(dial_code, 0x00, sizeof(dial_code));

		EXEC SQL FETCH cur INTO
			:dial_code
			;

		if(sqlca.sqlcode == 1403)
		{
			nRet = 1;
			break;
		}

		if(sqlca.sqlcode != 0)
		{
            sprintf(tmpLog3, "[ERR] CDatabaseORA::selectAllowDialCode  exec failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode, trim(sqlca.sqlerrm.sqlerrmc, strlen(sqlca.sqlerrm.sqlerrmc)));
            _logPrint(_DATALOG, tmpLog3);
			return sqlca.sqlcode;
		}

		trim(dial_code, sizeof(dial_code));
		set_dialcode_list.insert(dial_code);
	}

	EXEC SQL CLOSE cur;

	return nRet;
}

//int CDatabaseORA::getMMSID()
long long CDatabaseORA::getMMSID()
{       
    EXEC SQL BEGIN DECLARE SECTION;
    char szSqlErrorMsg[1024];
    char szCid[10+1]; 
    //int nMMSID = 0;
    long long nMMSID = 0;
    int nSqlCode = -999;
    EXEC SQL END DECLARE SECTION;

    memset(szSqlErrorMsg,0x00,sizeof(szSqlErrorMsg));       //CCL(szSqlErrorMsg);
    memset(szCid        ,0x00,sizeof(szCid        ));       //CCL(szCid);    
    
    memset(szCid, 0x00, 10);
        
    EXEC SQL EXECUTE
        BEGIN
            proc_get_mms_id( in_cid     =>:szCid
                            ,ot_mms_id  =>:nMMSID
                            ,ot_sqlcode =>:nSqlCode
                            ,ot_sqlmsg  =>:szSqlErrorMsg
                            );
        END;
    END-EXEC;
    if( nSqlCode != 0 )
    {
        if( nSqlCode == -999 )
        {
            sprintf(tmpLog3, "[ERR] CDatabaseORA::getMMSID failed - MMSID[%lld] CID[%s] sqlcode[%d] sqlmsg[%s]",nMMSID,szCid,sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc)))
;
            _logPrint(_DATALOG, tmpLog3);
            return -1;
        }
        sprintf(tmpLog3, "[ERR] dCDatabaseORA::getMMSID invaled failed - MMSID[%lld]CID[%s]otReuslt[%d]errMsg[%s]",nMMSID,szCid,nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
        _logPrint(_DATALOG, tmpLog3);
        return -1;
    }

    return nMMSID;
}

int CDatabaseORA::setMMSMSG_TALK(CSenderDbMMSMSG_TALK &que_data)
{   
    EXEC SQL BEGIN DECLARE SECTION;
    char szSqlErrorMsg[1024];
    int nSqlCode = -999;
    
    int nQNum;
    int nPriority;
    char szSenderKey[40+1];
    char szDstAddr[12+1];
    char szTmplCd[10+1];
    char szMsgBody[2000+1];
    
    char cMMSId[20];
    int nMMSId;
    EXEC SQL END DECLARE SECTION;
            
    memset(szSqlErrorMsg, 0x00, sizeof(szSqlErrorMsg));
    memset(szSenderKey, 0x00, sizeof(szSenderKey));
    memset(szDstAddr, 0x00, sizeof(szDstAddr));
    memset(szTmplCd, 0x00, sizeof(szTmplCd));
    memset(szMsgBody, 0x00, sizeof(szMsgBody));
    memset(cMMSId, 0x00, sizeof(cMMSId));
    
    nQNum     = 1;
    nQNum     = atoi(que_data.szQName);
    nPriority = que_data.nPriority;

    strcpy(szSenderKey, que_data.szSenderKey);
    strcpy(szDstAddr, que_data.szDstAddr);
    strcpy(szTmplCd, que_data.szTmplCd);
    strcpy(szMsgBody, que_data.szMsgBody);

    nMMSId  = que_data.nMMSId;

    sprintf(cMMSId, "%lld", que_data.nMMSId);

    EXEC SQL EXECUTE
        BEGIN
            PROC_SET_MMS_MSG_TALK(in_q_num       =>:nQNum
                                 ,in_priority    =>:nPriority
                                 ,in_sender_key  =>:szSenderKey
                                 ,in_dst_addr    =>:szDstAddr
                                 ,in_template_cd =>:szTmplCd
                                 ,in_msg_body    =>:szMsgBody
                                 ,in_mms_id      =>:cMMSId
                                 ,ot_sqlcode     =>:nSqlCode
                                 ,ot_sqlmsg      =>:szSqlErrorMsg
                                 );

        END;
    END-EXEC;

    if( nSqlCode != 0 )
    {
        if( nSqlCode == -999 )
        {
            sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSMSG_TALK exec failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,strlen(sqlca.sqlerrm.
sqlerrmc)));
            _logPrint(_DATALOG, tmpLog3);
            return -1;
        }
        sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSMSG_TALK invaled failed - otReuslt[%d]errMsg[%s]",nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
        _logPrint(_DATALOG, tmpLog3);
        return -1;
    }

    return 1;
}

int CDatabaseORA::setMMSMSG_FTK_V3(CSenderDbMMSMSG_FTALK &que_data)
{
    EXEC SQL BEGIN DECLARE SECTION;
    char szSqlErrorMsg[1024];
    int nSqlCode = -999;

    int nQNum;
    int nPriority;
    char szSenderKey[40+1];
    //char szDstAddr[12+1];
    char szDstAddr[16+1];
    char szUserKey[30+1];
    char szMsgBody[2000+1];
    char szBtName[50+1];
    //char szBtUrl[1000+1];
    char szBtUrl[500+1];
    //char szButton[4000+1];
    char szButton[3000+1];
    char szImgPath[100+1];
    //char szImgLink[1000+1];
    char szImgLink[500+1];
    char szResMethod[8+1];
    char szTimeout[2+1];
    char szAdFlag[1+1];
    char szWide[1+1];
	char szKkoImgUrl[500+1];

    char cMMSId[20];
    long long lMMSId;
    EXEC SQL END DECLARE SECTION;

    memset(szSqlErrorMsg, 0x00, sizeof(szSqlErrorMsg));
    memset(szSenderKey, 0x00, sizeof(szSenderKey));
    memset(szDstAddr, 0x00, sizeof(szDstAddr));
    memset(szUserKey, 0x00, sizeof(szUserKey));
    memset(szMsgBody, 0x00, sizeof(szMsgBody));
    memset(szBtName, 0x00, sizeof(szBtName));
    memset(szBtUrl, 0x00, sizeof(szBtUrl));
    memset(szButton, 0x00, sizeof(szButton));
    memset(szImgPath, 0x00, sizeof(szImgPath));
    memset(szImgLink, 0x00, sizeof(szImgLink));
    memset(cMMSId, 0x00, sizeof(cMMSId));
    memset(szResMethod, 0x00, sizeof(szResMethod));
    memset(szTimeout, 0x00, sizeof(szTimeout));
    memset(szAdFlag, 0x00, sizeof(szAdFlag));
    memset(szWide, 0x00, sizeof(szWide));
	memset(szKkoImgUrl,   0x00, sizeof(szKkoImgUrl));

	nQNum     = 1;
    nQNum     = atoi(que_data.szQName);
    nPriority = que_data.nPriority;
    sprintf(szSenderKey, que_data.szSenderKey);
    sprintf(szDstAddr, que_data.szDstAddr);
    sprintf(szUserKey, que_data.szUserKey);
    sprintf(szBtName, que_data.szBtName);
    //sprintf(szBtUrl, que_data.szBtUrl);
    strncpy(szBtUrl, que_data.szBtUrl,sizeof(szBtUrl)-1);
    //sprintf(szButton, que_data.szButton);
    strncpy(szButton, que_data.szButton,sizeof(szButton)-1);
    sprintf(szImgPath, que_data.szImgPath);
    //sprintf(szImgLink, que_data.szImgLink);
    strncpy(szImgLink, que_data.szImgLink,sizeof(szImgLink)-1);
    //sprintf(szMsgBody, que_data.szMsgBody);
    strncpy(szMsgBody, que_data.szMsgBody,sizeof(szMsgBody)-1);
    sprintf(szResMethod, que_data.szResMethod);
    sprintf(szTimeout, que_data.szTimeout);
    sprintf(szAdFlag, que_data.szAdFlag);
    sprintf(szWide, que_data.szWide);

	strncpy(szKkoImgUrl, que_data.szKkoImgUrl, sizeof(szKkoImgUrl)-1);

    sprintf(cMMSId, "%lld", que_data.nMMSId);
    //lMMSId    = que_data.nMMSId;
    
    /*sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSMSG_FTALK_V3 que[%d] Priority[%d] cMMSId[%s] szSenderKey[%s] szDstAddr[%s] szMsgBody[%s] szBtName[%s] szBtUrl[%s] szButton[%s] szImgPath[%s] szImgLink[%s] szResMethod[%s] szTimeout[%s] szAdFlag[%s] szWide[%s]",
            nQNum       ,        
            nPriority   ,  
            cMMSId      ,  
            szSenderKey ,  
            szDstAddr   ,  
            szMsgBody   ,  
            szBtName    ,  
            szBtUrl     ,  
            szButton 	,  
            szImgPath   ,  
            szImgLink   ,  
            szResMethod ,  
            szTimeout,
            szAdFlag,
            szWide);
            _logPrint(_DATALOG, tmpLog3);*/

    EXEC SQL EXECUTE
        BEGIN
            PROC_SET_FTK_MSG_V3(in_q_num         =>:nQNum
                                 ,in_priority    =>:nPriority
                                 ,in_mms_id      =>:cMMSId
                                 ,in_sender_key  =>:szSenderKey
                                 ,in_dst_addr    =>:szDstAddr
                                 ,in_user_key    =>:szUserKey
                                 ,in_msg_body    =>:szMsgBody
                                 ,in_button_name =>:szBtName
                                 ,in_button_url  =>:szBtUrl
                                 ,in_button      =>:szButton 	
                                 ,in_img_path    =>:szImgPath
                                 ,in_img_link    =>:szImgLink
                                 ,in_res_method  =>:szResMethod
                                 ,in_tmout       =>:szTimeout
                                 ,in_ad_flag     =>:szAdFlag
                                 ,in_wide        =>:szWide
								 ,in_kko_img_url =>:szKkoImgUrl	
                                 ,ot_sqlcode     =>:nSqlCode
                                 ,ot_sqlmsg      =>:szSqlErrorMsg
                                 );

        END;
    END-EXEC;

    if( nSqlCode != 0 )
    {
        if( nSqlCode == -999 )
        {
            sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSMSG_FTK_V3 exec failed - MMSID[%s] sqlcode[%d] sqlmsg[%s]",cMMSId, sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,strlen(sqlca.sqlerrm.sqlerrmc)));
            _logPrint(_DATALOG, tmpLog3);
            return -1;
        }
        sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSMSG_FTK_V3 invaled failed - MMSID[%s]otReuslt[%d]errMsg[%s]",cMMSId, nSqlCode, trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
        _logPrint(_DATALOG, tmpLog3);
        return -1;
    }

    return 1;
}

int CDatabaseORA::setMMSMSG_FTKUP(CSenderDbMMSMSG_FTALKUP &que_data) {
	EXEC SQL BEGIN DECLARE SECTION;
    char szSqlErrorMsg[1024];
    int nSqlCode = -999;

    int nQNum;
    int nPriority;
    char szSenderKey[40+1];
    //char szDstAddr[12+1];
    char szDstAddr[16+1];
    char szMsgBody[2000+1];
    //char szButton[4000+1];
    char szButton[3000+1];
    char szResMethod[8+1];
    char szTimeout[2+1];
	char szEncoding[20+1];
	char szChatBubbleType[30+1];
	char szTargeting[1+1];
	char szTmplCd    [20+1];
	char szAppUserId [20+1];
	char szPushAlarm [1+1];
	char szMessageVariable	[2000+1];
	char szButtonVariable 	[2000+1];
	char szCouponVariable	[1000+1];
	char szImageVariable	[1000+1];
	char szVideoVariable	[1000+1];
	char szCommerceVariable	[1000+1];
	char szCarouselVariable	[2000+1];
	char szReserve			[4000+1];
    char cMMSId[20];
    long long lMMSId;
    EXEC SQL END DECLARE SECTION;

	memset(szSqlErrorMsg, 0x00, sizeof(szSqlErrorMsg));
	memset(szSenderKey, 0x00, sizeof(szSenderKey));
	memset(szDstAddr, 0x00, sizeof(szDstAddr));
	memset(szMsgBody, 0x00, sizeof(szMsgBody));
	memset(szButton, 0x00, sizeof(szButton));
	memset(cMMSId, 0x00, sizeof(cMMSId));
	memset(szResMethod, 0x00, sizeof(szResMethod));
	memset(szTimeout, 0x00, sizeof(szTimeout));
	memset(szEncoding, 0x00, sizeof(szEncoding));
	memset(szChatBubbleType, 0x00, sizeof(szChatBubbleType));
	memset(szTargeting, 0x00, sizeof(szTargeting));
	memset(szTmplCd, 0x00, sizeof(szTmplCd));
	memset(szAppUserId, 0x00, sizeof(szAppUserId));
	memset(szPushAlarm, 0x00, sizeof(szPushAlarm));
	memset(szMessageVariable, 0x00, sizeof(szMessageVariable));
	memset(szButtonVariable, 0x00, sizeof(szButtonVariable));
	memset(szCouponVariable, 0x00, sizeof(szCouponVariable));
	memset(szImageVariable, 0x00, sizeof(szImageVariable));
	memset(szVideoVariable, 0x00, sizeof(szVideoVariable));
	memset(szCommerceVariable, 0x00, sizeof(szCommerceVariable));
	memset(szCarouselVariable, 0x00, sizeof(szCarouselVariable));
	memset(szReserve, 0x00, sizeof(szReserve));
	
	nQNum     = 1;
    nQNum     = atoi(que_data.szQName);
    nPriority = que_data.nPriority;
    sprintf(szSenderKey, que_data.szSenderKey);
    sprintf(szDstAddr, que_data.szDstAddr);
    //strncpy(szButton, que_data.szButton,sizeof(szButton)-1);
    //sprintf(szMsgBody, que_data.szMsgBody);
    //strncpy(szMsgBody, que_data.szMsgBody,sizeof(szMsgBody)-1);
    sprintf(szResMethod, que_data.szResMethod);
    sprintf(szTimeout, que_data.szTimeout);
	snprintf(szEncoding, sizeof(szEncoding), que_data.szEncoding);
	snprintf(szChatBubbleType, sizeof(szChatBubbleType), que_data.szChatBubbleType);
	snprintf(szTargeting, sizeof(szTargeting), que_data.szTargeting);
	snprintf(szTmplCd, sizeof(szTmplCd), que_data.szTmplCd);
	snprintf(szAppUserId, sizeof(szAppUserId), que_data.szAppUserId);
	snprintf(szPushAlarm, sizeof(szPushAlarm), que_data.szPushAlarm);
	snprintf(szMessageVariable, sizeof(szMessageVariable), que_data.szMessageVariable);
#if (DEBUG >= 6)
	sprintf(tmpLog3, "[DEB] setMMSMSG_FTKUP message_variable set[%s] mms_id[%s]",
			szMessageVariable, cMMSId);
	_logPrint(_DATALOG, tmpLog3);
#endif
	snprintf(szButtonVariable, sizeof(szButtonVariable), que_data.szButtonVariable);
	snprintf(szCouponVariable, sizeof(szCouponVariable), que_data.szCouponVariable);
	snprintf(szImageVariable, sizeof(szImageVariable), que_data.szImageVariable);
	snprintf(szVideoVariable, sizeof(szVideoVariable), que_data.szVideoVariable);
	snprintf(szCommerceVariable, sizeof(szCommerceVariable), que_data.szCommerceVariable);
	snprintf(szCarouselVariable, sizeof(szCarouselVariable), que_data.szCarouselVariable);
	snprintf(szReserve, sizeof(szReserve), que_data.szReserve);

    sprintf(cMMSId, "%lld", que_data.nMMSId);
    //lMMSId    = que_data.nMMSId;

	sprintf(tmpLog3,
			"[ERR] CDatabaseORA::setMMSMSG_FTKUP que[%d] Priority[%d] cMMSId[%s] szSenderKey[%s] "
			"szDstAddr[%s] szResMethod[%s] szTimeout[%s] ",
			nQNum, nPriority, cMMSId, szSenderKey, szDstAddr, 
			szResMethod, szTimeout
			);
	_logPrint(_DATALOG, tmpLog3);

    EXEC SQL EXECUTE
        BEGIN
            PROC_SET_FTKUP_MSG(in_q_num         =>:nQNum
                                 ,in_priority    =>:nPriority
                                 ,in_mms_id      =>:cMMSId
                                 ,in_sender_key  =>:szSenderKey
                                 ,in_dst_addr    =>:szDstAddr
                                 ,in_res_method  =>:szResMethod
                                 ,in_tmout       =>:szTimeout
                                 ,in_encoding_type   =>:szEncoding
								 , in_chat_bubble_type =>:szChatBubbleType
								 , in_targeting =>:szTargeting
								 , in_tmpl_cd =>:szTmplCd
								 , in_app_user_id =>:szAppUserId
								 , in_push_alarm =>:szPushAlarm
								 , in_message_variable =>:szMessageVariable
								 , in_button_variable =>:szButtonVariable
								 , in_coupon_variable =>:szCouponVariable
								 , in_image_variable =>:szImageVariable
								 , in_video_variable =>:szVideoVariable
								 , in_commerce_variable =>:szCommerceVariable
								 , in_carousel_variable =>:szCarouselVariable
								 , in_reserve	=> :szReserve
                                 ,ot_sqlcode     =>:nSqlCode
                                 ,ot_sqlmsg      =>:szSqlErrorMsg
                                 );

        END;
    END-EXEC;

    if( nSqlCode != 0 )
    {
        if( nSqlCode == -999 )
        {
            sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSMSG_FTKUP exec failed - MMSID[%s] sqlcode[%d] sqlmsg[%s]",
            	cMMSId, sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,strlen(sqlca.sqlerrm.sqlerrmc)));
            _logPrint(_DATALOG, tmpLog3);
            return -1;
        }
        sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSMSG_FTKUP invaled failed - MMSID[%s]otReuslt[%d]errMsg[%s]",
        	cMMSId, nSqlCode, trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
        _logPrint(_DATALOG, tmpLog3);
        return -1;
    }

    return 1;
	
}

int CDatabaseORA::setMMSMSG_TALKBt(CSenderDbMMSMSG_TALK &que_data)
{
	EXEC SQL BEGIN DECLARE SECTION;
	char szSqlErrorMsg[1024];
	int nSqlCode = -999;

	int nQNum;
	int nPriority;
	char szSenderKey[40+1];
	char szDstAddr[12+1];
	char szTmplCd[30+1];
	char szBtName[50+1];
	char szBtUrl[1000+1];
	char szMsgBody[2000+1];
	
	char cMMSId[20];
	long long lMMSId;
	EXEC SQL END DECLARE SECTION;

	memset(szSqlErrorMsg, 0x00, sizeof(szSqlErrorMsg));
	memset(szSenderKey, 0x00, sizeof(szSenderKey));
	memset(szDstAddr, 0x00, sizeof(szDstAddr));
	memset(szTmplCd, 0x00, sizeof(szTmplCd));
	memset(szBtName, 0x00, sizeof(szBtName));
	memset(szBtUrl, 0x00, sizeof(szBtUrl));
	memset(szMsgBody, 0x00, sizeof(szMsgBody));
	memset(cMMSId, 0x00, sizeof(cMMSId));

	nQNum     = 1;
	nQNum     = atoi(que_data.szQName);
	nPriority = que_data.nPriority;
	sprintf(szSenderKey, que_data.szSenderKey);
	sprintf(szDstAddr, que_data.szDstAddr);
	sprintf(szTmplCd, que_data.szTmplCd);
	sprintf(szBtName, que_data.szBtName);
	sprintf(szBtUrl, que_data.szBtUrl);
	sprintf(szMsgBody, que_data.szMsgBody);
	
	sprintf(cMMSId, "%lld", que_data.nMMSId);
	//lMMSId	= que_data.nMMSId;
	
	EXEC SQL EXECUTE
		BEGIN
			PROC_SET_TALKBT_MSG(in_q_num       =>:nQNum
								 ,in_priority    =>:nPriority
								 ,in_sender_key  =>:szSenderKey
								 ,in_dst_addr    =>:szDstAddr
								 ,in_template_cd =>:szTmplCd
								 ,in_button_name =>:szBtName
								 ,in_button_url  =>:szBtUrl
								 ,in_msg_body    =>:szMsgBody
								 ,in_mms_id      =>:cMMSId
								 ,ot_sqlcode     =>:nSqlCode
								 ,ot_sqlmsg      =>:szSqlErrorMsg
								 );

		END;
	END-EXEC;
	
	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSMSG_TALKBt exec failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,strlen(sqlca.sqlerrm.sqlerrmc)));
			_logPrint(_DATALOG, tmpLog3);
			return -1;
		}
		sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSMSG_TALKBt invaled failed - otReuslt[%d]errMsg[%s]",nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
		
    return 1;
}

// 20170621 MMSID SEQ USE
int CDatabaseORA::selectSEQ()
{

	int nRet = -1;

	EXEC SQL BEGIN DECLARE SECTION;
	int seq = -1;
  EXEC SQL END DECLARE SECTION;

	
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	
	EXEC SQL DECLARE cur_proc_id CURSOR FOR
		SELECT SEQ_PROC_ID.NEXTVAL FROM DUAL;

	
	EXEC SQL OPEN cur_proc_id;

	if(sqlca.sqlcode!= 0)
	{
		sprintf(tmpLog3, "[ERR] CDatabaseORA::selectSEQ OPEN failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode, trim(sqlca.sqlerrm.sqlerrmc, strlen(sqlca.sqlerrm.sqlerrmc)));
		_logPrint(_DATALOG, tmpLog3);
		return nRet;		
	}
	

	while(true)
	{

		EXEC SQL FETCH cur_proc_id INTO
			:seq ; 	

		sprintf(tmpLog3, "[INF] CDatabaseORA::selectSEQ seq[%d] sqlcode[%d] sqlmsg[%s]",seq, sqlca.sqlcode, trim(sqlca.sqlerrm.sqlerrmc, strlen(sqlca.sqlerrm.sqlerrmc)));
            _logPrint(_DATALOG, tmpLog3);
            			
		if( seq > 0 )
			break;
				
		if(sqlca.sqlcode != 0)
		{
            sprintf(tmpLog3, "[ERR] CDatabaseORA::selectSEQ FETCH failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode, trim(sqlca.sqlerrm.sqlerrmc, strlen(sqlca.sqlerrm.sqlerrmc)));
            _logPrint(_DATALOG, tmpLog3);
            
			EXEC SQL CLOSE cur_proc_id;            
			return nRet;
		}

	}

	EXEC SQL CLOSE cur_proc_id;
	
	return seq;
}

int CDatabaseORA::setSendReportData(CSenderDbMMSRPTQUE &rpt_data)
{
	EXEC SQL BEGIN DECLARE SECTION;
	int ot_sqlcode = -1;
	char ot_sqlmsg[1024];
	int ot_queue_sqlcode = -1;
	char ot_queue_sqlmsg[1024];

	char msg_id[50+1];
	long long mms_id;
	char cmms_id[32+1];
	char dlv_date[14+1];
	char snd_numb[12+1];
	char rcv_numb[12+1];
	char res_code[4+1];
	char res_text[200+1];
	char cid[10+1];
	char ptn_sn[16+1];
	int telco_id;
	int res_type;
	char end_telco[5+1];
	char resv_data[200+1];
	

	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;
	
	int type;
	string cTime;
	time_t tm_time;
	struct tm *st_time;
	char buff[1024] = {0x00,};

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
    EXEC SQL WHENEVER SQLERROR CONTINUE;   /* don't trap errors */

	memset(ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
	memset(ot_queue_sqlmsg, 0x00, sizeof ot_queue_sqlmsg);
	memset(msg_id, 0x00, sizeof msg_id);
	memset(dlv_date, 0x00, sizeof dlv_date);
	memset(snd_numb, 0x00, sizeof snd_numb);
	memset(rcv_numb, 0x00, sizeof rcv_numb);
	memset(res_code, 0x00, sizeof res_code);
	memset(res_text, 0x00, sizeof res_text);
	memset(cid, 0x00, sizeof cid);
	memset(ptn_sn, 0x00, sizeof ptn_sn);
	memset(end_telco, 0x00, sizeof end_telco);
	memset(cmms_id,	0x00, sizeof(cmms_id));
	memset(resv_data,	0x00, sizeof(resv_data));

	char sNum[24+1];
	char rNum[24+1];
	memset(sNum, 0x00, 24+1);
	memset(rNum, 0x00, 24+1);
	
	//map<string, string>::iterator FindItr;
	
	//FindItr = mapReport.find("msg_id");
	
	mms_id = rpt_data.nMMSId;
	
	sprintf(cmms_id,"%lld", mms_id);
	
	sprintf(msg_id, "%lld", mms_id); //msg_id
	
	sprintf(cid, "%s", rpt_data.szCid); //msg_id
	
	time(&tm_time);
	st_time = localtime(&tm_time);
	strftime(buff, 1024, "%Y%m%d%H%M%S", st_time);
	cTime = buff;
	
	sprintf(dlv_date, "%s", cTime.c_str()); //reg_snd_dttm
	if (strcmp(dlv_date, "") == 0 || strcmp(dlv_date, "              ") == 0)
	{
		strcpy(dlv_date,"19710101000000");
	}

			
	sprintf(res_code, "%d", rpt_data.res_code); //rslt_val

	sprintf(end_telco, "%s", "KKO");

	sprintf(res_text, "%s", rpt_data.res_text);
	
	sprintf(ptn_sn, "%s", rpt_data.szPtnSn);
	
	telco_id = rpt_data.nTelcoId;
	res_type = 0;

	type = rpt_data.nType;
	//strcpy(snd_numb, sNum);
	//FindItr = mapReport.find("rcv_numb");
	//sprintf(rcv_numb, FindItr->second.c_str());
	
	if(type != 1)
	{
		sprintf(rcv_numb, "%s", rpt_data.szDstAddr);
	}
	
	//int retry_cnt = 0;
	
	
//retry:
	if(type == 1)
	{
		EXEC SQL EXECUTE
		BEGIN
			proc_set_rpt_skb(:cmms_id, :msg_id, :dlv_date, :snd_numb, :rcv_numb, 
			    :res_code, :res_text, :telco_id, :res_type, :end_telco, 
			    :ot_sqlcode, :ot_sqlmsg, :ot_queue_sqlcode, :ot_queue_sqlmsg);
		END;
		END-EXEC;
    	
		if( ot_sqlcode != 0 )
		{
			sprintf(tmpLog3, "CDatabaseORA::setReportData() SNDTBL ERROR MMSID[%lld][%d][%.100s]", 
				mms_id, ot_sqlcode, ot_sqlmsg);
			_logPrint(_DATALOG, tmpLog3);
			return -1;
		}
		
		if( ot_queue_sqlcode != 0 )
		{
			sprintf(tmpLog3, "CDatabaseORA::setReportData() SNDTBL queue ERROR MMSID[%lld][%d][%.100s]", 
				mms_id, ot_queue_sqlcode, ot_queue_sqlmsg);
			_logPrint(_DATALOG, tmpLog3);
			return -1;
		}
	}
	else
	{
		EXEC SQL EXECUTE
		BEGIN
			proc_set_mms_rpt_queue(:cid, :ptn_sn, :res_code, :telco_id, :end_telco, 
			    :cmms_id, :resv_data, :rcv_numb, :dlv_date, :ot_sqlcode, :ot_sqlmsg);
		END;
		END-EXEC;
    	
		if( ot_sqlcode != 0 )
		{
			sprintf(tmpLog3, "CDatabaseORA::setReportData() SND queue ERROR MMSID[%lld][%d][%.100s]", 
				mms_id, ot_sqlcode, ot_sqlmsg);
			_logPrint(_DATALOG, tmpLog3);
			return -1;
		}
	}
	
	return 0;
}
	
int CDatabaseORA::procCheckMmsLoginExt2(const CMmsLoginInput& input, CMmsLoginOutput& output)
{
	EXEC SQL BEGIN DECLARE SECTION;
	char v_in_cid[64];
	char v_in_pwd[64];
	char v_appname[256];
	char v_sip[64];
	int v_pid;
	int v_job;
	int v_c_job;
	int v_prt;
	int v_cnt;
	int v_rpt_cnt;
	char v_server_info[512];
	int v_rpt_sleep_cnt;
	char v_sender_proc[256];
	char v_report_proc[256];
	char v_senderdb_info[512];
	char v_reportdb_info[512];
	char v_logfile_info[512];
	char v_etc[512];
	int v_rst;
	char v_rstmsg[512];

	// indicator variables for NULL value handling
	short ind_appname;
	short ind_sip;
	short ind_server_info;
	short ind_sender_proc;
	short ind_report_proc;
	short ind_senderdb_info;
	short ind_reportdb_info;
	short ind_logfile_info;
	short ind_etc;
	short ind_rstmsg;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER NOT FOUND CONTINUE;
	EXEC SQL WHENEVER SQLERROR CONTINUE;

	// Copy class-based input parameters
	memset(v_in_cid, 0x00, sizeof(v_in_cid));
	memset(v_in_pwd, 0x00, sizeof(v_in_pwd));
	strncpy(v_in_cid, input.cid, sizeof(v_in_cid) - 1);
	strncpy(v_in_pwd, input.pwd, sizeof(v_in_pwd) - 1);

	// Initialize output parameters
	memset(v_appname, 0x00, sizeof(v_appname));
	memset(v_sip, 0x00, sizeof(v_sip));
	memset(v_server_info, 0x00, sizeof(v_server_info));
	memset(v_sender_proc, 0x00, sizeof(v_sender_proc));
	memset(v_report_proc, 0x00, sizeof(v_report_proc));
	memset(v_senderdb_info, 0x00, sizeof(v_senderdb_info));
	memset(v_reportdb_info, 0x00, sizeof(v_reportdb_info));
	memset(v_logfile_info, 0x00, sizeof(v_logfile_info));
	memset(v_etc, 0x00, sizeof(v_etc));
	memset(v_rstmsg, 0x00, sizeof(v_rstmsg));
	v_rst = -999;

	// Initialize integer variables
	v_pid = 0;
	v_job = 0;
	v_c_job = 0;
	v_prt = 0;
	v_cnt = 0;
	v_rpt_cnt = 0;
	v_rpt_sleep_cnt = 3;

	// Initialize indicator variables
	ind_appname = 0;
	ind_sip = 0;
	ind_server_info = 0;
	ind_sender_proc = 0;
	ind_report_proc = 0;
	ind_senderdb_info = 0;
	ind_reportdb_info = 0;
	ind_logfile_info = 0;
	ind_etc = 0;
	ind_rstmsg = 0;

	// Debug log - Class-based input parameters
	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) INPUT - cid[%s] pwd[%s]", input.cid, input.pwd);
	_logPrint(_DATALOG, tmpLog3);

	// Debug log - Initial values before procedure call
	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2() BEFORE PROC - v_pid[%d] v_job[%d] v_prt[%d] v_cnt[%d] v_rpt_cnt[%d] v_rpt_sleep_cnt[%d]",
		v_pid, v_job, v_prt, v_cnt, v_rpt_cnt, v_rpt_sleep_cnt);
	_logPrint(_DATALOG, tmpLog3);

	EXEC SQL EXECUTE
		BEGIN
			proc_check_mms_login_ext2(
				in_cid => :v_in_cid,
				in_pwd => :v_in_pwd,
				ot_appname => :v_appname:ind_appname,
				ot_sip => :v_sip:ind_sip,
				ot_pid => :v_pid,
				ot_job => :v_job,
				ot_c_job => :v_c_job,
				ot_prt => :v_prt,
				ot_cnt => :v_cnt,
				ot_rpt_cnt => :v_rpt_cnt,
				ot_server_info => :v_server_info:ind_server_info,
				ot_rpt_sleep_cnt => :v_rpt_sleep_cnt,
				ot_sender_proc => :v_sender_proc:ind_sender_proc,
				ot_report_proc => :v_report_proc:ind_report_proc,
				ot_senderdb_info => :v_senderdb_info:ind_senderdb_info,
				ot_reportdb_info => :v_reportdb_info:ind_reportdb_info,
				ot_logfile_info => :v_logfile_info:ind_logfile_info,
				ot_etc => :v_etc:ind_etc,
				ot_rst => :v_rst,
				ot_rstmsg => :v_rstmsg:ind_rstmsg
			);
		END;
	END-EXEC;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		_logPrint(_DATALOG, tmpLog3);
		output.rst = -1;
		strncpy(output.rstmsg, sqlca.sqlerrm.sqlerrmc, sizeof(output.rstmsg) - 1);
		output.rstmsg[sizeof(output.rstmsg) - 1] = '\0';
		return -1;
	}

	// Debug log - All output values immediately after procedure call
	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2() AFTER PROC - v_pid[%d] v_job[%d] v_c_job[%d] v_prt[%d] v_cnt[%d] v_rpt_cnt[%d] v_rpt_sleep_cnt[%d] v_rst[%d]",
		v_pid, v_job, v_c_job, v_prt, v_cnt, v_rpt_cnt, v_rpt_sleep_cnt, v_rst);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2() AFTER PROC - v_appname[%s] v_sip[%s]", v_appname, v_sip);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2() AFTER PROC - v_server_info[%s]", v_server_info);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2() AFTER PROC - v_sender_proc[%s] v_report_proc[%s]", v_sender_proc, v_report_proc);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2() AFTER PROC - v_senderdb_info[%s]", v_senderdb_info);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2() AFTER PROC - v_reportdb_info[%s]", v_reportdb_info);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2() AFTER PROC - v_logfile_info[%s]", v_logfile_info);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2() AFTER PROC - v_etc[%s] v_rstmsg[%s]", v_etc, v_rstmsg);
	_logPrint(_DATALOG, tmpLog3);

	// Copy class-based output parameters (with NULL value check)
	if (ind_appname == -1) {
		sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) WARN - v_appname is NULL");
		_logPrint(_DATALOG, tmpLog3);
		strcpy(output.appname, "");
	} else {
		strncpy(output.appname, v_appname, sizeof(output.appname) - 1);
		output.appname[sizeof(output.appname) - 1] = '\0';
	}

	if (ind_sip == -1) {
		sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) WARN - v_sip is NULL");
		_logPrint(_DATALOG, tmpLog3);
		strcpy(output.sip, "");
	} else {
		strncpy(output.sip, v_sip, sizeof(output.sip) - 1);
		output.sip[sizeof(output.sip) - 1] = '\0';
	}

	// Debug log before setting class values
	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) DEBUG - Before setting class values: v_pid[%d] v_job[%d] v_prt[%d] v_cnt[%d]", v_pid, v_job, v_prt, v_cnt);
	_logPrint(_DATALOG, tmpLog3);

	output.pid = v_pid;
	output.job = v_job;
	output.c_job = v_c_job;
	output.prt = v_prt;
	output.cnt = v_cnt;
	output.rpt_cnt = v_rpt_cnt;

	// Debug log after setting class values - ALL OUTPUT PARAMETERS
	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) FINAL OUTPUT - pid[%d] job[%d] c_job[%d] prt[%d] cnt[%d] rpt_cnt[%d] rpt_sleep_cnt[%d] rst[%d]",
		output.pid, output.job, output.c_job, output.prt, output.cnt, output.rpt_cnt, output.rpt_sleep_cnt, output.rst);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) FINAL OUTPUT - appname[%s] sip[%s]", output.appname, output.sip);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) FINAL OUTPUT - server_info[%s]", output.server_info);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) FINAL OUTPUT - sender_proc[%s] report_proc[%s]", output.sender_proc, output.report_proc);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) FINAL OUTPUT - senderdb_info[%s]", output.senderdb_info);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) FINAL OUTPUT - reportdb_info[%s]", output.reportdb_info);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) FINAL OUTPUT - logfile_info[%s]", output.logfile_info);
	_logPrint(_DATALOG, tmpLog3);

	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) FINAL OUTPUT - etc[%s] rstmsg[%s]", output.etc, output.rstmsg);
	_logPrint(_DATALOG, tmpLog3);

	if (ind_server_info == -1) {
		sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) WARN - v_server_info is NULL");
		_logPrint(_DATALOG, tmpLog3);
		strcpy(output.server_info, "");
	} else {
		strncpy(output.server_info, v_server_info, sizeof(output.server_info) - 1);
		output.server_info[sizeof(output.server_info) - 1] = '\0';
	}

	output.rpt_sleep_cnt = v_rpt_sleep_cnt;

	if (ind_sender_proc == -1) {
		sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) WARN - v_sender_proc is NULL");
		_logPrint(_DATALOG, tmpLog3);
		strcpy(output.sender_proc, "");
	} else {
		strncpy(output.sender_proc, v_sender_proc, sizeof(output.sender_proc) - 1);
		output.sender_proc[sizeof(output.sender_proc) - 1] = '\0';
	}

	if (ind_report_proc == -1) {
		sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) WARN - v_report_proc is NULL");
		_logPrint(_DATALOG, tmpLog3);
		strcpy(output.report_proc, "");
	} else {
		strncpy(output.report_proc, v_report_proc, sizeof(output.report_proc) - 1);
		output.report_proc[sizeof(output.report_proc) - 1] = '\0';
	}

	if (ind_senderdb_info == -1) {
		sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) WARN - v_senderdb_info is NULL");
		_logPrint(_DATALOG, tmpLog3);
		strcpy(output.senderdb_info, "");
	} else {
		strncpy(output.senderdb_info, v_senderdb_info, sizeof(output.senderdb_info) - 1);
		output.senderdb_info[sizeof(output.senderdb_info) - 1] = '\0';
	}

	if (ind_reportdb_info == -1) {
		sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) WARN - v_reportdb_info is NULL");
		_logPrint(_DATALOG, tmpLog3);
		strcpy(output.reportdb_info, "");
	} else {
		strncpy(output.reportdb_info, v_reportdb_info, sizeof(output.reportdb_info) - 1);
		output.reportdb_info[sizeof(output.reportdb_info) - 1] = '\0';
	}

	if (ind_logfile_info == -1) {
		sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) WARN - v_logfile_info is NULL");
		_logPrint(_DATALOG, tmpLog3);
		strcpy(output.logfile_info, "");
	} else {
		strncpy(output.logfile_info, v_logfile_info, sizeof(output.logfile_info) - 1);
		output.logfile_info[sizeof(output.logfile_info) - 1] = '\0';
	}

	if (ind_etc == -1) {
		sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) WARN - v_etc is NULL");
		_logPrint(_DATALOG, tmpLog3);
		strcpy(output.etc, "");
	} else {
		strncpy(output.etc, v_etc, sizeof(output.etc) - 1);
		output.etc[sizeof(output.etc) - 1] = '\0';
	}

	output.rst = v_rst;

	if (ind_rstmsg == -1) {
		sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) WARN - v_rstmsg is NULL");
		_logPrint(_DATALOG, tmpLog3);
		strcpy(output.rstmsg, "");
	} else {
		strncpy(output.rstmsg, v_rstmsg, sizeof(output.rstmsg) - 1);
		output.rstmsg[sizeof(output.rstmsg) - 1] = '\0';
	}

	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) SUCCESS - RST[%d]", v_rst);
	_logPrint(_DATALOG, tmpLog3);

	// Debug log before returning - all procedure values and all class values
	sprintf(tmpLog3, "CDatabaseORA::procCheckMmsLoginExt2(class-based) DEBUG - Before returning: v_rst[%d] v_rstmsg[%s] output.rst[%d] output.rstmsg[%s]", v_rst, v_rstmsg, output.rst, output.rstmsg);
	_logPrint(_DATALOG, tmpLog3);

	return 1;
}

// int CDatabaseORA::procGetLimitDef(int in_pid, char* ot_limit_type, int* ot_day_warn_cnt,
//                                  int* ot_day_limit_cnt, int* ot_mon_warn_cnt, int* ot_mon_limit_cnt,
//                                  char* ot_limit_flag, int* ot_day_acc_cnt, int* ot_mon_acc_cnt,
//                                  int* ot_rst, char* ot_rstmsg)
int CDatabaseORA::procGetLimitDef(const CLimitDefInput& input, CLimitDefOutput& output)	
{
	EXEC SQL BEGIN DECLARE SECTION;
	int v_in_pid;
	char v_limit_type[256];
	int v_day_warn_cnt;
	int v_day_limit_cnt;
	int v_mon_warn_cnt;
	int v_mon_limit_cnt;
	char v_limit_flag[256];
	int v_day_acc_cnt;
	int v_mon_acc_cnt;
	int v_rst;
	char v_rstmsg[512];
	EXEC SQL END DECLARE SECTION;

	// Set class-based input parameters
	v_in_pid = input.pid;

	// Initialize output parameters
	memset(v_limit_type, 0x00, sizeof(v_limit_type));
	memset(v_limit_flag, 0x00, sizeof(v_limit_flag));
	memset(v_rstmsg, 0x00, sizeof(v_rstmsg));
	v_rst = -999;

	EXEC SQL EXECUTE
		BEGIN
			proc_get_limit_def(
				IN_PID => :v_in_pid,
				OT_LIMIT_TYPE => :v_limit_type,
				OT_DAY_WARN_CNT => :v_day_warn_cnt,
				OT_DAY_LIMIT_CNT => :v_day_limit_cnt,
				OT_MON_WARN_CNT => :v_mon_warn_cnt,
				OT_MON_LIMIT_CNT => :v_mon_limit_cnt,
				OT_LIMIT_FLAG => :v_limit_flag,
				OT_DAY_ACC_CNT => :v_day_acc_cnt,
				OT_MON_ACC_CNT => :v_mon_acc_cnt,
				OT_RST => :v_rst,
				OT_RSTMSG => :v_rstmsg
			);
		END;
	END-EXEC;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::procGetLimitDef(class-based) ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		_logPrint(_DATALOG, tmpLog3);
		output.rst = -1;
		strncpy(output.rstmsg, sqlca.sqlerrm.sqlerrmc, sizeof(output.rstmsg) - 1);
		output.rstmsg[sizeof(output.rstmsg) - 1] = '\0';
		return -1;
	}
	
	strncpy(output.limit_type, v_limit_type, sizeof(output.limit_type) - 1);
	output.limit_type[sizeof(output.limit_type) - 1] = '\0';
	output.day_warn_cnt = v_day_warn_cnt;
	output.day_limit_cnt = v_day_limit_cnt;
	output.mon_warn_cnt = v_mon_warn_cnt;
	output.mon_limit_cnt = v_mon_limit_cnt;
	strncpy(output.limit_flag, v_limit_flag, sizeof(output.limit_flag) - 1);
	output.limit_flag[sizeof(output.limit_flag) - 1] = '\0';
	output.day_acc_cnt = v_day_acc_cnt;
	output.mon_acc_cnt = v_mon_acc_cnt;
	output.rst = v_rst;
	strncpy(output.rstmsg, v_rstmsg, sizeof(output.rstmsg) - 1);
	output.rstmsg[sizeof(output.rstmsg) - 1] = '\0';

	sprintf(tmpLog3, "CDatabaseORA::procGetLimitDef(class-based) SUCCESS - RST[%d]", v_rst);
	_logPrint(_DATALOG, tmpLog3);

	return 1;
}



}
