
/* Result Sets Interface */
#ifndef SQL_CRSR
#  define SQL_CRSR
  struct sql_cursor
  {
    unsigned int curocn;
    void *ptr1;
    void *ptr2;
    unsigned int magic;
  };
  typedef struct sql_cursor sql_cursor;
  typedef struct sql_cursor SQL_CURSOR;
#endif /* SQL_CRSR */

/* Thread Safety */
typedef void * sql_context;
typedef void * SQL_CONTEXT;

/* Object support */
struct sqltvn
{
  unsigned char *tvnvsn; 
  unsigned short tvnvsnl; 
  unsigned char *tvnnm;
  unsigned short tvnnml; 
  unsigned char *tvnsnm;
  unsigned short tvnsnml;
};
typedef struct sqltvn sqltvn;

struct sqladts
{
  unsigned int adtvsn; 
  unsigned short adtmode; 
  unsigned short adtnum;  
  sqltvn adttvn[1];       
};
typedef struct sqladts sqladts;

static struct sqladts sqladt = {
  1,0,0,
};

/* Binding to PL/SQL Records */
struct sqltdss
{
  unsigned int tdsvsn; 
  unsigned short tdsnum; 
  unsigned char *tdsval[1]; 
};
typedef struct sqltdss sqltdss;
static struct sqltdss sqltds =
{
  1,
  0,
};

/* File name & Package Name */
struct sqlcxp
{
  unsigned short fillen;
           char  filnam[75];
};
static const struct sqlcxp sqlfpn =
{
    74,
    "/home/<USER>/CLionProjects/ftalk_up/daemon_logon_ftk/build_occi/reportMMSDB.pc"
};


static unsigned int sqlctx = 71249909;


static struct sqlexd {
   unsigned long  sqlvsn;
   unsigned int   arrsiz;
   unsigned int   iters;
   unsigned int   offset;
   unsigned short selerr;
   unsigned short sqlety;
   unsigned int   occurs;
      const short *cud;
   unsigned char  *sqlest;
      const char  *stmt;
   sqladts *sqladtp;
   sqltdss *sqltdsp;
   unsigned char  **sqphsv;
   unsigned long  *sqphsl;
            int   *sqphss;
            short **sqpind;
            int   *sqpins;
   unsigned long  *sqparm;
   unsigned long  **sqparc;
   unsigned short  *sqpadto;
   unsigned short  *sqptdso;
   unsigned int   sqlcmax;
   unsigned int   sqlcmin;
   unsigned int   sqlcincr;
   unsigned int   sqlctimeout;
   unsigned int   sqlcnowait;
            int   sqfoff;
   unsigned int   sqcmod;
   unsigned int   sqfmod;
   unsigned int   sqlpfmem;
   unsigned char  *sqhstv[13];
   unsigned long  sqhstl[13];
            int   sqhsts[13];
            short *sqindv[13];
            int   sqinds[13];
   unsigned long  sqharm[13];
   unsigned long  *sqharc[13];
   unsigned short  sqadto[13];
   unsigned short  sqtdso[13];
} sqlstm = {13,13};

// Prototypes
extern "C" {
  void sqlcxt (void **, unsigned int *,
               struct sqlexd *, const struct sqlcxp *);
  void sqlcx2t(void **, unsigned int *,
               struct sqlexd *, const struct sqlcxp *);
  void sqlbuft(void **, char *);
  void sqlgs2t(void **, char *);
  void sqlorat(void **, unsigned int *, void *);
}

// Forms Interface
static const int IAPSUCC = 0;
static const int IAPFAIL = 1403;
static const int IAPFTL  = 535;
extern "C" { void sqliem(unsigned char *, signed int *); }

typedef struct { unsigned short len; unsigned char arr[1]; } VARCHAR;
typedef struct { unsigned short len; unsigned char arr[1]; } varchar;

/* cud (compilation unit data) array */
static const short sqlcud0[] =
{13,4242,1,0,0,
5,0,0,0,0,0,60,70,0,0,0,0,0,1,0,
20,0,0,0,0,0,58,74,0,0,1,1,0,1,0,3,109,0,0,
39,0,0,1,0,0,30,219,0,0,0,0,0,1,0,
54,0,0,0,0,0,59,220,0,0,1,1,0,1,0,3,109,0,0,
73,0,0,2,0,0,29,393,0,0,0,0,0,1,0,
88,0,0,3,0,0,31,405,0,0,0,0,0,1,0,
103,0,0,4,378,0,6,502,0,0,13,13,0,1,0,1,5,0,0,2,5,0,0,2,5,0,0,2,3,0,0,2,5,0,0,
2,5,0,0,2,5,0,0,2,5,0,0,2,5,0,0,2,5,0,0,2,3,0,0,2,3,0,0,2,5,0,0,
170,0,0,0,0,0,27,606,0,0,4,4,0,1,0,1,9,0,0,1,9,0,0,1,9,0,0,1,10,0,0,
201,0,0,6,0,0,30,639,0,0,0,0,0,1,0,
216,0,0,0,0,0,59,640,0,0,1,1,0,1,0,3,109,0,0,
235,0,0,0,0,0,58,647,0,0,1,1,0,1,0,3,109,0,0,
};


#include "reportDB.h"
#include <queue>
#include <list>
#include <semaphore.h> 

/* EXEC SQL BEGIN DECLARE SECTION; */ 

#define MAX_DB_CONNECTION 20
/* EXEC SQL END DECLARE SECTION; */ 


void Init_Oracle(sql_context ctx);
void* doService(void* param);
int getReportDB(sql_context pDB,struct sqlca sqlca, char* buff,CReportDbInfo& reportDbInfo);
int setReportDB(sql_context pDB,struct sqlca sqlca, char* buff,CReportDbInfo& reportDbInfo);
void* doDBError(void* param);
int errorDBprocess(void* pDB);
int offerInfo(CKSSocket& newSockfd);

queue<void*, list<void*> > dbConnQ;
std::list <CThreadInfo*> listThreadHandle;
typedef std::list <CThreadInfo*>::iterator listPosition;

sem_t m_sem;

int main(int argc, char* argv[])
{
	int ret;
	int i;
	int nThreadCount = 0;
	int hNewSocket;
	
	char logMsg[SOCKET_BUFF];
	char buff[SOCKET_BUFF];
	
	struct timeval outtime;

	CKSSocket svrSockfd;
	CKSSocket newSockfd;
	CKSThread ksthread;
	CThreadInfo* pThreadInfo;
	TypeMsgBindSnd* pLogonData;
	CKSConfig conf;

	listPosition pos;
	listPosition posPrev;

	/* EXEC SQL BEGIN DECLARE SECTION; */ 

		sql_context db[MAX_DB_CONNECTION];
	/* EXEC SQL END DECLARE SECTION; */ 


	if (ml_sub_init(PROCESS_NO, PROCESS_NAME,(char*)0,(char*)0)<0)
	{
		perror("ml_sub_init Error.");
		ml_sub_end();
		exit(1);
	}

	ret = configParse(argv[1]);
	if( ret != 0 )
	{
		exit(1);
	}

	log_history(0,0,"[INF] conf reportDBName - [%s]",gConf.reportDBName);

	sprintf(logMsg,"[INF] start [%s]",PROCESS_NAME);
	monitoring(logMsg,0,0);

	Init_Server();

	/* EXEC SQL ENABLE THREADS; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 0;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.stmt = "";
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )5;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}


	
	for(i=0;i<MAX_DB_CONNECTION;i++)
	{
		/* EXEC SQL CONTEXT ALLOCATE :db[i]; */ 

{
  struct sqlexd sqlstm;
  sqlstm.sqlvsn = 13;
  sqlstm.arrsiz = 1;
  sqlstm.sqladtp = &sqladt;
  sqlstm.sqltdsp = &sqltds;
  sqlstm.stmt = "";
  sqlstm.iters = (unsigned int  )1;
  sqlstm.offset = (unsigned int  )20;
  sqlstm.cud = sqlcud0;
  sqlstm.sqlest = (unsigned char  *)&sqlca;
  sqlstm.sqlety = (unsigned short)4352;
  sqlstm.occurs = (unsigned int  )0;
  sqlstm.sqhstv[0] = (unsigned char  *)&db[i];
  sqlstm.sqhstl[0] = (unsigned long )sizeof(void *);
  sqlstm.sqhsts[0] = (         int  )0;
  sqlstm.sqindv[0] = (         short *)0;
  sqlstm.sqinds[0] = (         int  )0;
  sqlstm.sqharm[0] = (unsigned long )0;
  sqlstm.sqadto[0] = (unsigned short )0;
  sqlstm.sqtdso[0] = (unsigned short )0;
  sqlstm.sqphsv = sqlstm.sqhstv;
  sqlstm.sqphsl = sqlstm.sqhstl;
  sqlstm.sqphss = sqlstm.sqhsts;
  sqlstm.sqpind = sqlstm.sqindv;
  sqlstm.sqpins = sqlstm.sqinds;
  sqlstm.sqparm = sqlstm.sqharm;
  sqlstm.sqparc = sqlstm.sqharc;
  sqlstm.sqpadto = sqlstm.sqadto;
  sqlstm.sqptdso = sqlstm.sqtdso;
  sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



		Init_Oracle(db[i]);
		if( sqlca.sqlcode !=0 )
		{
			monitoring("[ERR] db connect failed",0,errno);
			ml_sub_end();
			return -1;
		}

		log_history(0,0,"[INF] db sql_context - value [%x]",db[i]);
		dbConnQ.push(db[i]);
	}

	sem_init(&m_sem,0,1);

	ret = svrSockfd.createDomainNon(gConf.reportDBName);
	
	if( ret !=0 )
	{
		log_history(0,0,"[ERR] socket_domain create failed - conf.reportDBName[%s]",strerror(errno),gConf.reportDBName);
		goto END;
	}

    

	while(activeProcess)
	{
		wait_a_moment(1000);
		pos = listThreadHandle.begin();
		nThreadCount = 0;
		while( pos != listThreadHandle.end() )
		{
			nThreadCount++;
			posPrev = pos++;

			if( (*posPrev)->tid <= 0 )
			{
				log_history(0,0,"[INF] thread tid - [%d]",(*posPrev)->tid);
				listThreadHandle.erase(posPrev);
				continue;
			}

			ret =  pthread_kill((*posPrev)->tid,0);

			switch (ret)
			{
				case 0 : 
					break;
				case ESRCH:
					pthread_join((*posPrev)->tid,NULL);
					listThreadHandle.erase(posPrev);
					delete (CThreadInfo*)(*posPrev);
					break;
				case EINVAL:
					pthread_join((*posPrev)->tid,NULL);
					listThreadHandle.erase(posPrev);
					delete (CThreadInfo*)(*posPrev);
					break;
				default:
					log_history(0,0,"[ERR] thread check failed - ret[%d]errno[%s]",ret,strerror(errno));
					pthread_join((*posPrev)->tid,NULL);
					listThreadHandle.erase(posPrev);
					delete (CThreadInfo*)(*posPrev);
					break;
				}
		}


		hNewSocket = svrSockfd.accept();
		if( hNewSocket <= 0 )
		{
			continue;
		}
		/* new connection  */
		pThreadInfo = NULL;
		pThreadInfo = new CThreadInfo;
		if( pThreadInfo == NULL )
		{
			log_history(0,0,"[ERR] thread threadInfo object create failed - errno[%s]",strerror(errno));
			close(hNewSocket);
			continue;
		}

		pThreadInfo->sock = hNewSocket;
		
		ret = ksthread.create(&(pThreadInfo->tid),NULL,doService,(void*)pThreadInfo);
		if( ret != 0 )
		{
			log_history(0,0,"[ERR] thread create failed - errno[%s]",strerror(errno));
			close(pThreadInfo->sock);
			delete pThreadInfo;
			continue;
		}
		listThreadHandle.push_back( pThreadInfo);

	}

END:
	svrSockfd.close();
	pos = listThreadHandle.begin();
	nThreadCount = 0;
	while( pos != listThreadHandle.end() )
	{
		nThreadCount++;
		posPrev = pos++;
		ret =  pthread_kill((*posPrev)->tid,0);

		switch (ret) {
			case 0 : /* thread is alive */     
				log_history(0,0,"[INF] thread closing - listThreadHandle[%d]",(pthread_t)(*posPrev));
				pthread_join((*posPrev)->tid,NULL);
				listThreadHandle.erase(posPrev);
				delete (CThreadInfo*)(*posPrev);
				break;
			case ESRCH:
			case EINVAL:
				/* close thread */
				pthread_join((*posPrev)->tid,NULL);
				listThreadHandle.erase(posPrev);
				delete (CThreadInfo*)(*posPrev);
				break;
			default:
				pthread_join((*posPrev)->tid,NULL);
				listThreadHandle.erase(posPrev);
				delete (CThreadInfo*)(*posPrev);
				break;
		}
	}

	sem_destroy(&m_sem);
	/* EXEC SQL BEGIN DECLARE SECTION; */ 

		sql_context pDB;
	/* EXEC SQL END DECLARE SECTION; */ 


	sleep(2);//?
	while( dbConnQ.size()>0 )
	{
		pDB = dbConnQ.front();
		if( pDB == NULL )
		{
			break;
		}

		/* EXEC SQL CONTEXT USE :pDB; */ 

		/* EXEC SQL COMMIT WORK RELEASE; */ 

{
  struct sqlexd sqlstm;
  sqlstm.sqlvsn = 13;
  sqlstm.arrsiz = 1;
  sqlstm.sqladtp = &sqladt;
  sqlstm.sqltdsp = &sqltds;
  sqlstm.iters = (unsigned int  )1;
  sqlstm.offset = (unsigned int  )39;
  sqlstm.cud = sqlcud0;
  sqlstm.sqlest = (unsigned char  *)&sqlca;
  sqlstm.sqlety = (unsigned short)4352;
  sqlstm.occurs = (unsigned int  )0;
  sqlcxt(&pDB, &sqlctx, &sqlstm, &sqlfpn);
}


		/* EXEC SQL CONTEXT FREE :pDB; */ 

{
  struct sqlexd sqlstm;
  sqlstm.sqlvsn = 13;
  sqlstm.arrsiz = 1;
  sqlstm.sqladtp = &sqladt;
  sqlstm.sqltdsp = &sqltds;
  sqlstm.stmt = "";
  sqlstm.iters = (unsigned int  )1;
  sqlstm.offset = (unsigned int  )54;
  sqlstm.cud = sqlcud0;
  sqlstm.sqlest = (unsigned char  *)&sqlca;
  sqlstm.sqlety = (unsigned short)4352;
  sqlstm.occurs = (unsigned int  )0;
  sqlstm.sqhstv[0] = (unsigned char  *)&pDB;
  sqlstm.sqhstl[0] = (unsigned long )sizeof(void *);
  sqlstm.sqhsts[0] = (         int  )0;
  sqlstm.sqindv[0] = (         short *)0;
  sqlstm.sqinds[0] = (         int  )0;
  sqlstm.sqharm[0] = (unsigned long )0;
  sqlstm.sqadto[0] = (unsigned short )0;
  sqlstm.sqtdso[0] = (unsigned short )0;
  sqlstm.sqphsv = sqlstm.sqhstv;
  sqlstm.sqphsl = sqlstm.sqhstl;
  sqlstm.sqphss = sqlstm.sqhsts;
  sqlstm.sqpind = sqlstm.sqindv;
  sqlstm.sqpins = sqlstm.sqinds;
  sqlstm.sqparm = sqlstm.sqharm;
  sqlstm.sqparc = sqlstm.sqharc;
  sqlstm.sqpadto = sqlstm.sqadto;
  sqlstm.sqptdso = sqlstm.sqtdso;
  sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



		dbConnQ.pop();
	}
    
	sprintf(logMsg,"[INF] close [%s]",PROCESS_NAME);
	monitoring(logMsg,0,0);
	ml_sub_end();

	return 0;
}


void* doService(void* param)
{
	int ret;
	CKSSocket newSockfd;
	char buff[SOCKET_BUFF];
	CThreadInfo* info = (CThreadInfo*)param;
	CReportDbInfo reportDbInfo;
	CReportDbInfo* pReportDbInfo;

	void* pDB;
	struct sqlca sqlca;

	newSockfd.attach(info->sock);
	memset(buff	,0x00	,sizeof(buff));//CCL(buff);

	ret = newSockfd.rcvmsg(buff);
	if( ret == 0 ) 
	{
		newSockfd.close();
		log_history(0,0,"[ERR] socket_domain reportDB - time out [3]sec");
		
		return NULL;
	}
	if( ret < 0 ) 
	{
		newSockfd.close();
		log_history(0,0,"[ERR] socket_domain reportDB - recv failed ");
        
		return NULL;
	}
	if( memcmp(buff,"getInfo",7) == 0 )
	{
		offerInfo(newSockfd);
		newSockfd.close();
		
		return NULL;
	}

/*    log_history(0,0,"doService ... start");
 */
REGETDB:
	while( sem_wait(&m_sem) == -1 )
	{
		if(errno != EINTR )
		{ 
			newSockfd.close();
			log_history(0,0,"[ERR] semaphore wait failed");
			
			return NULL;
		}
	}

	if( dbConnQ.size() > 0 )
	{
		pDB = dbConnQ.front();
		if( pDB != NULL ) 
		{
			//log_history(0,0,"pop NOT NULL pDB[%x] [%d]", pDB,dbConnQ.size()); 
			dbConnQ.pop();
		}
	}
	else
	{
		pDB = NULL;
	}

	if( sem_post(&m_sem) == -1 )
	{
		newSockfd.close();
		log_history(0,0,"[ERR] semaphore clear failed - [%s]",strerror(errno));
		
		return NULL;
	}

	if( pDB == NULL )
	{
		ret = 0;
		log_history(0,0,"NODATA[%s]",strerror(errno));
		goto NODATA;
	}

	memset(&reportDbInfo	,0x00	,sizeof(reportDbInfo));

	alarm(15);
	pReportDbInfo = (CReportDbInfo*)buff;
	
	switch(atoi(pReportDbInfo->header.msgType))
	{
		case 1: /* 전송결과 요청 */
			ret = getReportDB(pDB,sqlca,buff,reportDbInfo);
			if( ret < 0 )
			{
				newSockfd.close();
				log_history(0,0,"getReportDB Error");
				ret = errorDBprocess(pDB);
				alarm(0);
			
				return NULL;
			}

			break;
		case 3: /* 레포트 재입력 */
			/*
			ret = setReportDB(pDB,sqlca,buff,reportDbInfo);
			if( ret < 0 )
			{
				newSockfd.close();
				log_history(0,0,"setReportDB Error");
				ret = errorDBprocess(pDB);
				alarm(0);
				
				return NULL;
			}
			log_history(0,0,"setReportDB Succ");
			*/
			break;
		default:
			ret = -1;
			log_history(0,0,"[ERR] packet msg type failed - reportDbInfo header msgType[%s]",pReportDbInfo->header.msgType);
			break;
	}
	alarm(0);

	if( ret < 0 )
	{
		newSockfd.close();
		while( sem_wait(&m_sem) == -1 )
		{
			if(errno != EINTR )
			{ 
				newSockfd.close();
				log_history(0,0,"[ERR] semaphore wait failed");

				return NULL;
			}
		}
		dbConnQ.push(pDB);

		if( sem_post(&m_sem) == -1 )
		{
			newSockfd.close();
			log_history(0,0,"[ERR] semaphore clear failed - errno[%s]",strerror(errno));
			
			return NULL;
		}
		return NULL;
	}

	if( ret == 0 )
	{
		//memcpy(reportDbInfo.szResCode,"99",2);
		strcpy(reportDbInfo.szResCode,"99");
	}

	ret = newSockfd.send((char*)&reportDbInfo,sizeof(reportDbInfo));
	
	if( ret == sizeof(reportDbInfo))
	{
		/* commit */
		/* EXEC SQL CONTEXT USE :pDB; */ 

		/* EXEC SQL COMMIT; */ 

{
  struct sqlexd sqlstm;
  sqlstm.sqlvsn = 13;
  sqlstm.arrsiz = 1;
  sqlstm.sqladtp = &sqladt;
  sqlstm.sqltdsp = &sqltds;
  sqlstm.iters = (unsigned int  )1;
  sqlstm.offset = (unsigned int  )73;
  sqlstm.cud = sqlcud0;
  sqlstm.sqlest = (unsigned char  *)&sqlca;
  sqlstm.sqlety = (unsigned short)4352;
  sqlstm.occurs = (unsigned int  )0;
  sqlcxt(&pDB, &sqlctx, &sqlstm, &sqlfpn);
}


	}
	else 
	{
		log_history(0,0,"[ERR] socket_domian send failed - ret[%d]errno[%s]rescode[%S]",ret,strerror(errno),reportDbInfo.szResCode);
		
		//if( memcmp(reportDbInfo.szResCode,"99",2) != 0 &&  memcmp(reportDbInfo.szResCode,"98",2) != 0 )
		if( memcmp(reportDbInfo.szResCode,"99", strlen(reportDbInfo.szResCode)) != 0 &&  
			memcmp(reportDbInfo.szResCode,"98", strlen(reportDbInfo.szResCode)) != 0 )
		{
			/* rollback */
			/* EXEC SQL CONTEXT USE :pDB; */ 

			/* EXEC SQL ROLLBACK; */ 

{
   struct sqlexd sqlstm;
   sqlstm.sqlvsn = 13;
   sqlstm.arrsiz = 1;
   sqlstm.sqladtp = &sqladt;
   sqlstm.sqltdsp = &sqltds;
   sqlstm.iters = (unsigned int  )1;
   sqlstm.offset = (unsigned int  )88;
   sqlstm.cud = sqlcud0;
   sqlstm.sqlest = (unsigned char  *)&sqlca;
   sqlstm.sqlety = (unsigned short)4352;
   sqlstm.occurs = (unsigned int  )0;
   sqlcxt(&pDB, &sqlctx, &sqlstm, &sqlfpn);
}


			log_history(0,0,"[INF] db rollback msgid[%lld]rescode[%s]",reportDbInfo.nMsgId,reportDbInfo.szResCode);
		}
	}


	while( sem_wait(&m_sem) == -1 )
	{
		if(errno != EINTR )
		{ 
			newSockfd.close();
			log_history(0,0,"[ERR] semaphore wait failed");
			
			return NULL;
		}
	}

	dbConnQ.push(pDB);
	
	if( sem_post(&m_sem) == -1 )
	{
		newSockfd.close();
		log_history(0,0,"[ERR] semaphore clear failed - errno[%s]",strerror(errno));
		
		return NULL;
	}

	newSockfd.close();

	return NULL;

NODATA:
	if( ret == 0 )
	{
		//memcpy(reportDbInfo.szResCode,"99",2);
		strcpy(reportDbInfo.szResCode,"99");
	}
	
	ret = newSockfd.send((char*)&reportDbInfo,sizeof(reportDbInfo));
	if( ret != sizeof(reportDbInfo))
	{
		log_history(0,0,"[ERR] socket_domain send NODATA - ret[%d]errno[%s]",ret,strerror(errno));
	}

	newSockfd.close();

	return NULL;
}

int getReportDB(sql_context pDB,struct sqlca sqlca, char* buff ,CReportDbInfo& reportDbInfo)
{
	/* EXEC SQL BEGIN DECLARE SECTION; */ 


		char szPtnsn[16+1];
		char szResCode[16+1];
		int  nTelcoId =-1;
		char szResvData[512];
		char szEndTelco[8];
		long long  nMsgId=-1;
		char szMsgId[14+1];
		char szRptDate[16];
		int  nJobCode=-1;
		int  nSqlCode=-1;
		
		int  nCnt = -1;
		char szSqlErrorMsg[1024];
			
		char szAppName[16];
		char szSerial[16+1];
		char szDstadr[16+1];
		char szCid[12+1]; /*  ptnid&jobcode 대체 */
		
		int nPtnId = -1;

	/* EXEC SQL END DECLARE SECTION; */ 


	CReportDbInfo* pReportDbInfo = (CReportDbInfo*)buff;

	memset(szPtnsn       ,0x00, sizeof(szPtnsn      ));   //CCL(szPtnsn);      
	memset(szResCode     ,0x00, sizeof(szResCode    ));   //CCL(szResCode);    
	memset(szResvData    ,0x00, sizeof(szResvData   ));   //CCL(szResvData);   
	memset(szDstadr      ,0x00, sizeof(szDstadr     ));   //CCL(szDstadr);     
	memset(szEndTelco    ,0x00, sizeof(szEndTelco   ));   //CCL(szEndTelco);   
	memset(szRptDate     ,0x00, sizeof(szRptDate    ));   //CCL(szRptDate);    
	memset(szSqlErrorMsg ,0x00, sizeof(szSqlErrorMsg));   //CCL(szSqlErrorMsg);
	memset(szAppName     ,0x00, sizeof(szAppName    ));   //CCL(szAppName);    
	memset(szCid         ,0x00, sizeof(szCid        ));   //CCL(szCid);        
    memset(szMsgId       ,0x00, sizeof(szMsgId      ));

	strcpy(szAppName,pReportDbInfo->szResvData);
	memcpy(szResCode,"Init",4);
    
#ifdef DEBUG
	log_history(0,0,"appname[%s][%x]",szAppName,pDB);
#endif

	/* EXEC SQL CONTEXT USE :pDB; */ 

	/* EXEC SQL EXECUTE
		BEGIN
			proc_get_mms_report(in_appname=>:szAppName
                					,ot_ptn_sn=>:szPtnsn
                					,ot_res_code=>:szResCode
                					,ot_telco_id=>:nTelcoId
                					,ot_end_telco=>:szEndTelco
                					,ot_msg_id=>:szMsgId
                					,ot_resv_data=>:szResvData
                					,ot_dstaddr=>:szDstadr
                					,ot_rpt_telco_date=>:szRptDate
                					,ot_cid=>:szCid 
                					,ot_cnt=>:nCnt
                					,ot_sqlcode=>:nSqlCode
                					,ot_sqlmsg=>:szSqlErrorMsg
                					); 
		END;
	END-EXEC; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 13;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.stmt = "begin proc_get_mms_report ( in_appname => :szAppName , ot_pt\
n_sn => :szPtnsn , ot_res_code => :szResCode , ot_telco_id => :nTelcoId , ot_e\
nd_telco => :szEndTelco , ot_msg_id => :szMsgId , ot_resv_data => :szResvData \
, ot_dstaddr => :szDstadr , ot_rpt_telco_date => :szRptDate , ot_cid => :szCid\
 , ot_cnt => :nCnt , ot_sqlcode => :nSqlCode , ot_sqlmsg => :szSqlErrorMsg ) ;\
 END ;";
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )103;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlstm.sqhstv[0] = (unsigned char  *)szAppName;
 sqlstm.sqhstl[0] = (unsigned long )16;
 sqlstm.sqhsts[0] = (         int  )0;
 sqlstm.sqindv[0] = (         short *)0;
 sqlstm.sqinds[0] = (         int  )0;
 sqlstm.sqharm[0] = (unsigned long )0;
 sqlstm.sqadto[0] = (unsigned short )0;
 sqlstm.sqtdso[0] = (unsigned short )0;
 sqlstm.sqhstv[1] = (unsigned char  *)szPtnsn;
 sqlstm.sqhstl[1] = (unsigned long )17;
 sqlstm.sqhsts[1] = (         int  )0;
 sqlstm.sqindv[1] = (         short *)0;
 sqlstm.sqinds[1] = (         int  )0;
 sqlstm.sqharm[1] = (unsigned long )0;
 sqlstm.sqadto[1] = (unsigned short )0;
 sqlstm.sqtdso[1] = (unsigned short )0;
 sqlstm.sqhstv[2] = (unsigned char  *)szResCode;
 sqlstm.sqhstl[2] = (unsigned long )17;
 sqlstm.sqhsts[2] = (         int  )0;
 sqlstm.sqindv[2] = (         short *)0;
 sqlstm.sqinds[2] = (         int  )0;
 sqlstm.sqharm[2] = (unsigned long )0;
 sqlstm.sqadto[2] = (unsigned short )0;
 sqlstm.sqtdso[2] = (unsigned short )0;
 sqlstm.sqhstv[3] = (unsigned char  *)&nTelcoId;
 sqlstm.sqhstl[3] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[3] = (         int  )0;
 sqlstm.sqindv[3] = (         short *)0;
 sqlstm.sqinds[3] = (         int  )0;
 sqlstm.sqharm[3] = (unsigned long )0;
 sqlstm.sqadto[3] = (unsigned short )0;
 sqlstm.sqtdso[3] = (unsigned short )0;
 sqlstm.sqhstv[4] = (unsigned char  *)szEndTelco;
 sqlstm.sqhstl[4] = (unsigned long )8;
 sqlstm.sqhsts[4] = (         int  )0;
 sqlstm.sqindv[4] = (         short *)0;
 sqlstm.sqinds[4] = (         int  )0;
 sqlstm.sqharm[4] = (unsigned long )0;
 sqlstm.sqadto[4] = (unsigned short )0;
 sqlstm.sqtdso[4] = (unsigned short )0;
 sqlstm.sqhstv[5] = (unsigned char  *)szMsgId;
 sqlstm.sqhstl[5] = (unsigned long )15;
 sqlstm.sqhsts[5] = (         int  )0;
 sqlstm.sqindv[5] = (         short *)0;
 sqlstm.sqinds[5] = (         int  )0;
 sqlstm.sqharm[5] = (unsigned long )0;
 sqlstm.sqadto[5] = (unsigned short )0;
 sqlstm.sqtdso[5] = (unsigned short )0;
 sqlstm.sqhstv[6] = (unsigned char  *)szResvData;
 sqlstm.sqhstl[6] = (unsigned long )512;
 sqlstm.sqhsts[6] = (         int  )0;
 sqlstm.sqindv[6] = (         short *)0;
 sqlstm.sqinds[6] = (         int  )0;
 sqlstm.sqharm[6] = (unsigned long )0;
 sqlstm.sqadto[6] = (unsigned short )0;
 sqlstm.sqtdso[6] = (unsigned short )0;
 sqlstm.sqhstv[7] = (unsigned char  *)szDstadr;
 sqlstm.sqhstl[7] = (unsigned long )17;
 sqlstm.sqhsts[7] = (         int  )0;
 sqlstm.sqindv[7] = (         short *)0;
 sqlstm.sqinds[7] = (         int  )0;
 sqlstm.sqharm[7] = (unsigned long )0;
 sqlstm.sqadto[7] = (unsigned short )0;
 sqlstm.sqtdso[7] = (unsigned short )0;
 sqlstm.sqhstv[8] = (unsigned char  *)szRptDate;
 sqlstm.sqhstl[8] = (unsigned long )16;
 sqlstm.sqhsts[8] = (         int  )0;
 sqlstm.sqindv[8] = (         short *)0;
 sqlstm.sqinds[8] = (         int  )0;
 sqlstm.sqharm[8] = (unsigned long )0;
 sqlstm.sqadto[8] = (unsigned short )0;
 sqlstm.sqtdso[8] = (unsigned short )0;
 sqlstm.sqhstv[9] = (unsigned char  *)szCid;
 sqlstm.sqhstl[9] = (unsigned long )13;
 sqlstm.sqhsts[9] = (         int  )0;
 sqlstm.sqindv[9] = (         short *)0;
 sqlstm.sqinds[9] = (         int  )0;
 sqlstm.sqharm[9] = (unsigned long )0;
 sqlstm.sqadto[9] = (unsigned short )0;
 sqlstm.sqtdso[9] = (unsigned short )0;
 sqlstm.sqhstv[10] = (unsigned char  *)&nCnt;
 sqlstm.sqhstl[10] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[10] = (         int  )0;
 sqlstm.sqindv[10] = (         short *)0;
 sqlstm.sqinds[10] = (         int  )0;
 sqlstm.sqharm[10] = (unsigned long )0;
 sqlstm.sqadto[10] = (unsigned short )0;
 sqlstm.sqtdso[10] = (unsigned short )0;
 sqlstm.sqhstv[11] = (unsigned char  *)&nSqlCode;
 sqlstm.sqhstl[11] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[11] = (         int  )0;
 sqlstm.sqindv[11] = (         short *)0;
 sqlstm.sqinds[11] = (         int  )0;
 sqlstm.sqharm[11] = (unsigned long )0;
 sqlstm.sqadto[11] = (unsigned short )0;
 sqlstm.sqtdso[11] = (unsigned short )0;
 sqlstm.sqhstv[12] = (unsigned char  *)szSqlErrorMsg;
 sqlstm.sqhstl[12] = (unsigned long )1024;
 sqlstm.sqhsts[12] = (         int  )0;
 sqlstm.sqindv[12] = (         short *)0;
 sqlstm.sqinds[12] = (         int  )0;
 sqlstm.sqharm[12] = (unsigned long )0;
 sqlstm.sqadto[12] = (unsigned short )0;
 sqlstm.sqtdso[12] = (unsigned short )0;
 sqlstm.sqphsv = sqlstm.sqhstv;
 sqlstm.sqphsl = sqlstm.sqhstl;
 sqlstm.sqphss = sqlstm.sqhsts;
 sqlstm.sqpind = sqlstm.sqindv;
 sqlstm.sqpins = sqlstm.sqinds;
 sqlstm.sqparm = sqlstm.sqharm;
 sqlstm.sqparc = sqlstm.sqharc;
 sqlstm.sqpadto = sqlstm.sqadto;
 sqlstm.sqptdso = sqlstm.sqtdso;
 sqlcxt(&pDB, &sqlctx, &sqlstm, &sqlfpn);
}


/*              ot_ptn_id=>:nPtnId,
                ot_job_code=>:nJobCode,
*/

	if( memcmp(szResCode,"Init",4) == 0 )
	{
		log_history(0,0,"[ERR] db call proc_get_report_time - CID[%s]ptnSn[%s]sqlcode[%d] sqlmsg[%s][%s]"
                ,szCid
                ,szPtnsn
                ,sqlca.sqlcode
                ,trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc))
                ,szAppName
                );
		return -1;
	}



	/* 
	 * NO DATA FOUND 일 경우 SKIP
	 * szResCode '999'일경우도 있어 수정
	 * szResCode에 space 포함되어있어 trim 위치 올림
	 */
	trim(szResCode	,sizeof(szResCode)	);// LSY 20131115 결과코드값 trim 추가
	//if( memcmp(szResCode,"99",2) == 0 || memcmp(szResCode,"98",2) == 0 )
	if( memcmp(szResCode,"99", strlen(szResCode)) == 0 || memcmp(szResCode,"98", strlen(szResCode)) == 0 )
	{
		return 0;
	}
	
	trim(szPtnsn	,sizeof(szPtnsn)	);
	trim(szResvData	,sizeof(szResvData)	);
	trim(szDstadr	,sizeof(szDstadr)	);
	trim(szEndTelco	,sizeof(szEndTelco)	);
	trim(szRptDate	,sizeof(szRptDate)	);
	
	strcpy(reportDbInfo.header.msgType,"2");

	memcpy(reportDbInfo.szPtnsn		,szPtnsn		,strlen(szPtnsn));
	memcpy(reportDbInfo.szResCode	,szResCode	,strlen(szResCode));
	memcpy(reportDbInfo.szAppName	,szAppName	,strlen(szAppName));
	
	reportDbInfo.nTelcoId 	= nTelcoId;
	
	memcpy(reportDbInfo.szResvData	,szResvData	,strlen(szResvData));
	memcpy(reportDbInfo.szDstAdr	,szDstadr	,strlen(szDstadr));
	memcpy(reportDbInfo.szEndTelco	,szEndTelco	,strlen(szEndTelco));
	
	reportDbInfo.nMsgId 	= atoll(szMsgId);
	
	memcpy(reportDbInfo.szRptDate	,szRptDate	,strlen(szRptDate));
	
	reportDbInfo.nCnt 		= nCnt;
	reportDbInfo.nSqlCode 	= nSqlCode;
	
	memcpy(reportDbInfo.szCid		,szCid		,strlen(szCid));
	
	/*
	reportDbInfo.nJobCode = nJobCode;
	reportDbInfo.nPtnId = nPtnId;
	*/
	
	return 1;
}



void Init_Oracle(sql_context ctx)
{
	/* EXEC SQL BEGIN DECLARE SECTION; */ 


		/* VARCHAR Username[10]; */ 
struct { unsigned short len; unsigned char arr[10]; } Username;

		/* VARCHAR Password[10]; */ 
struct { unsigned short len; unsigned char arr[10]; } Password;

		/* VARCHAR dbstring[10]; */ 
struct { unsigned short len; unsigned char arr[10]; } dbstring;


	/* EXEC SQL END DECLARE SECTION; */ 

	
	strcpy((char*)Username.arr	,gConf.dbID);
	strcpy((char*)Password.arr	,gConf.dbPASS);
	strcpy((char*)dbstring.arr	,gConf.dbSID);
	
	Username.len = strlen((char*)Username.arr);
	Password.len = strlen((char*)Password.arr);
	dbstring.len = strlen((char*)dbstring.arr);
	
	/* EXEC SQL CONTEXT USE :ctx; */ 

	/* EXEC SQL CONNECT :Username IDENTIFIED BY :Password USING :dbstring ; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 13;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.iters = (unsigned int  )10;
 sqlstm.offset = (unsigned int  )170;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlstm.sqhstv[0] = (unsigned char  *)&Username;
 sqlstm.sqhstl[0] = (unsigned long )12;
 sqlstm.sqhsts[0] = (         int  )12;
 sqlstm.sqindv[0] = (         short *)0;
 sqlstm.sqinds[0] = (         int  )0;
 sqlstm.sqharm[0] = (unsigned long )0;
 sqlstm.sqadto[0] = (unsigned short )0;
 sqlstm.sqtdso[0] = (unsigned short )0;
 sqlstm.sqhstv[1] = (unsigned char  *)&Password;
 sqlstm.sqhstl[1] = (unsigned long )12;
 sqlstm.sqhsts[1] = (         int  )12;
 sqlstm.sqindv[1] = (         short *)0;
 sqlstm.sqinds[1] = (         int  )0;
 sqlstm.sqharm[1] = (unsigned long )0;
 sqlstm.sqadto[1] = (unsigned short )0;
 sqlstm.sqtdso[1] = (unsigned short )0;
 sqlstm.sqhstv[2] = (unsigned char  *)&dbstring;
 sqlstm.sqhstl[2] = (unsigned long )12;
 sqlstm.sqhsts[2] = (         int  )12;
 sqlstm.sqindv[2] = (         short *)0;
 sqlstm.sqinds[2] = (         int  )0;
 sqlstm.sqharm[2] = (unsigned long )0;
 sqlstm.sqadto[2] = (unsigned short )0;
 sqlstm.sqtdso[2] = (unsigned short )0;
 sqlstm.sqphsv = sqlstm.sqhstv;
 sqlstm.sqphsl = sqlstm.sqhstl;
 sqlstm.sqphss = sqlstm.sqhsts;
 sqlstm.sqpind = sqlstm.sqindv;
 sqlstm.sqpins = sqlstm.sqinds;
 sqlstm.sqparm = sqlstm.sqharm;
 sqlstm.sqparc = sqlstm.sqharc;
 sqlstm.sqpadto = sqlstm.sqadto;
 sqlstm.sqptdso = sqlstm.sqtdso;
 sqlstm.sqlcmax = (unsigned int )100;
 sqlstm.sqlcmin = (unsigned int )2;
 sqlstm.sqlcincr = (unsigned int )1;
 sqlstm.sqlctimeout = (unsigned int )3;
 sqlstm.sqlcnowait = (unsigned int )0;
 sqlcxt(&ctx, &sqlctx, &sqlstm, &sqlfpn);
}


}


int errorDBprocess(void* pDB)
{
	CKSThread dbErr;
	int ret;
	pthread_t tid;
	
	ret = dbErr.create(&(tid),NULL,doDBError,(void*)pDB);
	if( ret != 0 )
	{
	    log_history(0,0,"[ERR] thread errorDBprocess create failed - errno[%s]dbQueSize[%d]",
	            strerror(errno),
	            dbConnQ.size());
	}
	
	return -1;
}


void* doDBError(void* param)
{

	pthread_detach(pthread_self()); 
	struct sqlca sqlca;
	/* EXEC SQL BEGIN DECLARE SECTION; */ 

		sql_context pDB = (sql_context)param;
	/* EXEC SQL END DECLARE SECTION; */ 


DBERRCONN:
	/* EXEC SQL CONTEXT USE :pDB; */ 

	/* EXEC SQL COMMIT WORK RELEASE; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 13;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )201;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlcxt(&pDB, &sqlctx, &sqlstm, &sqlfpn);
}


	/* EXEC SQL CONTEXT FREE :pDB; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 13;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.stmt = "";
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )216;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlstm.sqhstv[0] = (unsigned char  *)&pDB;
 sqlstm.sqhstl[0] = (unsigned long )sizeof(void *);
 sqlstm.sqhsts[0] = (         int  )0;
 sqlstm.sqindv[0] = (         short *)0;
 sqlstm.sqinds[0] = (         int  )0;
 sqlstm.sqharm[0] = (unsigned long )0;
 sqlstm.sqadto[0] = (unsigned short )0;
 sqlstm.sqtdso[0] = (unsigned short )0;
 sqlstm.sqphsv = sqlstm.sqhstv;
 sqlstm.sqphsl = sqlstm.sqhstl;
 sqlstm.sqphss = sqlstm.sqhsts;
 sqlstm.sqpind = sqlstm.sqindv;
 sqlstm.sqpins = sqlstm.sqinds;
 sqlstm.sqparm = sqlstm.sqharm;
 sqlstm.sqparc = sqlstm.sqharc;
 sqlstm.sqpadto = sqlstm.sqadto;
 sqlstm.sqptdso = sqlstm.sqtdso;
 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



	if( activeProcess == false )
	{
		return NULL;
	}
	
	/* EXEC SQL CONTEXT ALLOCATE :pDB; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 13;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.stmt = "";
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )235;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlstm.sqhstv[0] = (unsigned char  *)&pDB;
 sqlstm.sqhstl[0] = (unsigned long )sizeof(void *);
 sqlstm.sqhsts[0] = (         int  )0;
 sqlstm.sqindv[0] = (         short *)0;
 sqlstm.sqinds[0] = (         int  )0;
 sqlstm.sqharm[0] = (unsigned long )0;
 sqlstm.sqadto[0] = (unsigned short )0;
 sqlstm.sqtdso[0] = (unsigned short )0;
 sqlstm.sqphsv = sqlstm.sqhstv;
 sqlstm.sqphsl = sqlstm.sqhstl;
 sqlstm.sqphss = sqlstm.sqhsts;
 sqlstm.sqpind = sqlstm.sqindv;
 sqlstm.sqpins = sqlstm.sqinds;
 sqlstm.sqparm = sqlstm.sqharm;
 sqlstm.sqparc = sqlstm.sqharc;
 sqlstm.sqpadto = sqlstm.sqadto;
 sqlstm.sqptdso = sqlstm.sqtdso;
 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}


	
	Init_Oracle(pDB);
	
	if( sqlca.sqlcode !=0 )
	{
		monitoring("[ERR] db connection failed",0,errno);
		wait_a_moment(900000);
		goto DBERRCONN;
	}
	
	log_history(0,0,"[INF] db object create - sql_context[%x]",pDB);
	while( sem_wait(&m_sem) == -1 )
	{
		if(errno != EINTR )
		{ 
			log_history(0,0,"[ERR] semaphore wait failed");
			wait_a_moment(900000);
			goto DBERRCONN;
		}
	}
	/* 크리티컬 섹션 */
	dbConnQ.push(pDB);
	
	if( sem_post(&m_sem) == -1 )
	{
		log_history(0,0,"[ERR] semaphore clear failed - errno[%s]pdb[%x]",strerror(errno),pDB);
		return NULL;
	}
	
	log_history(0,0,"[INF] db doDBError - queueSize[%d]",dbConnQ.size());
	
	return NULL;
}

int offerInfo(CKSSocket& newSockfd)
{
	char szQueSize[4];
	memset(szQueSize	,0x00	,sizeof(szQueSize));//CCL(szQueSize);
	sprintf(szQueSize	,"%d"	,dbConnQ.size());

	newSockfd.send(szQueSize,strlen(szQueSize));
	
	return 0;
}

int configParse(char* file)
{
	Q_Entry *pEntry;
	int  i;
	int  nRetFlag = TRUE;
	char *pszTmp;
	CKSConfig conf;
	//memset(&gConf,0x00,sizeof(gConf));

	// read mert conf
	if((pEntry = conf.qfDecoder(file)) == NULL) 
	{
		printf("WARNING: Configuration file(%s) not found.\n", file);
		return -1;
	}

	conf.strncpy2(gConf.reportDBName	,conf.FetchEntry("domain.reportDB")	,64);
	if( gConf.reportDBName == NULL )
	{
		strcpy(gConf.reportDBName,"");
	}

	conf.strncpy2(gConf.dbID			,conf.FetchEntry("db.id")			,16);
	if( gConf.dbID == NULL )
	{
		strcpy(gConf.dbID,"");
	}

	conf.strncpy2(gConf.dbPASS			,conf.FetchEntry("db.pass")			,16);
	if( gConf.dbPASS == NULL )
	{
		strcpy(gConf.dbPASS,"");
	}

	conf.strncpy2(gConf.dbSID			,conf.FetchEntry("db.sid")			,16);
	if( gConf.dbSID == NULL )
	{
		strcpy(gConf.dbSID,"");
	}
    return 0;
}
