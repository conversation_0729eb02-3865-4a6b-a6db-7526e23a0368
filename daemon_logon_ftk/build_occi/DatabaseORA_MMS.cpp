
/* Result Sets Interface */
#ifndef SQL_CRSR
#  define SQL_CRSR
  struct sql_cursor
  {
    unsigned int curocn;
    void *ptr1;
    void *ptr2;
    unsigned int magic;
  };
  typedef struct sql_cursor sql_cursor;
  typedef struct sql_cursor SQL_CURSOR;
#endif /* SQL_CRSR */

/* Thread Safety */
typedef void * sql_context;
typedef void * SQL_CONTEXT;

/* Object support */
struct sqltvn
{
  unsigned char *tvnvsn; 
  unsigned short tvnvsnl; 
  unsigned char *tvnnm;
  unsigned short tvnnml; 
  unsigned char *tvnsnm;
  unsigned short tvnsnml;
};
typedef struct sqltvn sqltvn;

struct sqladts
{
  unsigned int adtvsn; 
  unsigned short adtmode; 
  unsigned short adtnum;  
  sqltvn adttvn[1];       
};
typedef struct sqladts sqladts;

static struct sqladts sqladt = {
  1,0,0,
};

/* Binding to PL/SQL Records */
struct sqltdss
{
  unsigned int tdsvsn; 
  unsigned short tdsnum; 
  unsigned char *tdsval[1]; 
};
typedef struct sqltdss sqltdss;
static struct sqltdss sqltds =
{
  1,
  0,
};

/* File name & Package Name */
struct sqlcxp
{
  unsigned short fillen;
           char  filnam[79];
};
static const struct sqlcxp sqlfpn =
{
    78,
    "/home/<USER>/CLionProjects/ftalk_up/daemon_logon_ftk/build_occi/DatabaseORA_MMS.pc"
};


static unsigned int sqlctx = 1142393117;


static struct sqlexd {
   unsigned long  sqlvsn;
   unsigned int   arrsiz;
   unsigned int   iters;
   unsigned int   offset;
   unsigned short selerr;
   unsigned short sqlety;
   unsigned int   occurs;
      const short *cud;
   unsigned char  *sqlest;
      const char  *stmt;
   sqladts *sqladtp;
   sqltdss *sqltdsp;
   unsigned char  **sqphsv;
   unsigned long  *sqphsl;
            int   *sqphss;
            short **sqpind;
            int   *sqpins;
   unsigned long  *sqparm;
   unsigned long  **sqparc;
   unsigned short  *sqpadto;
   unsigned short  *sqptdso;
   unsigned int   sqlcmax;
   unsigned int   sqlcmin;
   unsigned int   sqlcincr;
   unsigned int   sqlctimeout;
   unsigned int   sqlcnowait;
            int   sqfoff;
   unsigned int   sqcmod;
   unsigned int   sqfmod;
   unsigned int   sqlpfmem;
   unsigned char  *sqhstv[35];
   unsigned long  sqhstl[35];
            int   sqhsts[35];
            short *sqindv[35];
            int   sqinds[35];
   unsigned long  sqharm[35];
   unsigned long  *sqharc[35];
   unsigned short  sqadto[35];
   unsigned short  sqtdso[35];
} sqlstm = {13,35};

// Prototypes
extern "C" {
  void sqlcxt (void **, unsigned int *,
               struct sqlexd *, const struct sqlcxp *);
  void sqlcx2t(void **, unsigned int *,
               struct sqlexd *, const struct sqlcxp *);
  void sqlbuft(void **, char *);
  void sqlgs2t(void **, char *);
  void sqlorat(void **, unsigned int *, void *);
}

// Forms Interface
static const int IAPSUCC = 0;
static const int IAPFAIL = 1403;
static const int IAPFTL  = 535;
extern "C" { void sqliem(unsigned char *, signed int *); }

 static const char *sq0011 = 
"select CALLBACK  from TBL_CALLBACK where PTN_ID=:b0           ";

 static const char *sq0012 = 
"select DIAL_CODE  from TBL_ALLOW_DIAL_CODE where DIAL_CODE_TYPE=:b0         \
  ";

 static const char *sq0018 = 
"select SEQ_PROC_ID.nextval   from DUAL            ";

typedef struct { unsigned short len; unsigned char arr[1]; } VARCHAR;
typedef struct { unsigned short len; unsigned char arr[1]; } varchar;

/* cud (compilation unit data) array */
static const short sqlcud0[] =
{13,4242,1,0,0,
5,0,0,0,0,0,27,32,0,0,4,4,0,1,0,1,5,0,0,1,10,0,0,1,5,0,0,1,10,0,0,
36,0,0,2,0,0,30,47,0,0,0,0,0,1,0,
51,0,0,3,0,0,29,62,0,0,0,0,0,1,0,
66,0,0,4,0,0,31,80,0,0,0,0,0,1,0,
81,0,0,5,129,0,6,109,0,0,4,4,0,1,0,1,5,0,0,2,3,0,0,2,3,0,0,2,5,0,0,
112,0,0,6,215,0,6,158,0,0,7,7,0,1,0,1,3,0,0,1,3,0,0,1,5,0,0,1,5,0,0,1,5,0,0,2,
3,0,0,2,5,0,0,
155,0,0,7,1112,0,6,298,0,0,35,35,0,1,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,
1,5,0,0,1,3,0,0,1,3,0,0,1,3,0,0,1,3,0,0,1,3,0,0,1,3,0,0,1,3,0,0,1,3,0,0,1,3,0,
0,1,3,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,
0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,2,3,0,0,2,
5,0,0,
310,0,0,8,380,0,6,402,0,0,13,13,0,1,0,1,3,0,0,1,3,0,0,1,3,0,0,1,5,0,0,1,5,0,0,
1,5,0,0,1,3,0,0,1,5,0,0,1,3,0,0,1,3,0,0,1,5,0,0,2,3,0,0,2,5,0,0,
377,0,0,9,378,0,6,478,0,0,13,13,0,1,0,1,5,0,0,2,5,0,0,2,5,0,0,2,3,0,0,2,5,0,0,
2,5,0,0,2,5,0,0,2,5,0,0,2,5,0,0,2,5,0,0,2,3,0,0,2,3,0,0,2,5,0,0,
444,0,0,10,168,0,6,595,0,0,11,11,0,1,0,1,3,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,3,0,0,
1,5,0,0,1,3,0,0,1,3,0,0,1,5,0,0,2,3,0,0,2,5,0,0,
503,0,0,11,62,0,9,651,0,0,1,1,0,1,0,1,3,0,0,
522,0,0,11,0,0,13,665,0,0,1,0,0,1,0,2,5,0,0,
541,0,0,11,0,0,15,685,0,0,0,0,0,1,0,
556,0,0,12,78,0,9,707,0,0,1,1,0,1,0,1,5,0,0,
575,0,0,12,0,0,13,721,0,0,1,0,0,1,0,2,5,0,0,
594,0,0,12,0,0,15,742,0,0,0,0,0,1,0,
609,0,0,13,129,0,6,763,0,0,4,4,0,1,0,1,5,0,0,2,3,0,0,2,3,0,0,2,5,0,0,
640,0,0,14,283,0,6,826,0,0,9,9,0,1,0,1,3,0,0,1,3,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,
5,0,0,1,5,0,0,2,3,0,0,2,5,0,0,
691,0,0,15,555,0,6,1136,0,0,19,19,0,1,0,1,3,0,0,1,3,0,0,1,5,0,0,1,5,0,0,1,5,0,
0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,
0,0,1,5,0,0,1,5,0,0,2,3,0,0,2,5,0,0,
782,0,0,16,805,0,6,1270,0,0,23,23,0,1,0,1,3,0,0,1,3,0,0,1,5,0,0,1,5,0,0,1,5,0,
0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,
0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,2,3,0,0,2,5,0,0,
889,0,0,17,339,0,6,1363,0,0,11,11,0,1,0,1,3,0,0,1,3,0,0,1,5,0,0,1,5,0,0,1,5,0,
0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,2,3,0,0,2,5,0,0,
948,0,0,18,50,0,9,1414,0,0,0,0,0,1,0,
963,0,0,18,0,0,13,1427,0,0,1,0,0,1,0,2,3,0,0,
982,0,0,18,0,0,15,1441,0,0,0,0,0,1,0,
997,0,0,18,0,0,15,1447,0,0,0,0,0,1,0,
1012,0,0,19,216,0,6,1558,0,0,14,14,0,1,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,
0,1,5,0,0,1,5,0,0,1,3,0,0,1,3,0,0,1,5,0,0,2,3,0,0,2,5,0,0,2,3,0,0,2,5,0,0,
1083,0,0,20,167,0,6,1584,0,0,11,11,0,1,0,1,5,0,0,1,5,0,0,1,5,0,0,1,3,0,0,1,5,0,
0,1,5,0,0,1,5,0,0,1,5,0,0,1,5,0,0,2,3,0,0,2,5,0,0,
};


/*
 * DatabaseORA_MMS.cpp
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#include "DatabaseORA_MMS.h"
#include <sqlca.h>
#include <iostream>

using namespace std;

namespace KSKYB
{
int CDatabaseORA::connectToOracle(char* szUID, char* szDSN)
{
	/* EXEC SQL BEGIN DECLARE SECTION; */ 

	char szConnInf[20+1], szConnDsn[20+1];
	/* EXEC SQL END DECLARE SECTION; */ 

	
	if ((szUID == NULL) || (szDSN == NULL)) {
		sprintf(tmpLog3, "CDatabaseORA::connectToOracle() ERROR[szUID or szDSN is NULL]");
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
	memset(szConnInf, 0x00, sizeof(szConnInf));
	memset(szConnDsn, 0x00, sizeof(szConnDsn));
	strcpy(szConnInf, szUID);
	strcpy(szConnDsn, szDSN);
	
	/* EXEC SQL CONNECT :szConnInf USING :szConnDsn; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 4;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.iters = (unsigned int  )10;
 sqlstm.offset = (unsigned int  )5;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlstm.sqhstv[0] = (unsigned char  *)szConnInf;
 sqlstm.sqhstl[0] = (unsigned long )21;
 sqlstm.sqhsts[0] = (         int  )21;
 sqlstm.sqindv[0] = (         short *)0;
 sqlstm.sqinds[0] = (         int  )0;
 sqlstm.sqharm[0] = (unsigned long )0;
 sqlstm.sqadto[0] = (unsigned short )0;
 sqlstm.sqtdso[0] = (unsigned short )0;
 sqlstm.sqhstv[2] = (unsigned char  *)szConnDsn;
 sqlstm.sqhstl[2] = (unsigned long )21;
 sqlstm.sqhsts[2] = (         int  )21;
 sqlstm.sqindv[2] = (         short *)0;
 sqlstm.sqinds[2] = (         int  )0;
 sqlstm.sqharm[2] = (unsigned long )0;
 sqlstm.sqadto[2] = (unsigned short )0;
 sqlstm.sqtdso[2] = (unsigned short )0;
 sqlstm.sqphsv = sqlstm.sqhstv;
 sqlstm.sqphsl = sqlstm.sqhstl;
 sqlstm.sqphss = sqlstm.sqhsts;
 sqlstm.sqpind = sqlstm.sqindv;
 sqlstm.sqpins = sqlstm.sqinds;
 sqlstm.sqparm = sqlstm.sqharm;
 sqlstm.sqparc = sqlstm.sqharc;
 sqlstm.sqpadto = sqlstm.sqadto;
 sqlstm.sqptdso = sqlstm.sqtdso;
 sqlstm.sqlcmax = (unsigned int )100;
 sqlstm.sqlcmin = (unsigned int )2;
 sqlstm.sqlcincr = (unsigned int )1;
 sqlstm.sqlctimeout = (unsigned int )3;
 sqlstm.sqlcnowait = (unsigned int )0;
 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::connectToOracle() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
	sprintf(tmpLog3, "CDatabaseORA::connectToOracle() Success");
	_logPrint(_DATALOG, tmpLog3);
	
	return 1;
}

int CDatabaseORA::closeFromOracle()
{
	/* EXEC SQL COMMIT WORK RELEASE; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 4;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )36;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::closeFromOracle() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
	sprintf(tmpLog3, "CDatabaseORA::closeFromOracle() Success");
	_logPrint(_DATALOG, tmpLog3);
	
	return 1;
}

int CDatabaseORA::commitOracle()
{
	/* EXEC SQL COMMIT WORK; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 4;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )51;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::commitOracle() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
	
	sprintf(tmpLog3, "CDatabaseORA::commitOracle() Success");
	_logPrint(_DATALOG, tmpLog3);
	
	
	return 1;
}

int CDatabaseORA::rollbackOracle()
{
	/* EXEC SQL WHENEVER SQLERROR CONTINUE; */ 

	/* EXEC SQL ROLLBACK WORK; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 4;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )66;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::rollbackOracle() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
	
	sprintf(tmpLog3, "CDatabaseORA::rollbackOracle() Success");
	_logPrint(_DATALOG, tmpLog3);
	
	return 1;
}

int CDatabaseORA::getCTNID()
{
	/* EXEC SQL BEGIN DECLARE SECTION; */ 

	char szSqlErrorMsg[1024];
	char szCid[10+1];
	int nCTNID = 0;
	int nSqlCode = -999;
	/* EXEC SQL END DECLARE SECTION; */ 


	memset(szSqlErrorMsg,0x00,sizeof(szSqlErrorMsg));		//CCL(szSqlErrorMsg);
	memset(szCid        ,0x00,sizeof(szCid        ));		//CCL(szCid);
	
	/* in_cid CA·I½AA®¿¡¼­ ≫c¿e¾ECO */
	memset(szCid, 0x00, 10);
	
	/* EXEC SQL EXECUTE
		BEGIN
			proc_get_ctn_id( in_cid     =>:szCid
							,ot_cnt_id  =>:nCTNID
							,ot_sqlcode =>:nSqlCode
							,ot_sqlmsg  =>:szSqlErrorMsg
   							);
		END;
	END-EXEC; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 4;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.stmt = "begin proc_get_ctn_id ( in_cid => :szCid , ot_cnt_id => :nCT\
NID , ot_sqlcode => :nSqlCode , ot_sqlmsg => :szSqlErrorMsg ) ; END ;";
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )81;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlstm.sqhstv[0] = (unsigned char  *)szCid;
 sqlstm.sqhstl[0] = (unsigned long )11;
 sqlstm.sqhsts[0] = (         int  )0;
 sqlstm.sqindv[0] = (         short *)0;
 sqlstm.sqinds[0] = (         int  )0;
 sqlstm.sqharm[0] = (unsigned long )0;
 sqlstm.sqadto[0] = (unsigned short )0;
 sqlstm.sqtdso[0] = (unsigned short )0;
 sqlstm.sqhstv[1] = (unsigned char  *)&nCTNID;
 sqlstm.sqhstl[1] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[1] = (         int  )0;
 sqlstm.sqindv[1] = (         short *)0;
 sqlstm.sqinds[1] = (         int  )0;
 sqlstm.sqharm[1] = (unsigned long )0;
 sqlstm.sqadto[1] = (unsigned short )0;
 sqlstm.sqtdso[1] = (unsigned short )0;
 sqlstm.sqhstv[2] = (unsigned char  *)&nSqlCode;
 sqlstm.sqhstl[2] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[2] = (         int  )0;
 sqlstm.sqindv[2] = (         short *)0;
 sqlstm.sqinds[2] = (         int  )0;
 sqlstm.sqharm[2] = (unsigned long )0;
 sqlstm.sqadto[2] = (unsigned short )0;
 sqlstm.sqtdso[2] = (unsigned short )0;
 sqlstm.sqhstv[3] = (unsigned char  *)szSqlErrorMsg;
 sqlstm.sqhstl[3] = (unsigned long )1024;
 sqlstm.sqhsts[3] = (         int  )0;
 sqlstm.sqindv[3] = (         short *)0;
 sqlstm.sqinds[3] = (         int  )0;
 sqlstm.sqharm[3] = (unsigned long )0;
 sqlstm.sqadto[3] = (unsigned short )0;
 sqlstm.sqtdso[3] = (unsigned short )0;
 sqlstm.sqphsv = sqlstm.sqhstv;
 sqlstm.sqphsl = sqlstm.sqhstl;
 sqlstm.sqphss = sqlstm.sqhsts;
 sqlstm.sqpind = sqlstm.sqindv;
 sqlstm.sqpins = sqlstm.sqinds;
 sqlstm.sqparm = sqlstm.sqharm;
 sqlstm.sqparc = sqlstm.sqharc;
 sqlstm.sqpadto = sqlstm.sqadto;
 sqlstm.sqptdso = sqlstm.sqtdso;
 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}


	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			sprintf(tmpLog3, "[ERR] CDatabaseORA::getCTNID failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc)));
			_logPrint(_DATALOG, tmpLog3);
			return -1;
		}
		sprintf(tmpLog3, "[ERR] dCDatabaseORA::getCTNID invaled failed - otReuslt[%d]errMsg[%s]",nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
		
	return nCTNID;
}

int CDatabaseORA::setMMSCTNTBL(CSenderDbMMSCTNTBL &ctn_data)
{	
	/* EXEC SQL BEGIN DECLARE SECTION; */ 

	int nSqlCode = -999;
	int nCtnId   = 0   ;
	int nCtnSeq  = 0   ;
	char szSqlErrorMsg[1024];
	char szCtnName    [50+1];
	char szCtnMime    [50+1];
	char szCtnSvc     [ 5+1];
	/* EXEC SQL END DECLARE SECTION; */ 

	
	memset(szSqlErrorMsg, 0x00, sizeof(szSqlErrorMsg));
	memset(szCtnName    , 0x00, sizeof(szCtnName    ));
	memset(szCtnMime    , 0x00, sizeof(szCtnMime    ));
	memset(szCtnSvc     , 0x00, sizeof(szCtnSvc     ));

	memcpy(szCtnName, ctn_data.szCtnName, sizeof(ctn_data.szCtnName));
	memcpy(szCtnMime, ctn_data.szCtnMime, sizeof(ctn_data.szCtnMime));
	memcpy(szCtnSvc , ctn_data.szCtnSvc , sizeof(ctn_data.szCtnSvc ));
	
	nCtnId  = ctn_data.nCtnId ;
	nCtnSeq = ctn_data.nCtnSeq;

	/* EXEC SQL EXECUTE
		BEGIN
			proc_set_mms_ctn(in_cid      => :nCtnId
							,in_ctn_seq  => :nCtnSeq
							,in_ctn_name => :szCtnName
							,in_ctn_mime => :szCtnMime
							,in_ctn_svc  => :szCtnSvc
							,ot_sqlcode  => :nSqlCode
							,ot_sqlmsg   => :szSqlErrorMsg
							);
		END;
	END-EXEC; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 7;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.stmt = "begin proc_set_mms_ctn ( in_cid => :nCtnId , in_ctn_seq => :\
nCtnSeq , in_ctn_name => :szCtnName , in_ctn_mime => :szCtnMime , in_ctn_svc =\
> :szCtnSvc , ot_sqlcode => :nSqlCode , ot_sqlmsg => :szSqlErrorMsg ) ; END ;";
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )112;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlstm.sqhstv[0] = (unsigned char  *)&nCtnId;
 sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[0] = (         int  )0;
 sqlstm.sqindv[0] = (         short *)0;
 sqlstm.sqinds[0] = (         int  )0;
 sqlstm.sqharm[0] = (unsigned long )0;
 sqlstm.sqadto[0] = (unsigned short )0;
 sqlstm.sqtdso[0] = (unsigned short )0;
 sqlstm.sqhstv[1] = (unsigned char  *)&nCtnSeq;
 sqlstm.sqhstl[1] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[1] = (         int  )0;
 sqlstm.sqindv[1] = (         short *)0;
 sqlstm.sqinds[1] = (         int  )0;
 sqlstm.sqharm[1] = (unsigned long )0;
 sqlstm.sqadto[1] = (unsigned short )0;
 sqlstm.sqtdso[1] = (unsigned short )0;
 sqlstm.sqhstv[2] = (unsigned char  *)szCtnName;
 sqlstm.sqhstl[2] = (unsigned long )51;
 sqlstm.sqhsts[2] = (         int  )0;
 sqlstm.sqindv[2] = (         short *)0;
 sqlstm.sqinds[2] = (         int  )0;
 sqlstm.sqharm[2] = (unsigned long )0;
 sqlstm.sqadto[2] = (unsigned short )0;
 sqlstm.sqtdso[2] = (unsigned short )0;
 sqlstm.sqhstv[3] = (unsigned char  *)szCtnMime;
 sqlstm.sqhstl[3] = (unsigned long )51;
 sqlstm.sqhsts[3] = (         int  )0;
 sqlstm.sqindv[3] = (         short *)0;
 sqlstm.sqinds[3] = (         int  )0;
 sqlstm.sqharm[3] = (unsigned long )0;
 sqlstm.sqadto[3] = (unsigned short )0;
 sqlstm.sqtdso[3] = (unsigned short )0;
 sqlstm.sqhstv[4] = (unsigned char  *)szCtnSvc;
 sqlstm.sqhstl[4] = (unsigned long )6;
 sqlstm.sqhsts[4] = (         int  )0;
 sqlstm.sqindv[4] = (         short *)0;
 sqlstm.sqinds[4] = (         int  )0;
 sqlstm.sqharm[4] = (unsigned long )0;
 sqlstm.sqadto[4] = (unsigned short )0;
 sqlstm.sqtdso[4] = (unsigned short )0;
 sqlstm.sqhstv[5] = (unsigned char  *)&nSqlCode;
 sqlstm.sqhstl[5] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[5] = (         int  )0;
 sqlstm.sqindv[5] = (         short *)0;
 sqlstm.sqinds[5] = (         int  )0;
 sqlstm.sqharm[5] = (unsigned long )0;
 sqlstm.sqadto[5] = (unsigned short )0;
 sqlstm.sqtdso[5] = (unsigned short )0;
 sqlstm.sqhstv[6] = (unsigned char  *)szSqlErrorMsg;
 sqlstm.sqhstl[6] = (unsigned long )1024;
 sqlstm.sqhsts[6] = (         int  )0;
 sqlstm.sqindv[6] = (         short *)0;
 sqlstm.sqinds[6] = (         int  )0;
 sqlstm.sqharm[6] = (unsigned long )0;
 sqlstm.sqadto[6] = (unsigned short )0;
 sqlstm.sqtdso[6] = (unsigned short )0;
 sqlstm.sqphsv = sqlstm.sqhstv;
 sqlstm.sqphsl = sqlstm.sqhstl;
 sqlstm.sqphss = sqlstm.sqhsts;
 sqlstm.sqpind = sqlstm.sqindv;
 sqlstm.sqpins = sqlstm.sqinds;
 sqlstm.sqparm = sqlstm.sqharm;
 sqlstm.sqparc = sqlstm.sqharc;
 sqlstm.sqpadto = sqlstm.sqadto;
 sqlstm.sqptdso = sqlstm.sqtdso;
 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}


	/* here~ */
	//sprintf(tmpLog3, "[%s]: CDatabaseORA::setMMSCTNTBL-[%d][%d] [%s][%s][%s] [%d][%s]",__func__,nCtnId,nCtnSeq,szCtnName,szCtnMime,szCtnSvc,nSqlCode,"");
	//_logPrint(_DATALOG, tmpLog3);
	
	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSCTNTBL exec failed - nCtnId[%d] sqlcode[%d] sqlmsg[%s]",nCtnId,sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc)));
			_logPrint(_DATALOG, tmpLog3);
			return -1;
		}
		sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSCTNTBL invaled failed - nCtnId[%d]otReuslt[%d]errMsg[%s]",nCtnId,nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
	
	return 1;
}

int CDatabaseORA::setMMSTBL(CSenderDbMMSTBL &mms_data)
{
	/* EXEC SQL BEGIN DECLARE SECTION; */ 

	char szSqlErrorMsg[1024];
	int nSqlCode = -999;

	char szDstAddr[16+1];
	char szCallBack[16+1];
	char szMsgTitle[64+1];
	char szPtnSn[16+1];
	char szResvData[200+1];
	char szCid[10+1];
	int nMsgType = 0;
	int nPriority = 0;
	int nCtnId = 0;
	int nCtnType = 0;
	int nRgnRate = 0;
	int nInterval = 0;
	int nTextCnt = 0;
	int nImgCnt = 0;
	int nAugCnt = 0;
	int nMpCnt = 0;
	char cMMSId[20];
	char szSenderKey[40+1];
	char szChatBubbleType[30+1];
	char szTargeting[1+1];
	char szTmplCd    [20+1];
	char szRepFlag	 [1+1];
	char szRepTitle [64+1];
	char szRepText	 [500+1];
	char szAppUserId [20+1];
	char szPushAlarm [1+1];
	char szMessageVariable	[2000+1];
	char szButtonVariable 	[2000+1];
	char szCouponVariable	[1000+1];
	char szImageVariable	[1000+1];
	char szVideoVariable	[1000+1];
	char szCommerceVariable	[1000+1];
	char szCarouselVariable	[2000+1];	
	/* EXEC SQL END DECLARE SECTION; */ 


	memset(szSqlErrorMsg , 0x00, sizeof(szSqlErrorMsg));
	memset(szCid         , 0x00, sizeof(szCid        ));
	memset(szDstAddr     , 0x00, sizeof(szDstAddr    ));
	memset(szCallBack    , 0x00, sizeof(szCallBack   ));
	memset(szMsgTitle    , 0x00, sizeof(szMsgTitle   ));
	memset(szPtnSn       , 0x00, sizeof(szPtnSn      ));
	memset(szResvData    , 0x00, sizeof(szResvData   ));
	memset(cMMSId        , 0x00, sizeof(cMMSId       ));
	memset(szSenderKey   , 0x00, sizeof(szSenderKey       ));
	memset(szChatBubbleType   , 0x00, sizeof(szChatBubbleType       ));
	memset(szTargeting   , 0x00, sizeof(szTargeting       ));
	memset(szTmplCd    , 0x00, sizeof(szTmplCd       ));
	memset(szRepFlag   , 0x00, sizeof(szRepFlag       ));
	memset(szRepTitle  , 0x00, sizeof(szRepTitle       ));
	memset(szRepText   , 0x00, sizeof(szRepText       ));
	memset(szAppUserId , 0x00, sizeof(szAppUserId       ));
	memset(szPushAlarm , 0x00, sizeof(szPushAlarm       ));
	memset(szMessageVariable, 0x00, sizeof(szMessageVariable));
	memset(szButtonVariable, 0x00, sizeof(szButtonVariable));
	memset(szCouponVariable, 0x00, sizeof(szCouponVariable));
	memset(szImageVariable, 0x00, sizeof(szImageVariable));
	memset(szVideoVariable, 0x00, sizeof(szVideoVariable));
	memset(szCommerceVariable, 0x00, sizeof(szCommerceVariable));
	memset(szCarouselVariable, 0x00, sizeof(szCarouselVariable));
	
	strcpy(szDstAddr  , mms_data.szDstAddr);
	strcpy(szCallBack , mms_data.szCallBack);
	sprintf(szMsgTitle, "%.*s", (int)(sizeof(szMsgTitle)-1), mms_data.szMsgTitle);
	strcpy(szPtnSn    , mms_data.szPtnSn);
	strcpy(szResvData , mms_data.szResvData);
	memcpy(szCid      , mms_data.szCid, 10);
	
	sprintf(cMMSId,"%lld", mms_data.nMMSId);
	
	nMsgType 	= mms_data.nMsgType;
	nPriority 	= mms_data.nPriority;
	nCtnId 		= mms_data.nCtnId;
	nCtnType 	= mms_data.nCtnType;
	nRgnRate 	= mms_data.nRgnRate;
	nInterval 	= mms_data.nInterval;
	nTextCnt 	= mms_data.nTextCnt;
	nImgCnt 	= mms_data.nImgCnt;
	nAugCnt 	= mms_data.nAugCnt;
	nMpCnt 		= mms_data.nMpCnt;
	
	//20190708 sender key 추가
	//sprintf(szSenderKey, mms_data.szSenderKey);
	snprintf(szSenderKey, sizeof(szSenderKey), mms_data.szSenderKey);
	snprintf(szChatBubbleType, sizeof(szChatBubbleType), mms_data.szChatBubbleType);
	snprintf(szTargeting, sizeof(szTargeting), mms_data.szTargeting);
	snprintf(szTmplCd, sizeof(szTmplCd), mms_data.szTmplCd);
	snprintf(szRepFlag, sizeof(szRepFlag), mms_data.szRepFlag);
	snprintf(szRepTitle, sizeof(szRepTitle), mms_data.szRepTitle);
	snprintf(szRepText, sizeof(szRepText), mms_data.szRepText);
	snprintf(szAppUserId, sizeof(szAppUserId), mms_data.szAppUserId);
	snprintf(szPushAlarm, sizeof(szPushAlarm), mms_data.szPushAlarm);
	snprintf(szMessageVariable, sizeof(szMessageVariable), mms_data.szMessageVariable);
	snprintf(szButtonVariable, sizeof(szButtonVariable), mms_data.szButtonVariable);
	snprintf(szCouponVariable, sizeof(szCouponVariable), mms_data.szCouponVariable);
	snprintf(szImageVariable, sizeof(szImageVariable), mms_data.szImageVariable);
	snprintf(szVideoVariable, sizeof(szVideoVariable), mms_data.szVideoVariable);
	snprintf(szCommerceVariable, sizeof(szCommerceVariable), mms_data.szCommerceVariable);
	snprintf(szCarouselVariable, sizeof(szCarouselVariable), mms_data.szCarouselVariable);
	
	//sprintf(tmpLog3, "[DBG] setMMSTBL1 MMSID[%lld][%s]", mms_data.nMMSId, cMMSId);
	//_logPrint(_DATALOG, tmpLog3);
	
	/* EXEC SQL EXECUTE
		BEGIN
			proc_set_mms_tbl(in_dstaddr =>:szDstAddr
							,in_callback =>:szCallBack
							,in_msgtitle =>:szMsgTitle
							,in_ptn_sn =>:szPtnSn
							,in_resv_data =>:szResvData
							,in_cid =>:szCid
							,in_msg_type =>:nMsgType
							,in_priority =>:nPriority
							,in_ctn_id =>:nCtnId
							,in_ctn_type =>:nCtnType
							,in_rgn_rate =>:nRgnRate
							,in_interval => :nInterval
							,in_text_cnt => :nTextCnt
							,in_img_cnt => :nImgCnt
							,in_aud_cnt => :nAugCnt
							,in_mp_cnt => :nMpCnt
							,in_mms_id => :cMMSId
							,in_sender_key => :szSenderKey
							,in_chat_bubble_type => :szChatBubbleType
							,in_targeting => :szTargeting
							,in_tmpl_cd => :szTmplCd
							,in_rep_flag => :szRepFlag
							,in_rep_title => :szRepTitle
							,in_rep_text => :szRepText
							,in_app_user_id => :szAppUserId
							,in_push_alarm => :szPushAlarm
							,in_message_variable => :szMessageVariable
							,in_button_variable => :szButtonVariable
							,in_coupon_variable => :szCouponVariable
							,in_image_variable => :szImageVariable
							,in_video_variable => :szVideoVariable
							,in_commerce_variable => :szCommerceVariable
							,in_carousel_variable => :szCarouselVariable
							,ot_sqlcode =>:nSqlCode
							,ot_sqlmsg =>:szSqlErrorMsg
               				);
		END;
	END-EXEC; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 35;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlbuft((void **)0, 
   "begin proc_set_mms_tbl ( in_dstaddr => :szDstAddr , in_callback => :szCa\
llBack , in_msgtitle => :szMsgTitle , in_ptn_sn => :szPtnSn , in_resv_data =\
> :szResvData , in_cid => :szCid , in_msg_type => :nMsgType , in_priority =>\
 :nPriority , in_ctn_id => :nCtnId , in_ctn_type => :nCtnType , in_rgn_rate \
=> :nRgnRate , in_interval => :nInterval , in_text_cnt => :nTextCnt , in_img\
_cnt => :nImgCnt , in_aud_cnt => :nAugCnt , in_mp_cnt => :nMpCnt , in_mms_id\
 => :cMMSId , in_sender_key => :szSenderKey , in_chat_bubble_type => :szChat\
BubbleType , in_targeting => :szTargeting , in_tmpl_cd => :szTmplCd , in_rep\
_flag => :szRepFlag , in_rep_title => :szRepTitle , in_rep_text => :szRepTex\
t , in_app_user_id => :szAppUserId , in_push_alarm => :szPushAlarm , in_mess\
age_variable => :szMessageVariable , in_button_variable => :szButtonVariable\
 , in_coupon_variable => :szCouponVariable , in_image_variable => :szImageVa\
riable , in_video_variable => :szVideoVariable , in_commerce_variable => :sz\
CommerceVariable , in_carousel_variable ");
 sqlstm.stmt = "=> :szCarouselVariable , ot_sqlcode => :nSqlCode , ot_sqlmsg\
 => :szSqlErrorMsg ) ; END ;";
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )155;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlstm.sqhstv[0] = (unsigned char  *)szDstAddr;
 sqlstm.sqhstl[0] = (unsigned long )17;
 sqlstm.sqhsts[0] = (         int  )0;
 sqlstm.sqindv[0] = (         short *)0;
 sqlstm.sqinds[0] = (         int  )0;
 sqlstm.sqharm[0] = (unsigned long )0;
 sqlstm.sqadto[0] = (unsigned short )0;
 sqlstm.sqtdso[0] = (unsigned short )0;
 sqlstm.sqhstv[1] = (unsigned char  *)szCallBack;
 sqlstm.sqhstl[1] = (unsigned long )17;
 sqlstm.sqhsts[1] = (         int  )0;
 sqlstm.sqindv[1] = (         short *)0;
 sqlstm.sqinds[1] = (         int  )0;
 sqlstm.sqharm[1] = (unsigned long )0;
 sqlstm.sqadto[1] = (unsigned short )0;
 sqlstm.sqtdso[1] = (unsigned short )0;
 sqlstm.sqhstv[2] = (unsigned char  *)szMsgTitle;
 sqlstm.sqhstl[2] = (unsigned long )65;
 sqlstm.sqhsts[2] = (         int  )0;
 sqlstm.sqindv[2] = (         short *)0;
 sqlstm.sqinds[2] = (         int  )0;
 sqlstm.sqharm[2] = (unsigned long )0;
 sqlstm.sqadto[2] = (unsigned short )0;
 sqlstm.sqtdso[2] = (unsigned short )0;
 sqlstm.sqhstv[3] = (unsigned char  *)szPtnSn;
 sqlstm.sqhstl[3] = (unsigned long )17;
 sqlstm.sqhsts[3] = (         int  )0;
 sqlstm.sqindv[3] = (         short *)0;
 sqlstm.sqinds[3] = (         int  )0;
 sqlstm.sqharm[3] = (unsigned long )0;
 sqlstm.sqadto[3] = (unsigned short )0;
 sqlstm.sqtdso[3] = (unsigned short )0;
 sqlstm.sqhstv[4] = (unsigned char  *)szResvData;
 sqlstm.sqhstl[4] = (unsigned long )201;
 sqlstm.sqhsts[4] = (         int  )0;
 sqlstm.sqindv[4] = (         short *)0;
 sqlstm.sqinds[4] = (         int  )0;
 sqlstm.sqharm[4] = (unsigned long )0;
 sqlstm.sqadto[4] = (unsigned short )0;
 sqlstm.sqtdso[4] = (unsigned short )0;
 sqlstm.sqhstv[5] = (unsigned char  *)szCid;
 sqlstm.sqhstl[5] = (unsigned long )11;
 sqlstm.sqhsts[5] = (         int  )0;
 sqlstm.sqindv[5] = (         short *)0;
 sqlstm.sqinds[5] = (         int  )0;
 sqlstm.sqharm[5] = (unsigned long )0;
 sqlstm.sqadto[5] = (unsigned short )0;
 sqlstm.sqtdso[5] = (unsigned short )0;
 sqlstm.sqhstv[6] = (unsigned char  *)&nMsgType;
 sqlstm.sqhstl[6] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[6] = (         int  )0;
 sqlstm.sqindv[6] = (         short *)0;
 sqlstm.sqinds[6] = (         int  )0;
 sqlstm.sqharm[6] = (unsigned long )0;
 sqlstm.sqadto[6] = (unsigned short )0;
 sqlstm.sqtdso[6] = (unsigned short )0;
 sqlstm.sqhstv[7] = (unsigned char  *)&nPriority;
 sqlstm.sqhstl[7] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[7] = (         int  )0;
 sqlstm.sqindv[7] = (         short *)0;
 sqlstm.sqinds[7] = (         int  )0;
 sqlstm.sqharm[7] = (unsigned long )0;
 sqlstm.sqadto[7] = (unsigned short )0;
 sqlstm.sqtdso[7] = (unsigned short )0;
 sqlstm.sqhstv[8] = (unsigned char  *)&nCtnId;
 sqlstm.sqhstl[8] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[8] = (         int  )0;
 sqlstm.sqindv[8] = (         short *)0;
 sqlstm.sqinds[8] = (         int  )0;
 sqlstm.sqharm[8] = (unsigned long )0;
 sqlstm.sqadto[8] = (unsigned short )0;
 sqlstm.sqtdso[8] = (unsigned short )0;
 sqlstm.sqhstv[9] = (unsigned char  *)&nCtnType;
 sqlstm.sqhstl[9] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[9] = (         int  )0;
 sqlstm.sqindv[9] = (         short *)0;
 sqlstm.sqinds[9] = (         int  )0;
 sqlstm.sqharm[9] = (unsigned long )0;
 sqlstm.sqadto[9] = (unsigned short )0;
 sqlstm.sqtdso[9] = (unsigned short )0;
 sqlstm.sqhstv[10] = (unsigned char  *)&nRgnRate;
 sqlstm.sqhstl[10] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[10] = (         int  )0;
 sqlstm.sqindv[10] = (         short *)0;
 sqlstm.sqinds[10] = (         int  )0;
 sqlstm.sqharm[10] = (unsigned long )0;
 sqlstm.sqadto[10] = (unsigned short )0;
 sqlstm.sqtdso[10] = (unsigned short )0;
 sqlstm.sqhstv[11] = (unsigned char  *)&nInterval;
 sqlstm.sqhstl[11] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[11] = (         int  )0;
 sqlstm.sqindv[11] = (         short *)0;
 sqlstm.sqinds[11] = (         int  )0;
 sqlstm.sqharm[11] = (unsigned long )0;
 sqlstm.sqadto[11] = (unsigned short )0;
 sqlstm.sqtdso[11] = (unsigned short )0;
 sqlstm.sqhstv[12] = (unsigned char  *)&nTextCnt;
 sqlstm.sqhstl[12] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[12] = (         int  )0;
 sqlstm.sqindv[12] = (         short *)0;
 sqlstm.sqinds[12] = (         int  )0;
 sqlstm.sqharm[12] = (unsigned long )0;
 sqlstm.sqadto[12] = (unsigned short )0;
 sqlstm.sqtdso[12] = (unsigned short )0;
 sqlstm.sqhstv[13] = (unsigned char  *)&nImgCnt;
 sqlstm.sqhstl[13] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[13] = (         int  )0;
 sqlstm.sqindv[13] = (         short *)0;
 sqlstm.sqinds[13] = (         int  )0;
 sqlstm.sqharm[13] = (unsigned long )0;
 sqlstm.sqadto[13] = (unsigned short )0;
 sqlstm.sqtdso[13] = (unsigned short )0;
 sqlstm.sqhstv[14] = (unsigned char  *)&nAugCnt;
 sqlstm.sqhstl[14] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[14] = (         int  )0;
 sqlstm.sqindv[14] = (         short *)0;
 sqlstm.sqinds[14] = (         int  )0;
 sqlstm.sqharm[14] = (unsigned long )0;
 sqlstm.sqadto[14] = (unsigned short )0;
 sqlstm.sqtdso[14] = (unsigned short )0;
 sqlstm.sqhstv[15] = (unsigned char  *)&nMpCnt;
 sqlstm.sqhstl[15] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[15] = (         int  )0;
 sqlstm.sqindv[15] = (         short *)0;
 sqlstm.sqinds[15] = (         int  )0;
 sqlstm.sqharm[15] = (unsigned long )0;
 sqlstm.sqadto[15] = (unsigned short )0;
 sqlstm.sqtdso[15] = (unsigned short )0;
 sqlstm.sqhstv[16] = (unsigned char  *)cMMSId;
 sqlstm.sqhstl[16] = (unsigned long )20;
 sqlstm.sqhsts[16] = (         int  )0;
 sqlstm.sqindv[16] = (         short *)0;
 sqlstm.sqinds[16] = (         int  )0;
 sqlstm.sqharm[16] = (unsigned long )0;
 sqlstm.sqadto[16] = (unsigned short )0;
 sqlstm.sqtdso[16] = (unsigned short )0;
 sqlstm.sqhstv[17] = (unsigned char  *)szSenderKey;
 sqlstm.sqhstl[17] = (unsigned long )41;
 sqlstm.sqhsts[17] = (         int  )0;
 sqlstm.sqindv[17] = (         short *)0;
 sqlstm.sqinds[17] = (         int  )0;
 sqlstm.sqharm[17] = (unsigned long )0;
 sqlstm.sqadto[17] = (unsigned short )0;
 sqlstm.sqtdso[17] = (unsigned short )0;
 sqlstm.sqhstv[18] = (unsigned char  *)szChatBubbleType;
 sqlstm.sqhstl[18] = (unsigned long )31;
 sqlstm.sqhsts[18] = (         int  )0;
 sqlstm.sqindv[18] = (         short *)0;
 sqlstm.sqinds[18] = (         int  )0;
 sqlstm.sqharm[18] = (unsigned long )0;
 sqlstm.sqadto[18] = (unsigned short )0;
 sqlstm.sqtdso[18] = (unsigned short )0;
 sqlstm.sqhstv[19] = (unsigned char  *)szTargeting;
 sqlstm.sqhstl[19] = (unsigned long )2;
 sqlstm.sqhsts[19] = (         int  )0;
 sqlstm.sqindv[19] = (         short *)0;
 sqlstm.sqinds[19] = (         int  )0;
 sqlstm.sqharm[19] = (unsigned long )0;
 sqlstm.sqadto[19] = (unsigned short )0;
 sqlstm.sqtdso[19] = (unsigned short )0;
 sqlstm.sqhstv[20] = (unsigned char  *)szTmplCd;
 sqlstm.sqhstl[20] = (unsigned long )21;
 sqlstm.sqhsts[20] = (         int  )0;
 sqlstm.sqindv[20] = (         short *)0;
 sqlstm.sqinds[20] = (         int  )0;
 sqlstm.sqharm[20] = (unsigned long )0;
 sqlstm.sqadto[20] = (unsigned short )0;
 sqlstm.sqtdso[20] = (unsigned short )0;
 sqlstm.sqhstv[21] = (unsigned char  *)szRepFlag;
 sqlstm.sqhstl[21] = (unsigned long )2;
 sqlstm.sqhsts[21] = (         int  )0;
 sqlstm.sqindv[21] = (         short *)0;
 sqlstm.sqinds[21] = (         int  )0;
 sqlstm.sqharm[21] = (unsigned long )0;
 sqlstm.sqadto[21] = (unsigned short )0;
 sqlstm.sqtdso[21] = (unsigned short )0;
 sqlstm.sqhstv[22] = (unsigned char  *)szRepTitle;
 sqlstm.sqhstl[22] = (unsigned long )65;
 sqlstm.sqhsts[22] = (         int  )0;
 sqlstm.sqindv[22] = (         short *)0;
 sqlstm.sqinds[22] = (         int  )0;
 sqlstm.sqharm[22] = (unsigned long )0;
 sqlstm.sqadto[22] = (unsigned short )0;
 sqlstm.sqtdso[22] = (unsigned short )0;
 sqlstm.sqhstv[23] = (unsigned char  *)szRepText;
 sqlstm.sqhstl[23] = (unsigned long )501;
 sqlstm.sqhsts[23] = (         int  )0;
 sqlstm.sqindv[23] = (         short *)0;
 sqlstm.sqinds[23] = (         int  )0;
 sqlstm.sqharm[23] = (unsigned long )0;
 sqlstm.sqadto[23] = (unsigned short )0;
 sqlstm.sqtdso[23] = (unsigned short )0;
 sqlstm.sqhstv[24] = (unsigned char  *)szAppUserId;
 sqlstm.sqhstl[24] = (unsigned long )21;
 sqlstm.sqhsts[24] = (         int  )0;
 sqlstm.sqindv[24] = (         short *)0;
 sqlstm.sqinds[24] = (         int  )0;
 sqlstm.sqharm[24] = (unsigned long )0;
 sqlstm.sqadto[24] = (unsigned short )0;
 sqlstm.sqtdso[24] = (unsigned short )0;
 sqlstm.sqhstv[25] = (unsigned char  *)szPushAlarm;
 sqlstm.sqhstl[25] = (unsigned long )2;
 sqlstm.sqhsts[25] = (         int  )0;
 sqlstm.sqindv[25] = (         short *)0;
 sqlstm.sqinds[25] = (         int  )0;
 sqlstm.sqharm[25] = (unsigned long )0;
 sqlstm.sqadto[25] = (unsigned short )0;
 sqlstm.sqtdso[25] = (unsigned short )0;
 sqlstm.sqhstv[26] = (unsigned char  *)szMessageVariable;
 sqlstm.sqhstl[26] = (unsigned long )2001;
 sqlstm.sqhsts[26] = (         int  )0;
 sqlstm.sqindv[26] = (         short *)0;
 sqlstm.sqinds[26] = (         int  )0;
 sqlstm.sqharm[26] = (unsigned long )0;
 sqlstm.sqadto[26] = (unsigned short )0;
 sqlstm.sqtdso[26] = (unsigned short )0;
 sqlstm.sqhstv[27] = (unsigned char  *)szButtonVariable;
 sqlstm.sqhstl[27] = (unsigned long )2001;
 sqlstm.sqhsts[27] = (         int  )0;
 sqlstm.sqindv[27] = (         short *)0;
 sqlstm.sqinds[27] = (         int  )0;
 sqlstm.sqharm[27] = (unsigned long )0;
 sqlstm.sqadto[27] = (unsigned short )0;
 sqlstm.sqtdso[27] = (unsigned short )0;
 sqlstm.sqhstv[28] = (unsigned char  *)szCouponVariable;
 sqlstm.sqhstl[28] = (unsigned long )1001;
 sqlstm.sqhsts[28] = (         int  )0;
 sqlstm.sqindv[28] = (         short *)0;
 sqlstm.sqinds[28] = (         int  )0;
 sqlstm.sqharm[28] = (unsigned long )0;
 sqlstm.sqadto[28] = (unsigned short )0;
 sqlstm.sqtdso[28] = (unsigned short )0;
 sqlstm.sqhstv[29] = (unsigned char  *)szImageVariable;
 sqlstm.sqhstl[29] = (unsigned long )1001;
 sqlstm.sqhsts[29] = (         int  )0;
 sqlstm.sqindv[29] = (         short *)0;
 sqlstm.sqinds[29] = (         int  )0;
 sqlstm.sqharm[29] = (unsigned long )0;
 sqlstm.sqadto[29] = (unsigned short )0;
 sqlstm.sqtdso[29] = (unsigned short )0;
 sqlstm.sqhstv[30] = (unsigned char  *)szVideoVariable;
 sqlstm.sqhstl[30] = (unsigned long )1001;
 sqlstm.sqhsts[30] = (         int  )0;
 sqlstm.sqindv[30] = (         short *)0;
 sqlstm.sqinds[30] = (         int  )0;
 sqlstm.sqharm[30] = (unsigned long )0;
 sqlstm.sqadto[30] = (unsigned short )0;
 sqlstm.sqtdso[30] = (unsigned short )0;
 sqlstm.sqhstv[31] = (unsigned char  *)szCommerceVariable;
 sqlstm.sqhstl[31] = (unsigned long )1001;
 sqlstm.sqhsts[31] = (         int  )0;
 sqlstm.sqindv[31] = (         short *)0;
 sqlstm.sqinds[31] = (         int  )0;
 sqlstm.sqharm[31] = (unsigned long )0;
 sqlstm.sqadto[31] = (unsigned short )0;
 sqlstm.sqtdso[31] = (unsigned short )0;
 sqlstm.sqhstv[32] = (unsigned char  *)szCarouselVariable;
 sqlstm.sqhstl[32] = (unsigned long )2001;
 sqlstm.sqhsts[32] = (         int  )0;
 sqlstm.sqindv[32] = (         short *)0;
 sqlstm.sqinds[32] = (         int  )0;
 sqlstm.sqharm[32] = (unsigned long )0;
 sqlstm.sqadto[32] = (unsigned short )0;
 sqlstm.sqtdso[32] = (unsigned short )0;
 sqlstm.sqhstv[33] = (unsigned char  *)&nSqlCode;
 sqlstm.sqhstl[33] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[33] = (         int  )0;
 sqlstm.sqindv[33] = (         short *)0;
 sqlstm.sqinds[33] = (         int  )0;
 sqlstm.sqharm[33] = (unsigned long )0;
 sqlstm.sqadto[33] = (unsigned short )0;
 sqlstm.sqtdso[33] = (unsigned short )0;
 sqlstm.sqhstv[34] = (unsigned char  *)szSqlErrorMsg;
 sqlstm.sqhstl[34] = (unsigned long )1024;
 sqlstm.sqhsts[34] = (         int  )0;
 sqlstm.sqindv[34] = (         short *)0;
 sqlstm.sqinds[34] = (         int  )0;
 sqlstm.sqharm[34] = (unsigned long )0;
 sqlstm.sqadto[34] = (unsigned short )0;
 sqlstm.sqtdso[34] = (unsigned short )0;
 sqlstm.sqphsv = sqlstm.sqhstv;
 sqlstm.sqphsl = sqlstm.sqhstl;
 sqlstm.sqphss = sqlstm.sqhsts;
 sqlstm.sqpind = sqlstm.sqindv;
 sqlstm.sqpins = sqlstm.sqinds;
 sqlstm.sqparm = sqlstm.sqharm;
 sqlstm.sqparc = sqlstm.sqharc;
 sqlstm.sqpadto = sqlstm.sqadto;
 sqlstm.sqptdso = sqlstm.sqtdso;
 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSTBL failed - MMSID[%s] CID[%s] sqlcode[%d] sqlmsg[%s]",
				cMMSId,szCid,sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc)));
			_logPrint(_DATALOG, tmpLog3);
			return -1;
		}
		sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSTBL invaled failed - MMSID[%s]CID[%s]otReuslt[%d]errMsg[%s]",
			cMMSId,szCid,nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
    
	return 1;
}

int CDatabaseORA::setMMSMSG(CSenderDbMMSMSG &que_data)
{
	/* EXEC SQL BEGIN DECLARE SECTION; */ 

	char szSqlErrorMsg[1024];
	int nSqlCode = -999;

	char szQName[32+1];
	int nQNum;
	int nPriority;
	int nCtnId;
	char szCallBack[ 16+1];
	char szDstAddr [ 16+1];
	char szMsgTitle[ 64+1];
	int nCntType;
	char szTxtPath [256+1];
	int nRgnRate;
	int nInterval;
	
	char cMMSId[20];
	/* EXEC SQL END DECLARE SECTION; */ 


	memset(szSqlErrorMsg, 0x00, sizeof(szSqlErrorMsg));
	memset(szQName		, 0x00, sizeof(szQName	    ));
	memset(szCallBack	, 0x00, sizeof(szCallBack	));
	memset(szDstAddr	, 0x00, sizeof(szDstAddr	));
	memset(szMsgTitle	, 0x00, sizeof(szMsgTitle	));
	memset(szTxtPath	, 0x00, sizeof(szTxtPath	));
	memset(cMMSId       , 0x00, sizeof(cMMSId		));

	nQNum     = 1;
	nQNum     = atoi(que_data.szQName);
	nPriority = que_data.nPriority;
	nCtnId    = que_data.nCtnId;
	strcpy(szCallBack, que_data.szCallBack);
	strcpy(szDstAddr , que_data.szDstAddr );
	sprintf(szMsgTitle, "%.*s", sizeof(szMsgTitle)-1, que_data.szMsgTitle);
	nCntType  = que_data.nCntType;
	strcpy(szTxtPath , que_data.szTxtPath );
	nRgnRate  = que_data.nRgnRate;
	nInterval = que_data.nInterval;
	
	sprintf(cMMSId   , "%lld", que_data.nMMSId);
	//sprintf(tmpLog3, "[DBG] setMMSMSG MMSID[%s]",cMMSId);
	//_logPrint(_DATALOG, tmpLog3);

	/* EXEC SQL EXECUTE
		BEGIN
			proc_set_mms_msg2(in_q_num    =>:nQNum
							 ,in_priority =>:nPriority
							 ,in_ctn_id   =>:nCtnId
							 ,in_callback =>:szCallBack
							 ,in_dst_addr =>:szDstAddr
							 ,in_msgtitle =>:szMsgTitle
							 ,in_cnt_type =>:nCntType
							 ,in_txt_path =>:szTxtPath
							 ,in_rgn_rate =>:nRgnRate
							 ,in_interval =>:nInterval
							 ,in_mms_id   =>:cMMSId
							 ,ot_sqlcode  =>:nSqlCode
							 ,ot_sqlmsg   =>:szSqlErrorMsg
							 );

		END;
	END-EXEC; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 35;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.stmt = "begin proc_set_mms_msg2 ( in_q_num => :nQNum , in_priority =\
> :nPriority , in_ctn_id => :nCtnId , in_callback => :szCallBack , in_dst_addr\
 => :szDstAddr , in_msgtitle => :szMsgTitle , in_cnt_type => :nCntType , in_tx\
t_path => :szTxtPath , in_rgn_rate => :nRgnRate , in_interval => :nInterval , \
in_mms_id => :cMMSId , ot_sqlcode => :nSqlCode , ot_sqlmsg => :szSqlErrorMsg )\
 ; END ;";
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )310;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlstm.sqhstv[0] = (unsigned char  *)&nQNum;
 sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[0] = (         int  )0;
 sqlstm.sqindv[0] = (         short *)0;
 sqlstm.sqinds[0] = (         int  )0;
 sqlstm.sqharm[0] = (unsigned long )0;
 sqlstm.sqadto[0] = (unsigned short )0;
 sqlstm.sqtdso[0] = (unsigned short )0;
 sqlstm.sqhstv[1] = (unsigned char  *)&nPriority;
 sqlstm.sqhstl[1] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[1] = (         int  )0;
 sqlstm.sqindv[1] = (         short *)0;
 sqlstm.sqinds[1] = (         int  )0;
 sqlstm.sqharm[1] = (unsigned long )0;
 sqlstm.sqadto[1] = (unsigned short )0;
 sqlstm.sqtdso[1] = (unsigned short )0;
 sqlstm.sqhstv[2] = (unsigned char  *)&nCtnId;
 sqlstm.sqhstl[2] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[2] = (         int  )0;
 sqlstm.sqindv[2] = (         short *)0;
 sqlstm.sqinds[2] = (         int  )0;
 sqlstm.sqharm[2] = (unsigned long )0;
 sqlstm.sqadto[2] = (unsigned short )0;
 sqlstm.sqtdso[2] = (unsigned short )0;
 sqlstm.sqhstv[3] = (unsigned char  *)szCallBack;
 sqlstm.sqhstl[3] = (unsigned long )17;
 sqlstm.sqhsts[3] = (         int  )0;
 sqlstm.sqindv[3] = (         short *)0;
 sqlstm.sqinds[3] = (         int  )0;
 sqlstm.sqharm[3] = (unsigned long )0;
 sqlstm.sqadto[3] = (unsigned short )0;
 sqlstm.sqtdso[3] = (unsigned short )0;
 sqlstm.sqhstv[4] = (unsigned char  *)szDstAddr;
 sqlstm.sqhstl[4] = (unsigned long )17;
 sqlstm.sqhsts[4] = (         int  )0;
 sqlstm.sqindv[4] = (         short *)0;
 sqlstm.sqinds[4] = (         int  )0;
 sqlstm.sqharm[4] = (unsigned long )0;
 sqlstm.sqadto[4] = (unsigned short )0;
 sqlstm.sqtdso[4] = (unsigned short )0;
 sqlstm.sqhstv[5] = (unsigned char  *)szMsgTitle;
 sqlstm.sqhstl[5] = (unsigned long )65;
 sqlstm.sqhsts[5] = (         int  )0;
 sqlstm.sqindv[5] = (         short *)0;
 sqlstm.sqinds[5] = (         int  )0;
 sqlstm.sqharm[5] = (unsigned long )0;
 sqlstm.sqadto[5] = (unsigned short )0;
 sqlstm.sqtdso[5] = (unsigned short )0;
 sqlstm.sqhstv[6] = (unsigned char  *)&nCntType;
 sqlstm.sqhstl[6] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[6] = (         int  )0;
 sqlstm.sqindv[6] = (         short *)0;
 sqlstm.sqinds[6] = (         int  )0;
 sqlstm.sqharm[6] = (unsigned long )0;
 sqlstm.sqadto[6] = (unsigned short )0;
 sqlstm.sqtdso[6] = (unsigned short )0;
 sqlstm.sqhstv[7] = (unsigned char  *)szTxtPath;
 sqlstm.sqhstl[7] = (unsigned long )257;
 sqlstm.sqhsts[7] = (         int  )0;
 sqlstm.sqindv[7] = (         short *)0;
 sqlstm.sqinds[7] = (         int  )0;
 sqlstm.sqharm[7] = (unsigned long )0;
 sqlstm.sqadto[7] = (unsigned short )0;
 sqlstm.sqtdso[7] = (unsigned short )0;
 sqlstm.sqhstv[8] = (unsigned char  *)&nRgnRate;
 sqlstm.sqhstl[8] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[8] = (         int  )0;
 sqlstm.sqindv[8] = (         short *)0;
 sqlstm.sqinds[8] = (         int  )0;
 sqlstm.sqharm[8] = (unsigned long )0;
 sqlstm.sqadto[8] = (unsigned short )0;
 sqlstm.sqtdso[8] = (unsigned short )0;
 sqlstm.sqhstv[9] = (unsigned char  *)&nInterval;
 sqlstm.sqhstl[9] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[9] = (         int  )0;
 sqlstm.sqindv[9] = (         short *)0;
 sqlstm.sqinds[9] = (         int  )0;
 sqlstm.sqharm[9] = (unsigned long )0;
 sqlstm.sqadto[9] = (unsigned short )0;
 sqlstm.sqtdso[9] = (unsigned short )0;
 sqlstm.sqhstv[10] = (unsigned char  *)cMMSId;
 sqlstm.sqhstl[10] = (unsigned long )20;
 sqlstm.sqhsts[10] = (         int  )0;
 sqlstm.sqindv[10] = (         short *)0;
 sqlstm.sqinds[10] = (         int  )0;
 sqlstm.sqharm[10] = (unsigned long )0;
 sqlstm.sqadto[10] = (unsigned short )0;
 sqlstm.sqtdso[10] = (unsigned short )0;
 sqlstm.sqhstv[11] = (unsigned char  *)&nSqlCode;
 sqlstm.sqhstl[11] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[11] = (         int  )0;
 sqlstm.sqindv[11] = (         short *)0;
 sqlstm.sqinds[11] = (         int  )0;
 sqlstm.sqharm[11] = (unsigned long )0;
 sqlstm.sqadto[11] = (unsigned short )0;
 sqlstm.sqtdso[11] = (unsigned short )0;
 sqlstm.sqhstv[12] = (unsigned char  *)szSqlErrorMsg;
 sqlstm.sqhstl[12] = (unsigned long )1024;
 sqlstm.sqhsts[12] = (         int  )0;
 sqlstm.sqindv[12] = (         short *)0;
 sqlstm.sqinds[12] = (         int  )0;
 sqlstm.sqharm[12] = (unsigned long )0;
 sqlstm.sqadto[12] = (unsigned short )0;
 sqlstm.sqtdso[12] = (unsigned short )0;
 sqlstm.sqphsv = sqlstm.sqhstv;
 sqlstm.sqphsl = sqlstm.sqhstl;
 sqlstm.sqphss = sqlstm.sqhsts;
 sqlstm.sqpind = sqlstm.sqindv;
 sqlstm.sqpins = sqlstm.sqinds;
 sqlstm.sqparm = sqlstm.sqharm;
 sqlstm.sqparc = sqlstm.sqharc;
 sqlstm.sqpadto = sqlstm.sqadto;
 sqlstm.sqptdso = sqlstm.sqtdso;
 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}


	//sprintf(tmpLog3, "[INF] CDatabaseORA::setMMSCTNTBL - CallBack[%s]DstAddr[]TxtPath[%s]",szCallBack, szTxtPath);
	//_logPrint(_DATALOG, tmpLog3);
	
	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSCTNTBL exec failed - MMSID[%s] sqlcode[%d] sqlmsg[%s]",cMMSId,sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,strlen(sqlca.sqlerrm.sqlerrmc)));
			_logPrint(_DATALOG, tmpLog3);
			return -1;
		}
		sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSCTNTBL invaled failed - MMSID[%s]otReuslt[%d]errMsg[%s]",cMMSId,nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
		
    return 1;
}

int CDatabaseORA::getReportDB(CReportDbInfo &rpt_data)
{
	/* EXEC SQL BEGIN DECLARE SECTION; */ 

	int nJobCode = -1;
	int nSqlCode = -1;	
	int nCnt     = -1;	
	int nPtnId   = -1;
	int nTelcoId = -1;
	
	long long nMsgId = -1;
	
	char szPtnsn      [16+1];
	char szResCode    [16+1];
	char szResvData   [ 512];
	char szEndTelco   [   8];
	char szMsgId      [16+1];
	char szRptDate    [  16];
	char szSqlErrorMsg[1024];
	char szAppName    [  16];
	char szSerial     [16+1];
	char szDstadr     [16+1];
	char szCid        [12+1];	/*  ptnid&jobcode ´eA¼ */
	/* EXEC SQL END DECLARE SECTION; */ 


	memset(szPtnsn       ,0x00, sizeof(szPtnsn      ));
	memset(szResCode     ,0x00, sizeof(szResCode    ));
	memset(szResvData    ,0x00, sizeof(szResvData   ));
	memset(szDstadr      ,0x00, sizeof(szDstadr     ));
	memset(szEndTelco    ,0x00, sizeof(szEndTelco   ));
	memset(szRptDate     ,0x00, sizeof(szRptDate    ));
	memset(szSqlErrorMsg ,0x00, sizeof(szSqlErrorMsg));
	memset(szAppName     ,0x00, sizeof(szAppName    ));
	memset(szCid         ,0x00, sizeof(szCid        ));
    memset(szMsgId       ,0x00, sizeof(szMsgId      ));

	strcpy(szAppName, rpt_data.szResvData);
	memcpy(szResCode, "Init",4);
    
	/* EXEC SQL EXECUTE
		BEGIN
			proc_get_mms_report( in_appname       =>:szAppName
            					,ot_ptn_sn        =>:szPtnsn
            					,ot_res_code      =>:szResCode
            					,ot_telco_id      =>:nTelcoId
            					,ot_end_telco     =>:szEndTelco
            					,ot_msg_id        =>:szMsgId
            					,ot_resv_data     =>:szResvData
            					,ot_dstaddr       =>:szDstadr
            					,ot_rpt_telco_date=>:szRptDate
            					,ot_cid           =>:szCid 
            					,ot_cnt           =>:nCnt
            					,ot_sqlcode       =>:nSqlCode
            					,ot_sqlmsg        =>:szSqlErrorMsg
            					); 
		END;
	END-EXEC; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 35;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.stmt = "begin proc_get_mms_report ( in_appname => :szAppName , ot_pt\
n_sn => :szPtnsn , ot_res_code => :szResCode , ot_telco_id => :nTelcoId , ot_e\
nd_telco => :szEndTelco , ot_msg_id => :szMsgId , ot_resv_data => :szResvData \
, ot_dstaddr => :szDstadr , ot_rpt_telco_date => :szRptDate , ot_cid => :szCid\
 , ot_cnt => :nCnt , ot_sqlcode => :nSqlCode , ot_sqlmsg => :szSqlErrorMsg ) ;\
 END ;";
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )377;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlstm.sqhstv[0] = (unsigned char  *)szAppName;
 sqlstm.sqhstl[0] = (unsigned long )16;
 sqlstm.sqhsts[0] = (         int  )0;
 sqlstm.sqindv[0] = (         short *)0;
 sqlstm.sqinds[0] = (         int  )0;
 sqlstm.sqharm[0] = (unsigned long )0;
 sqlstm.sqadto[0] = (unsigned short )0;
 sqlstm.sqtdso[0] = (unsigned short )0;
 sqlstm.sqhstv[1] = (unsigned char  *)szPtnsn;
 sqlstm.sqhstl[1] = (unsigned long )17;
 sqlstm.sqhsts[1] = (         int  )0;
 sqlstm.sqindv[1] = (         short *)0;
 sqlstm.sqinds[1] = (         int  )0;
 sqlstm.sqharm[1] = (unsigned long )0;
 sqlstm.sqadto[1] = (unsigned short )0;
 sqlstm.sqtdso[1] = (unsigned short )0;
 sqlstm.sqhstv[2] = (unsigned char  *)szResCode;
 sqlstm.sqhstl[2] = (unsigned long )17;
 sqlstm.sqhsts[2] = (         int  )0;
 sqlstm.sqindv[2] = (         short *)0;
 sqlstm.sqinds[2] = (         int  )0;
 sqlstm.sqharm[2] = (unsigned long )0;
 sqlstm.sqadto[2] = (unsigned short )0;
 sqlstm.sqtdso[2] = (unsigned short )0;
 sqlstm.sqhstv[3] = (unsigned char  *)&nTelcoId;
 sqlstm.sqhstl[3] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[3] = (         int  )0;
 sqlstm.sqindv[3] = (         short *)0;
 sqlstm.sqinds[3] = (         int  )0;
 sqlstm.sqharm[3] = (unsigned long )0;
 sqlstm.sqadto[3] = (unsigned short )0;
 sqlstm.sqtdso[3] = (unsigned short )0;
 sqlstm.sqhstv[4] = (unsigned char  *)szEndTelco;
 sqlstm.sqhstl[4] = (unsigned long )8;
 sqlstm.sqhsts[4] = (         int  )0;
 sqlstm.sqindv[4] = (         short *)0;
 sqlstm.sqinds[4] = (         int  )0;
 sqlstm.sqharm[4] = (unsigned long )0;
 sqlstm.sqadto[4] = (unsigned short )0;
 sqlstm.sqtdso[4] = (unsigned short )0;
 sqlstm.sqhstv[5] = (unsigned char  *)szMsgId;
 sqlstm.sqhstl[5] = (unsigned long )17;
 sqlstm.sqhsts[5] = (         int  )0;
 sqlstm.sqindv[5] = (         short *)0;
 sqlstm.sqinds[5] = (         int  )0;
 sqlstm.sqharm[5] = (unsigned long )0;
 sqlstm.sqadto[5] = (unsigned short )0;
 sqlstm.sqtdso[5] = (unsigned short )0;
 sqlstm.sqhstv[6] = (unsigned char  *)szResvData;
 sqlstm.sqhstl[6] = (unsigned long )512;
 sqlstm.sqhsts[6] = (         int  )0;
 sqlstm.sqindv[6] = (         short *)0;
 sqlstm.sqinds[6] = (         int  )0;
 sqlstm.sqharm[6] = (unsigned long )0;
 sqlstm.sqadto[6] = (unsigned short )0;
 sqlstm.sqtdso[6] = (unsigned short )0;
 sqlstm.sqhstv[7] = (unsigned char  *)szDstadr;
 sqlstm.sqhstl[7] = (unsigned long )17;
 sqlstm.sqhsts[7] = (         int  )0;
 sqlstm.sqindv[7] = (         short *)0;
 sqlstm.sqinds[7] = (         int  )0;
 sqlstm.sqharm[7] = (unsigned long )0;
 sqlstm.sqadto[7] = (unsigned short )0;
 sqlstm.sqtdso[7] = (unsigned short )0;
 sqlstm.sqhstv[8] = (unsigned char  *)szRptDate;
 sqlstm.sqhstl[8] = (unsigned long )16;
 sqlstm.sqhsts[8] = (         int  )0;
 sqlstm.sqindv[8] = (         short *)0;
 sqlstm.sqinds[8] = (         int  )0;
 sqlstm.sqharm[8] = (unsigned long )0;
 sqlstm.sqadto[8] = (unsigned short )0;
 sqlstm.sqtdso[8] = (unsigned short )0;
 sqlstm.sqhstv[9] = (unsigned char  *)szCid;
 sqlstm.sqhstl[9] = (unsigned long )13;
 sqlstm.sqhsts[9] = (         int  )0;
 sqlstm.sqindv[9] = (         short *)0;
 sqlstm.sqinds[9] = (         int  )0;
 sqlstm.sqharm[9] = (unsigned long )0;
 sqlstm.sqadto[9] = (unsigned short )0;
 sqlstm.sqtdso[9] = (unsigned short )0;
 sqlstm.sqhstv[10] = (unsigned char  *)&nCnt;
 sqlstm.sqhstl[10] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[10] = (         int  )0;
 sqlstm.sqindv[10] = (         short *)0;
 sqlstm.sqinds[10] = (         int  )0;
 sqlstm.sqharm[10] = (unsigned long )0;
 sqlstm.sqadto[10] = (unsigned short )0;
 sqlstm.sqtdso[10] = (unsigned short )0;
 sqlstm.sqhstv[11] = (unsigned char  *)&nSqlCode;
 sqlstm.sqhstl[11] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[11] = (         int  )0;
 sqlstm.sqindv[11] = (         short *)0;
 sqlstm.sqinds[11] = (         int  )0;
 sqlstm.sqharm[11] = (unsigned long )0;
 sqlstm.sqadto[11] = (unsigned short )0;
 sqlstm.sqtdso[11] = (unsigned short )0;
 sqlstm.sqhstv[12] = (unsigned char  *)szSqlErrorMsg;
 sqlstm.sqhstl[12] = (unsigned long )1024;
 sqlstm.sqhsts[12] = (         int  )0;
 sqlstm.sqindv[12] = (         short *)0;
 sqlstm.sqinds[12] = (         int  )0;
 sqlstm.sqharm[12] = (unsigned long )0;
 sqlstm.sqadto[12] = (unsigned short )0;
 sqlstm.sqtdso[12] = (unsigned short )0;
 sqlstm.sqphsv = sqlstm.sqhstv;
 sqlstm.sqphsl = sqlstm.sqhstl;
 sqlstm.sqphss = sqlstm.sqhsts;
 sqlstm.sqpind = sqlstm.sqindv;
 sqlstm.sqpins = sqlstm.sqinds;
 sqlstm.sqparm = sqlstm.sqharm;
 sqlstm.sqparc = sqlstm.sqharc;
 sqlstm.sqpadto = sqlstm.sqadto;
 sqlstm.sqptdso = sqlstm.sqtdso;
 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



	if( memcmp(szResCode,"Init",4) == 0 )
	{
		sprintf(tmpLog3, "[ERR] CDatabaseORA::getReportDB - CID[%s]ptnSn[%s]sqlcode[%d] sqlmsg[%s][%s]"
                ,szCid
                ,szPtnsn
                ,sqlca.sqlcode
                ,trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc))
                ,szAppName);
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}

	/* 
	 * NO DATA FOUND AI °æ¿i SKIP
	 * szResCode '999'AI°æ¿iμμ AO¾i ¼oA¤
	 * szResCode¿¡ space Æ÷COμC¾iAO¾i trim A§A¡ ¿A¸²
	 */
	trim(szResCode	,sizeof(szResCode)	);// LSY 20131115 °a°uAUμa°ª trim Aß°¡
	if( memcmp(szResCode,"99", strlen(szResCode)) == 0 || memcmp(szResCode,"98", strlen(szResCode)) == 0 )
	{
		memcpy(rpt_data.szResCode	,"99"	,strlen(szResCode));
		return -1;
	}
	
	trim(szPtnsn	,sizeof(szPtnsn)	);
	trim(szResvData	,sizeof(szResvData)	);
	trim(szDstadr	,sizeof(szDstadr)	);
	trim(szEndTelco	,sizeof(szEndTelco)	);
	trim(szRptDate	,sizeof(szRptDate)	);
	
	strcpy(rpt_data.header.msgType,"2");

	memcpy(rpt_data.szPtnsn		,szPtnsn	,strlen(szPtnsn));
	memcpy(rpt_data.szResCode	,szResCode	,strlen(szResCode));
	memcpy(rpt_data.szAppName	,szAppName	,strlen(szAppName));
	
	rpt_data.nTelcoId 	= nTelcoId;
	
	memcpy(rpt_data.szResvData	,szResvData	,strlen(szResvData));
	memcpy(rpt_data.szDstAdr	,szDstadr	,strlen(szDstadr));
	memcpy(rpt_data.szEndTelco	,szEndTelco	,strlen(szEndTelco));
	
	rpt_data.nMsgId 	= atoll(szMsgId);
	
	memcpy(rpt_data.szRptDate	,szRptDate	,strlen(szRptDate));
	
	rpt_data.nCnt 		= nCnt;
	rpt_data.nSqlCode 	= nSqlCode;
	
	memcpy(rpt_data.szCid		,szCid		,strlen(szCid));
	
	/*
	rpt_data.nJobCode = nJobCode;
	rpt_data.nPtnId = nPtnId;
	*/
	
	return 1;
}

int CDatabaseORA::setRPTTBL(CSenderDbMMSRPTTBL &rpt_data)
{
	/* EXEC SQL BEGIN DECLARE SECTION; */ 


    char szSqlErrorMsg[1024];
    int nSqlCode = -999;

    long long mms_id;
    char msg_id[4096] = {0x00,};
    char snd_numb[4096] = {0x00,};
    char rcv_numb[4096] = {0x00,};
    int res_code;
    char res_text[4096] = {0x00,};
    int telco_id;
    int res_type;
    char end_telco[4096] = {0x00,};

    /* EXEC SQL END DECLARE SECTION; */ 


    //AE±aE­
    mms_id = 0;
    memset(msg_id, 0x00, sizeof(msg_id));
    memset(snd_numb, 0x00, sizeof(snd_numb));
    memset(rcv_numb, 0x00, sizeof(rcv_numb));
    res_code = 0;
    memset(res_text, 0x00, sizeof(res_text));
    telco_id = 0;
    res_type = 0;
    memset(end_telco, 0x00, sizeof(end_telco));

    //°ª ¼¼ÆA   
    mms_id = rpt_data.nMMSId;
    sprintf(msg_id, "%ld", rpt_data.nMMSId);
    sprintf(snd_numb, rpt_data.szCallBack);
    sprintf(rcv_numb, rpt_data.szDstAddr);
    res_code = rpt_data.res_code;
    sprintf(res_text, rpt_data.res_text);
    telco_id = 0;

	/* EXEC SQL EXECUTE
		BEGIN
        PROC_SET_RPT_FORCE(:mms_id, :msg_id, :snd_numb, :rcv_numb, :res_code,
                            :res_text, :telco_id, :res_type, :end_telco, :nSqlCode, :szSqlErrorMsg);
        END;
    END-EXEC; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 35;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.stmt = "begin PROC_SET_RPT_FORCE ( :mms_id , :msg_id , :snd_numb , :\
rcv_numb , :res_code , :res_text , :telco_id , :res_type , :end_telco , :nSqlC\
ode , :szSqlErrorMsg ) ; END ;";
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )444;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlstm.sqhstv[0] = (unsigned char  *)&mms_id;
 sqlstm.sqhstl[0] = (unsigned long )sizeof(long long);
 sqlstm.sqhsts[0] = (         int  )0;
 sqlstm.sqindv[0] = (         short *)0;
 sqlstm.sqinds[0] = (         int  )0;
 sqlstm.sqharm[0] = (unsigned long )0;
 sqlstm.sqadto[0] = (unsigned short )0;
 sqlstm.sqtdso[0] = (unsigned short )0;
 sqlstm.sqhstv[1] = (unsigned char  *)msg_id;
 sqlstm.sqhstl[1] = (unsigned long )4096;
 sqlstm.sqhsts[1] = (         int  )0;
 sqlstm.sqindv[1] = (         short *)0;
 sqlstm.sqinds[1] = (         int  )0;
 sqlstm.sqharm[1] = (unsigned long )0;
 sqlstm.sqadto[1] = (unsigned short )0;
 sqlstm.sqtdso[1] = (unsigned short )0;
 sqlstm.sqhstv[2] = (unsigned char  *)snd_numb;
 sqlstm.sqhstl[2] = (unsigned long )4096;
 sqlstm.sqhsts[2] = (         int  )0;
 sqlstm.sqindv[2] = (         short *)0;
 sqlstm.sqinds[2] = (         int  )0;
 sqlstm.sqharm[2] = (unsigned long )0;
 sqlstm.sqadto[2] = (unsigned short )0;
 sqlstm.sqtdso[2] = (unsigned short )0;
 sqlstm.sqhstv[3] = (unsigned char  *)rcv_numb;
 sqlstm.sqhstl[3] = (unsigned long )4096;
 sqlstm.sqhsts[3] = (         int  )0;
 sqlstm.sqindv[3] = (         short *)0;
 sqlstm.sqinds[3] = (         int  )0;
 sqlstm.sqharm[3] = (unsigned long )0;
 sqlstm.sqadto[3] = (unsigned short )0;
 sqlstm.sqtdso[3] = (unsigned short )0;
 sqlstm.sqhstv[4] = (unsigned char  *)&res_code;
 sqlstm.sqhstl[4] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[4] = (         int  )0;
 sqlstm.sqindv[4] = (         short *)0;
 sqlstm.sqinds[4] = (         int  )0;
 sqlstm.sqharm[4] = (unsigned long )0;
 sqlstm.sqadto[4] = (unsigned short )0;
 sqlstm.sqtdso[4] = (unsigned short )0;
 sqlstm.sqhstv[5] = (unsigned char  *)res_text;
 sqlstm.sqhstl[5] = (unsigned long )4096;
 sqlstm.sqhsts[5] = (         int  )0;
 sqlstm.sqindv[5] = (         short *)0;
 sqlstm.sqinds[5] = (         int  )0;
 sqlstm.sqharm[5] = (unsigned long )0;
 sqlstm.sqadto[5] = (unsigned short )0;
 sqlstm.sqtdso[5] = (unsigned short )0;
 sqlstm.sqhstv[6] = (unsigned char  *)&telco_id;
 sqlstm.sqhstl[6] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[6] = (         int  )0;
 sqlstm.sqindv[6] = (         short *)0;
 sqlstm.sqinds[6] = (         int  )0;
 sqlstm.sqharm[6] = (unsigned long )0;
 sqlstm.sqadto[6] = (unsigned short )0;
 sqlstm.sqtdso[6] = (unsigned short )0;
 sqlstm.sqhstv[7] = (unsigned char  *)&res_type;
 sqlstm.sqhstl[7] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[7] = (         int  )0;
 sqlstm.sqindv[7] = (         short *)0;
 sqlstm.sqinds[7] = (         int  )0;
 sqlstm.sqharm[7] = (unsigned long )0;
 sqlstm.sqadto[7] = (unsigned short )0;
 sqlstm.sqtdso[7] = (unsigned short )0;
 sqlstm.sqhstv[8] = (unsigned char  *)end_telco;
 sqlstm.sqhstl[8] = (unsigned long )4096;
 sqlstm.sqhsts[8] = (         int  )0;
 sqlstm.sqindv[8] = (         short *)0;
 sqlstm.sqinds[8] = (         int  )0;
 sqlstm.sqharm[8] = (unsigned long )0;
 sqlstm.sqadto[8] = (unsigned short )0;
 sqlstm.sqtdso[8] = (unsigned short )0;
 sqlstm.sqhstv[9] = (unsigned char  *)&nSqlCode;
 sqlstm.sqhstl[9] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[9] = (         int  )0;
 sqlstm.sqindv[9] = (         short *)0;
 sqlstm.sqinds[9] = (         int  )0;
 sqlstm.sqharm[9] = (unsigned long )0;
 sqlstm.sqadto[9] = (unsigned short )0;
 sqlstm.sqtdso[9] = (unsigned short )0;
 sqlstm.sqhstv[10] = (unsigned char  *)szSqlErrorMsg;
 sqlstm.sqhstl[10] = (unsigned long )1024;
 sqlstm.sqhsts[10] = (         int  )0;
 sqlstm.sqindv[10] = (         short *)0;
 sqlstm.sqinds[10] = (         int  )0;
 sqlstm.sqharm[10] = (unsigned long )0;
 sqlstm.sqadto[10] = (unsigned short )0;
 sqlstm.sqtdso[10] = (unsigned short )0;
 sqlstm.sqphsv = sqlstm.sqhstv;
 sqlstm.sqphsl = sqlstm.sqhstl;
 sqlstm.sqphss = sqlstm.sqhsts;
 sqlstm.sqpind = sqlstm.sqindv;
 sqlstm.sqpins = sqlstm.sqinds;
 sqlstm.sqparm = sqlstm.sqharm;
 sqlstm.sqparc = sqlstm.sqharc;
 sqlstm.sqpadto = sqlstm.sqadto;
 sqlstm.sqptdso = sqlstm.sqtdso;
 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



	if( nSqlCode != 0 )
    {
        if( nSqlCode == -999 )
        {
            sprintf(tmpLog3, "[ERR] CDatabaseORA::setRPTTBL exec failed - MMSID[%lld] sqlcode[%d] sqlmsg[%s]", mms_id, sqlca.sqlcode, trim(sqlca.sqlerrm.sqlerrmc, strlen(sqlca.sqlerrm.sqlerrmc)));
            _logPrint(_DATALOG, tmpLog3);
            return -1;
        }

        sprintf(tmpLog3, "[ERR] CDatabaseORA::setRPTTBL invaled failed - MMSID[%lld]otReuslt[%d]errMsg[%s]", mms_id, nSqlCode, trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
        _logPrint(_DATALOG, tmpLog3);
        return -1;
    }

	return 1;

}

char* CDatabaseORA::trim(char* szOrg, int leng)
{
    int i = 0;
    for (i=leng-1;i>=0;i--) {
        if( (szOrg[i]==0x20)||(szOrg[i]==' ')||(szOrg[i]==0x00) ) {
            szOrg[i] = 0x00;
        }
        else break;
    }
    return szOrg;
}

int CDatabaseORA::selectTblCallback(set<string>& set_callback_list, int _pid)
{

	/* EXEC SQL BEGIN DECLARE SECTION; */ 

	int pid;
	char callback[12+1];

    /* EXEC SQL END DECLARE SECTION; */ 


	pid = _pid;

	/* EXEC SQL WHENEVER SQLERROR CONTINUE; */ 

	
	/* EXEC SQL DECLARE cur_data CURSOR FOR
		SELECT CALLBACK
		FROM TBL_CALLBACK
		WHERE PTN_ID = :pid
	; */ 

	
	/* EXEC SQL OPEN cur_data; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 35;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.stmt = sq0011;
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )503;
 sqlstm.selerr = (unsigned short)1;
 sqlstm.sqlpfmem = (unsigned int  )0;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlstm.sqcmod = (unsigned int )0;
 sqlstm.sqhstv[0] = (unsigned char  *)&pid;
 sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[0] = (         int  )0;
 sqlstm.sqindv[0] = (         short *)0;
 sqlstm.sqinds[0] = (         int  )0;
 sqlstm.sqharm[0] = (unsigned long )0;
 sqlstm.sqadto[0] = (unsigned short )0;
 sqlstm.sqtdso[0] = (unsigned short )0;
 sqlstm.sqphsv = sqlstm.sqhstv;
 sqlstm.sqphsl = sqlstm.sqhstl;
 sqlstm.sqphss = sqlstm.sqhsts;
 sqlstm.sqpind = sqlstm.sqindv;
 sqlstm.sqpins = sqlstm.sqinds;
 sqlstm.sqparm = sqlstm.sqharm;
 sqlstm.sqparc = sqlstm.sqharc;
 sqlstm.sqpadto = sqlstm.sqadto;
 sqlstm.sqptdso = sqlstm.sqtdso;
 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



	if(sqlca.sqlcode!= 0)
	{
		sprintf(tmpLog3, "[ERR] CDatabaseORA::selectTblCallback exec failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode, trim(sqlca.sqlerrm.sqlerrmc, strlen(sqlca.sqlerrm.sqlerrmc)));
		_logPrint(_DATALOG, tmpLog3);
	}

	int nRet = -1;

	while(true)
	{
		memset(callback, 0x00, sizeof(callback));

		/* EXEC SQL FETCH cur_data INTO
			:callback 	
			; */ 

{
  struct sqlexd sqlstm;
  sqlstm.sqlvsn = 13;
  sqlstm.arrsiz = 35;
  sqlstm.sqladtp = &sqladt;
  sqlstm.sqltdsp = &sqltds;
  sqlstm.iters = (unsigned int  )1;
  sqlstm.offset = (unsigned int  )522;
  sqlstm.selerr = (unsigned short)1;
  sqlstm.sqlpfmem = (unsigned int  )0;
  sqlstm.cud = sqlcud0;
  sqlstm.sqlest = (unsigned char  *)&sqlca;
  sqlstm.sqlety = (unsigned short)4352;
  sqlstm.occurs = (unsigned int  )0;
  sqlstm.sqfoff = (         int )0;
  sqlstm.sqfmod = (unsigned int )2;
  sqlstm.sqhstv[0] = (unsigned char  *)callback;
  sqlstm.sqhstl[0] = (unsigned long )13;
  sqlstm.sqhsts[0] = (         int  )0;
  sqlstm.sqindv[0] = (         short *)0;
  sqlstm.sqinds[0] = (         int  )0;
  sqlstm.sqharm[0] = (unsigned long )0;
  sqlstm.sqadto[0] = (unsigned short )0;
  sqlstm.sqtdso[0] = (unsigned short )0;
  sqlstm.sqphsv = sqlstm.sqhstv;
  sqlstm.sqphsl = sqlstm.sqhstl;
  sqlstm.sqphss = sqlstm.sqhsts;
  sqlstm.sqpind = sqlstm.sqindv;
  sqlstm.sqpins = sqlstm.sqinds;
  sqlstm.sqparm = sqlstm.sqharm;
  sqlstm.sqparc = sqlstm.sqharc;
  sqlstm.sqpadto = sqlstm.sqadto;
  sqlstm.sqptdso = sqlstm.sqtdso;
  sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



		if(sqlca.sqlcode == 1403)
		{
			nRet = 1;
			break;
		}
		if(sqlca.sqlcode != 0)
		{
            sprintf(tmpLog3, "[ERR] CDatabaseORA::selectTblCallback exec failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode, trim(sqlca.sqlerrm.sqlerrmc, strlen(sqlca.sqlerrm.sqlerrmc)));
            _logPrint(_DATALOG, tmpLog3);
			return sqlca.sqlcode;
		}

		trim(callback, sizeof(callback));				
		set_callback_list.insert(callback);
	}

	/* EXEC SQL CLOSE cur_data; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 35;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )541;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}


	
	return nRet;
}

int CDatabaseORA::selectAllowDialCode(set<string>& set_dialcode_list, char *_dial_code_type)
{
	/* EXEC SQL BEGIN DECLARE SECTION; */ 

	char dial_code_type[4+1];
	char dial_code[5+1];
    /* EXEC SQL END DECLARE SECTION; */ 


	memset(dial_code_type, 0x00, sizeof(dial_code_type));
	snprintf(dial_code_type, sizeof(dial_code_type), _dial_code_type);	
	
	/* EXEC SQL WHENEVER SQLERROR CONTINUE; */ 

	/* EXEC SQL DECLARE cur CURSOR FOR
		SELECT DIAL_CODE
		FROM TBL_ALLOW_DIAL_CODE
		WHERE DIAL_CODE_TYPE = :dial_code_type
	; */ 

	
	/* EXEC SQL OPEN cur; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 35;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.stmt = sq0012;
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )556;
 sqlstm.selerr = (unsigned short)1;
 sqlstm.sqlpfmem = (unsigned int  )0;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlstm.sqcmod = (unsigned int )0;
 sqlstm.sqhstv[0] = (unsigned char  *)dial_code_type;
 sqlstm.sqhstl[0] = (unsigned long )5;
 sqlstm.sqhsts[0] = (         int  )0;
 sqlstm.sqindv[0] = (         short *)0;
 sqlstm.sqinds[0] = (         int  )0;
 sqlstm.sqharm[0] = (unsigned long )0;
 sqlstm.sqadto[0] = (unsigned short )0;
 sqlstm.sqtdso[0] = (unsigned short )0;
 sqlstm.sqphsv = sqlstm.sqhstv;
 sqlstm.sqphsl = sqlstm.sqhstl;
 sqlstm.sqphss = sqlstm.sqhsts;
 sqlstm.sqpind = sqlstm.sqindv;
 sqlstm.sqpins = sqlstm.sqinds;
 sqlstm.sqparm = sqlstm.sqharm;
 sqlstm.sqparc = sqlstm.sqharc;
 sqlstm.sqpadto = sqlstm.sqadto;
 sqlstm.sqptdso = sqlstm.sqtdso;
 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



	if(sqlca.sqlcode!= 0)
	{
		sprintf(tmpLog3, "[ERR] CDatabaseORA::selectTblCallback exec failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode, trim(sqlca.sqlerrm.sqlerrmc, strlen(sqlca.sqlerrm.sqlerrmc)));
		_logPrint(_DATALOG, tmpLog3);
	}

	int nRet = -1;

	while(true)
	{
		memset(dial_code, 0x00, sizeof(dial_code));

		/* EXEC SQL FETCH cur INTO
			:dial_code
			; */ 

{
  struct sqlexd sqlstm;
  sqlstm.sqlvsn = 13;
  sqlstm.arrsiz = 35;
  sqlstm.sqladtp = &sqladt;
  sqlstm.sqltdsp = &sqltds;
  sqlstm.iters = (unsigned int  )1;
  sqlstm.offset = (unsigned int  )575;
  sqlstm.selerr = (unsigned short)1;
  sqlstm.sqlpfmem = (unsigned int  )0;
  sqlstm.cud = sqlcud0;
  sqlstm.sqlest = (unsigned char  *)&sqlca;
  sqlstm.sqlety = (unsigned short)4352;
  sqlstm.occurs = (unsigned int  )0;
  sqlstm.sqfoff = (         int )0;
  sqlstm.sqfmod = (unsigned int )2;
  sqlstm.sqhstv[0] = (unsigned char  *)dial_code;
  sqlstm.sqhstl[0] = (unsigned long )6;
  sqlstm.sqhsts[0] = (         int  )0;
  sqlstm.sqindv[0] = (         short *)0;
  sqlstm.sqinds[0] = (         int  )0;
  sqlstm.sqharm[0] = (unsigned long )0;
  sqlstm.sqadto[0] = (unsigned short )0;
  sqlstm.sqtdso[0] = (unsigned short )0;
  sqlstm.sqphsv = sqlstm.sqhstv;
  sqlstm.sqphsl = sqlstm.sqhstl;
  sqlstm.sqphss = sqlstm.sqhsts;
  sqlstm.sqpind = sqlstm.sqindv;
  sqlstm.sqpins = sqlstm.sqinds;
  sqlstm.sqparm = sqlstm.sqharm;
  sqlstm.sqparc = sqlstm.sqharc;
  sqlstm.sqpadto = sqlstm.sqadto;
  sqlstm.sqptdso = sqlstm.sqtdso;
  sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



		if(sqlca.sqlcode == 1403)
		{
			nRet = 1;
			break;
		}

		if(sqlca.sqlcode != 0)
		{
            sprintf(tmpLog3, "[ERR] CDatabaseORA::selectAllowDialCode  exec failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode, trim(sqlca.sqlerrm.sqlerrmc, strlen(sqlca.sqlerrm.sqlerrmc)));
            _logPrint(_DATALOG, tmpLog3);
			return sqlca.sqlcode;
		}

		trim(dial_code, sizeof(dial_code));
		set_dialcode_list.insert(dial_code);
	}

	/* EXEC SQL CLOSE cur; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 35;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )594;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



	return nRet;
}

//int CDatabaseORA::getMMSID()
long long CDatabaseORA::getMMSID()
{       
    /* EXEC SQL BEGIN DECLARE SECTION; */ 

    char szSqlErrorMsg[1024];
    char szCid[10+1]; 
    //int nMMSID = 0;
    long long nMMSID = 0;
    int nSqlCode = -999;
    /* EXEC SQL END DECLARE SECTION; */ 


    memset(szSqlErrorMsg,0x00,sizeof(szSqlErrorMsg));       //CCL(szSqlErrorMsg);
    memset(szCid        ,0x00,sizeof(szCid        ));       //CCL(szCid);    
    
    memset(szCid, 0x00, 10);
        
    /* EXEC SQL EXECUTE
        BEGIN
            proc_get_mms_id( in_cid     =>:szCid
                            ,ot_mms_id  =>:nMMSID
                            ,ot_sqlcode =>:nSqlCode
                            ,ot_sqlmsg  =>:szSqlErrorMsg
                            );
        END;
    END-EXEC; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 35;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "begin proc_get_mms_id ( in_cid => :szCid , ot_mms_id => :\
nMMSID , ot_sqlcode => :nSqlCode , ot_sqlmsg => :szSqlErrorMsg ) ; END ;";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )609;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)szCid;
    sqlstm.sqhstl[0] = (unsigned long )11;
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&nMMSID;
    sqlstm.sqhstl[1] = (unsigned long )sizeof(long long);
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)&nSqlCode;
    sqlstm.sqhstl[2] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqhstv[3] = (unsigned char  *)szSqlErrorMsg;
    sqlstm.sqhstl[3] = (unsigned long )1024;
    sqlstm.sqhsts[3] = (         int  )0;
    sqlstm.sqindv[3] = (         short *)0;
    sqlstm.sqinds[3] = (         int  )0;
    sqlstm.sqharm[3] = (unsigned long )0;
    sqlstm.sqadto[3] = (unsigned short )0;
    sqlstm.sqtdso[3] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}


    if( nSqlCode != 0 )
    {
        if( nSqlCode == -999 )
        {
            sprintf(tmpLog3, "[ERR] CDatabaseORA::getMMSID failed - MMSID[%lld] CID[%s] sqlcode[%d] sqlmsg[%s]",nMMSID,szCid,sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc)))
;
            _logPrint(_DATALOG, tmpLog3);
            return -1;
        }
        sprintf(tmpLog3, "[ERR] dCDatabaseORA::getMMSID invaled failed - MMSID[%lld]CID[%s]otReuslt[%d]errMsg[%s]",nMMSID,szCid,nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
        _logPrint(_DATALOG, tmpLog3);
        return -1;
    }

    return nMMSID;
}

int CDatabaseORA::setMMSMSG_TALK(CSenderDbMMSMSG_TALK &que_data)
{   
    /* EXEC SQL BEGIN DECLARE SECTION; */ 

    char szSqlErrorMsg[1024];
    int nSqlCode = -999;
    
    int nQNum;
    int nPriority;
    char szSenderKey[40+1];
    char szDstAddr[12+1];
    char szTmplCd[10+1];
    char szMsgBody[2000+1];
    
    char cMMSId[20];
    int nMMSId;
    /* EXEC SQL END DECLARE SECTION; */ 

            
    memset(szSqlErrorMsg, 0x00, sizeof(szSqlErrorMsg));
    memset(szSenderKey, 0x00, sizeof(szSenderKey));
    memset(szDstAddr, 0x00, sizeof(szDstAddr));
    memset(szTmplCd, 0x00, sizeof(szTmplCd));
    memset(szMsgBody, 0x00, sizeof(szMsgBody));
    memset(cMMSId, 0x00, sizeof(cMMSId));
    
    nQNum     = 1;
    nQNum     = atoi(que_data.szQName);
    nPriority = que_data.nPriority;

    strcpy(szSenderKey, que_data.szSenderKey);
    strcpy(szDstAddr, que_data.szDstAddr);
    strcpy(szTmplCd, que_data.szTmplCd);
    strcpy(szMsgBody, que_data.szMsgBody);

    nMMSId  = que_data.nMMSId;

    sprintf(cMMSId, "%lld", que_data.nMMSId);

    /* EXEC SQL EXECUTE
        BEGIN
            PROC_SET_MMS_MSG_TALK(in_q_num       =>:nQNum
                                 ,in_priority    =>:nPriority
                                 ,in_sender_key  =>:szSenderKey
                                 ,in_dst_addr    =>:szDstAddr
                                 ,in_template_cd =>:szTmplCd
                                 ,in_msg_body    =>:szMsgBody
                                 ,in_mms_id      =>:cMMSId
                                 ,ot_sqlcode     =>:nSqlCode
                                 ,ot_sqlmsg      =>:szSqlErrorMsg
                                 );

        END;
    END-EXEC; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 35;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "begin PROC_SET_MMS_MSG_TALK ( in_q_num => :nQNum , in_pri\
ority => :nPriority , in_sender_key => :szSenderKey , in_dst_addr => :szDstAdd\
r , in_template_cd => :szTmplCd , in_msg_body => :szMsgBody , in_mms_id => :cM\
MSId , ot_sqlcode => :nSqlCode , ot_sqlmsg => :szSqlErrorMsg ) ; END ;";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )640;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&nQNum;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&nPriority;
    sqlstm.sqhstl[1] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)szSenderKey;
    sqlstm.sqhstl[2] = (unsigned long )41;
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqhstv[3] = (unsigned char  *)szDstAddr;
    sqlstm.sqhstl[3] = (unsigned long )13;
    sqlstm.sqhsts[3] = (         int  )0;
    sqlstm.sqindv[3] = (         short *)0;
    sqlstm.sqinds[3] = (         int  )0;
    sqlstm.sqharm[3] = (unsigned long )0;
    sqlstm.sqadto[3] = (unsigned short )0;
    sqlstm.sqtdso[3] = (unsigned short )0;
    sqlstm.sqhstv[4] = (unsigned char  *)szTmplCd;
    sqlstm.sqhstl[4] = (unsigned long )11;
    sqlstm.sqhsts[4] = (         int  )0;
    sqlstm.sqindv[4] = (         short *)0;
    sqlstm.sqinds[4] = (         int  )0;
    sqlstm.sqharm[4] = (unsigned long )0;
    sqlstm.sqadto[4] = (unsigned short )0;
    sqlstm.sqtdso[4] = (unsigned short )0;
    sqlstm.sqhstv[5] = (unsigned char  *)szMsgBody;
    sqlstm.sqhstl[5] = (unsigned long )2001;
    sqlstm.sqhsts[5] = (         int  )0;
    sqlstm.sqindv[5] = (         short *)0;
    sqlstm.sqinds[5] = (         int  )0;
    sqlstm.sqharm[5] = (unsigned long )0;
    sqlstm.sqadto[5] = (unsigned short )0;
    sqlstm.sqtdso[5] = (unsigned short )0;
    sqlstm.sqhstv[6] = (unsigned char  *)cMMSId;
    sqlstm.sqhstl[6] = (unsigned long )20;
    sqlstm.sqhsts[6] = (         int  )0;
    sqlstm.sqindv[6] = (         short *)0;
    sqlstm.sqinds[6] = (         int  )0;
    sqlstm.sqharm[6] = (unsigned long )0;
    sqlstm.sqadto[6] = (unsigned short )0;
    sqlstm.sqtdso[6] = (unsigned short )0;
    sqlstm.sqhstv[7] = (unsigned char  *)&nSqlCode;
    sqlstm.sqhstl[7] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[7] = (         int  )0;
    sqlstm.sqindv[7] = (         short *)0;
    sqlstm.sqinds[7] = (         int  )0;
    sqlstm.sqharm[7] = (unsigned long )0;
    sqlstm.sqadto[7] = (unsigned short )0;
    sqlstm.sqtdso[7] = (unsigned short )0;
    sqlstm.sqhstv[8] = (unsigned char  *)szSqlErrorMsg;
    sqlstm.sqhstl[8] = (unsigned long )1024;
    sqlstm.sqhsts[8] = (         int  )0;
    sqlstm.sqindv[8] = (         short *)0;
    sqlstm.sqinds[8] = (         int  )0;
    sqlstm.sqharm[8] = (unsigned long )0;
    sqlstm.sqadto[8] = (unsigned short )0;
    sqlstm.sqtdso[8] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



    if( nSqlCode != 0 )
    {
        if( nSqlCode == -999 )
        {
            sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSMSG_TALK exec failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,strlen(sqlca.sqlerrm.
sqlerrmc)));
            _logPrint(_DATALOG, tmpLog3);
            return -1;
        }
        sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSMSG_TALK invaled failed - otReuslt[%d]errMsg[%s]",nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
        _logPrint(_DATALOG, tmpLog3);
        return -1;
    }

    return 1;
}

/*
int CDatabaseORA::setMMSMSG_FTALK(CSenderDbMMSMSG_FTALK &que_data)
{
	EXEC SQL BEGIN DECLARE SECTION;
	char szSqlErrorMsg[1024];
	int nSqlCode = -999;

	int nQNum;
	int nPriority;
	char szSenderKey[40+1];
	char szDstAddr[12+1];
	char szUserKey[30+1];
	char szMsgGrpCd[30+1];
	char szMsgBody[2000+1];
	char szBtName[50+1];
	char szBtUrl[1000+1];
	char szImgPath[100+1];
	char szImgLink[1000+1];
	
	char cMMSId[20];
	long long lMMSId;
	EXEC SQL END DECLARE SECTION;

	memset(szSqlErrorMsg, 0x00, sizeof(szSqlErrorMsg));
	memset(szSenderKey, 0x00, sizeof(szSenderKey));
	memset(szDstAddr, 0x00, sizeof(szDstAddr));
	memset(szUserKey, 0x00, sizeof(szUserKey));
	memset(szMsgGrpCd, 0x00, sizeof(szMsgGrpCd));
	memset(szMsgBody, 0x00, sizeof(szMsgBody));
	memset(szBtName, 0x00, sizeof(szBtName));
	memset(szBtUrl, 0x00, sizeof(szBtUrl));
	memset(szImgPath, 0x00, sizeof(szImgPath));
	memset(szImgLink, 0x00, sizeof(szImgLink));
	memset(cMMSId, 0x00, sizeof(cMMSId));

	nQNum     = 1;
	nQNum     = atoi(que_data.szQName);
	nPriority = que_data.nPriority;
	sprintf(szSenderKey, que_data.szSenderKey);
	sprintf(szDstAddr, que_data.szDstAddr);
	sprintf(szUserKey, que_data.szUserKey);
	sprintf(szMsgGrpCd, que_data.szMsgGrpCd);
	sprintf(szBtName, que_data.szBtName);
	sprintf(szBtUrl, que_data.szBtUrl);
	sprintf(szImgPath, que_data.szImgPath);
	sprintf(szImgLink, que_data.szImgLink);
	sprintf(szMsgBody, que_data.szMsgBody);
	
	//아래코드가 된다
	sprintf(cMMSId, "%lld", que_data.nMMSId);
	//lMMSId	= que_data.nMMSId;
	
	EXEC SQL EXECUTE
		BEGIN
			PROC_SET_MMS_MSG_FTALK(in_q_num       =>:nQNum
								 ,in_priority    =>:nPriority
								 ,in_sender_key  =>:szSenderKey
								 ,in_dst_addr    =>:szDstAddr
								 ,in_user_key    =>:szUserKey
								 ,in_msg_grp_cd  =>:szMsgGrpCd
								 ,in_msg_body    =>:szMsgBody
								 ,in_button_name =>:szBtName
								 ,in_button_url  =>:szBtUrl
								 ,in_img_path    =>:szImgPath
								 ,in_img_link    =>:szImgLink
								 ,in_mms_id      =>:cMMSId
								 ,ot_sqlcode     =>:nSqlCode
								 ,ot_sqlmsg      =>:szSqlErrorMsg
								 );

		END;
	END-EXEC;
	
	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSCTNTBL exec failed - MMSID[%s] sqlcode[%d] sqlmsg[%s]",cMMSId,sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,strlen(sqlca.sqlerrm.sqlerrmc)));
			_logPrint(_DATALOG, tmpLog3);
			return -1;
		}
		sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSCTNTBL invaled failed - MMSID[%s]otReuslt[%d]errMsg[%s]",cMMSId,nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
		
    return 1;
}


int CDatabaseORA::setMMSMSG_FTALK_V2(CSenderDbMMSMSG_FTALK &que_data)
{
    EXEC SQL BEGIN DECLARE SECTION;
    char szSqlErrorMsg[1024];
    int nSqlCode = -999;

    int nQNum;
    int nPriority;
    char szSenderKey[40+1];
    char szDstAddr[12+1];
    char szUserKey[30+1];
    char szMsgBody[2000+1];
    char szBtName[50+1];
    char szBtUrl[1000+1];
    char szImgPath[100+1];
    char szImgLink[1000+1];
    char szResMethod[8+1];
    char szTimeout[2+1];

    char cMMSId[20];
    long long lMMSId;
    EXEC SQL END DECLARE SECTION;

    memset(szSqlErrorMsg, 0x00, sizeof(szSqlErrorMsg));
    memset(szSenderKey, 0x00, sizeof(szSenderKey));
    memset(szDstAddr, 0x00, sizeof(szDstAddr));
    memset(szUserKey, 0x00, sizeof(szUserKey));
    memset(szMsgBody, 0x00, sizeof(szMsgBody));
    memset(szBtName, 0x00, sizeof(szBtName));
    memset(szBtUrl, 0x00, sizeof(szBtUrl));
    memset(szImgPath, 0x00, sizeof(szImgPath));
    memset(szImgLink, 0x00, sizeof(szImgLink));
    memset(cMMSId, 0x00, sizeof(cMMSId));
    memset(szResMethod, 0x00, sizeof(szResMethod));
    memset(szTimeout, 0x00, sizeof(szTimeout));

	nQNum     = 1;
    nQNum     = atoi(que_data.szQName);
    nPriority = que_data.nPriority;
    sprintf(szSenderKey, que_data.szSenderKey);
    sprintf(szDstAddr, que_data.szDstAddr);
    sprintf(szUserKey, que_data.szUserKey);
    sprintf(szBtName, que_data.szBtName);
    sprintf(szBtUrl, que_data.szBtUrl);
    sprintf(szImgPath, que_data.szImgPath);
    sprintf(szImgLink, que_data.szImgLink);
    sprintf(szMsgBody, que_data.szMsgBody);
    sprintf(szResMethod, que_data.szResMethod);
    sprintf(szTimeout, que_data.szTimeout);

    
    //아래코드가 된다
    sprintf(cMMSId, "%lld", que_data.nMMSId);
    //lMMSId    = que_data.nMMSId;

    EXEC SQL EXECUTE
        BEGIN
            PROC_SET_FTALK_MSG(in_q_num          =>:nQNum
                                 ,in_priority    =>:nPriority
                                 ,in_mms_id      =>:cMMSId
                                 ,in_sender_key  =>:szSenderKey
                                 ,in_dst_addr    =>:szDstAddr
                                 ,in_user_key    =>:szUserKey
                                 ,in_msg_body    =>:szMsgBody
                                 ,in_button_name =>:szBtName
                                 ,in_button_url  =>:szBtUrl
                                 ,in_img_path    =>:szImgPath
                                 ,in_img_link    =>:szImgLink
                                 ,in_res_method  =>:szResMethod
                                 ,in_tmout       =>:szTimeout
                                 ,ot_sqlcode     =>:nSqlCode
                                 ,ot_sqlmsg      =>:szSqlErrorMsg
                                 );

        END;
    END-EXEC;

    if( nSqlCode != 0 )
    {
        if( nSqlCode == -999 )
        {
            sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSMSG_FTALK_V2 exec failed - MMSID[%s] sqlcode[%d] sqlmsg[%s]",cMMSId, sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,strlen(sqlca.sqlerrm.sqlerrmc)));
            _logPrint(_DATALOG, tmpLog3);
            return -1;
        }
        sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSMSG_FTALK_V2 invaled failed - MMSID[%s]otReuslt[%d]errMsg[%s]",cMMSId, nSqlCode, trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
        _logPrint(_DATALOG, tmpLog3);
        return -1;
    }

    return 1;
}*/

int CDatabaseORA::setMMSMSG_FTK_V3(CSenderDbMMSMSG_FTALK &que_data)
{
    /* EXEC SQL BEGIN DECLARE SECTION; */ 

    char szSqlErrorMsg[1024];
    int nSqlCode = -999;

    int nQNum;
    int nPriority;
    char szSenderKey[40+1];
    //char szDstAddr[12+1];
    char szDstAddr[16+1];
    char szUserKey[30+1];
    char szMsgBody[2000+1];
    char szBtName[50+1];
    //char szBtUrl[1000+1];
    char szBtUrl[500+1];
    //char szButton[4000+1];
    char szButton[3000+1];
    char szImgPath[100+1];
    //char szImgLink[1000+1];
    char szImgLink[500+1];
    char szResMethod[8+1];
    char szTimeout[2+1];
    char szAdFlag[1+1];
    char szWide[1+1];
	char szKkoImgUrl[500+1];

    char cMMSId[20];
    long long lMMSId;
    /* EXEC SQL END DECLARE SECTION; */ 


    memset(szSqlErrorMsg, 0x00, sizeof(szSqlErrorMsg));
    memset(szSenderKey, 0x00, sizeof(szSenderKey));
    memset(szDstAddr, 0x00, sizeof(szDstAddr));
    memset(szUserKey, 0x00, sizeof(szUserKey));
    memset(szMsgBody, 0x00, sizeof(szMsgBody));
    memset(szBtName, 0x00, sizeof(szBtName));
    memset(szBtUrl, 0x00, sizeof(szBtUrl));
    memset(szButton, 0x00, sizeof(szButton));
    memset(szImgPath, 0x00, sizeof(szImgPath));
    memset(szImgLink, 0x00, sizeof(szImgLink));
    memset(cMMSId, 0x00, sizeof(cMMSId));
    memset(szResMethod, 0x00, sizeof(szResMethod));
    memset(szTimeout, 0x00, sizeof(szTimeout));
    memset(szAdFlag, 0x00, sizeof(szAdFlag));
    memset(szWide, 0x00, sizeof(szWide));
	memset(szKkoImgUrl,   0x00, sizeof(szKkoImgUrl));

	nQNum     = 1;
    nQNum     = atoi(que_data.szQName);
    nPriority = que_data.nPriority;
    sprintf(szSenderKey, que_data.szSenderKey);
    sprintf(szDstAddr, que_data.szDstAddr);
    sprintf(szUserKey, que_data.szUserKey);
    sprintf(szBtName, que_data.szBtName);
    //sprintf(szBtUrl, que_data.szBtUrl);
    strncpy(szBtUrl, que_data.szBtUrl,sizeof(szBtUrl)-1);
    //sprintf(szButton, que_data.szButton);
    strncpy(szButton, que_data.szButton,sizeof(szButton)-1);
    sprintf(szImgPath, que_data.szImgPath);
    //sprintf(szImgLink, que_data.szImgLink);
    strncpy(szImgLink, que_data.szImgLink,sizeof(szImgLink)-1);
    //sprintf(szMsgBody, que_data.szMsgBody);
    strncpy(szMsgBody, que_data.szMsgBody,sizeof(szMsgBody)-1);
    sprintf(szResMethod, que_data.szResMethod);
    sprintf(szTimeout, que_data.szTimeout);
    sprintf(szAdFlag, que_data.szAdFlag);
    sprintf(szWide, que_data.szWide);

	strncpy(szKkoImgUrl, que_data.szKkoImgUrl, sizeof(szKkoImgUrl)-1);
    /*
     * 아래코드가 된다
     */
    sprintf(cMMSId, "%lld", que_data.nMMSId);
    //lMMSId    = que_data.nMMSId;
    
    /*sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSMSG_FTALK_V3 que[%d] Priority[%d] cMMSId[%s] szSenderKey[%s] szDstAddr[%s] szMsgBody[%s] szBtName[%s] szBtUrl[%s] szButton[%s] szImgPath[%s] szImgLink[%s] szResMethod[%s] szTimeout[%s] szAdFlag[%s] szWide[%s]",
            nQNum       ,        
            nPriority   ,  
            cMMSId      ,  
            szSenderKey ,  
            szDstAddr   ,  
            szMsgBody   ,  
            szBtName    ,  
            szBtUrl     ,  
            szButton 	,  
            szImgPath   ,  
            szImgLink   ,  
            szResMethod ,  
            szTimeout,
            szAdFlag,
            szWide);
            _logPrint(_DATALOG, tmpLog3);*/

    /* EXEC SQL EXECUTE
        BEGIN
            PROC_SET_FTK_MSG_V3(in_q_num         =>:nQNum
                                 ,in_priority    =>:nPriority
                                 ,in_mms_id      =>:cMMSId
                                 ,in_sender_key  =>:szSenderKey
                                 ,in_dst_addr    =>:szDstAddr
                                 ,in_user_key    =>:szUserKey
                                 ,in_msg_body    =>:szMsgBody
                                 ,in_button_name =>:szBtName
                                 ,in_button_url  =>:szBtUrl
                                 ,in_button      =>:szButton 	
                                 ,in_img_path    =>:szImgPath
                                 ,in_img_link    =>:szImgLink
                                 ,in_res_method  =>:szResMethod
                                 ,in_tmout       =>:szTimeout
                                 ,in_ad_flag     =>:szAdFlag
                                 ,in_wide        =>:szWide
								 ,in_kko_img_url =>:szKkoImgUrl	
                                 ,ot_sqlcode     =>:nSqlCode
                                 ,ot_sqlmsg      =>:szSqlErrorMsg
                                 );

        END;
    END-EXEC; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 35;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "begin PROC_SET_FTK_MSG_V3 ( in_q_num => :nQNum , in_prior\
ity => :nPriority , in_mms_id => :cMMSId , in_sender_key => :szSenderKey , in_\
dst_addr => :szDstAddr , in_user_key => :szUserKey , in_msg_body => :szMsgBody\
 , in_button_name => :szBtName , in_button_url => :szBtUrl , in_button => :szB\
utton , in_img_path => :szImgPath , in_img_link => :szImgLink , in_res_method \
=> :szResMethod , in_tmout => :szTimeout , in_ad_flag => :szAdFlag , in_wide =\
> :szWide , in_kko_img_url => :szKkoImgUrl , ot_sqlcode => :nSqlCode , ot_sqlm\
sg => :szSqlErrorMsg ) ; END ;";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )691;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&nQNum;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&nPriority;
    sqlstm.sqhstl[1] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)cMMSId;
    sqlstm.sqhstl[2] = (unsigned long )20;
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqhstv[3] = (unsigned char  *)szSenderKey;
    sqlstm.sqhstl[3] = (unsigned long )41;
    sqlstm.sqhsts[3] = (         int  )0;
    sqlstm.sqindv[3] = (         short *)0;
    sqlstm.sqinds[3] = (         int  )0;
    sqlstm.sqharm[3] = (unsigned long )0;
    sqlstm.sqadto[3] = (unsigned short )0;
    sqlstm.sqtdso[3] = (unsigned short )0;
    sqlstm.sqhstv[4] = (unsigned char  *)szDstAddr;
    sqlstm.sqhstl[4] = (unsigned long )17;
    sqlstm.sqhsts[4] = (         int  )0;
    sqlstm.sqindv[4] = (         short *)0;
    sqlstm.sqinds[4] = (         int  )0;
    sqlstm.sqharm[4] = (unsigned long )0;
    sqlstm.sqadto[4] = (unsigned short )0;
    sqlstm.sqtdso[4] = (unsigned short )0;
    sqlstm.sqhstv[5] = (unsigned char  *)szUserKey;
    sqlstm.sqhstl[5] = (unsigned long )31;
    sqlstm.sqhsts[5] = (         int  )0;
    sqlstm.sqindv[5] = (         short *)0;
    sqlstm.sqinds[5] = (         int  )0;
    sqlstm.sqharm[5] = (unsigned long )0;
    sqlstm.sqadto[5] = (unsigned short )0;
    sqlstm.sqtdso[5] = (unsigned short )0;
    sqlstm.sqhstv[6] = (unsigned char  *)szMsgBody;
    sqlstm.sqhstl[6] = (unsigned long )2001;
    sqlstm.sqhsts[6] = (         int  )0;
    sqlstm.sqindv[6] = (         short *)0;
    sqlstm.sqinds[6] = (         int  )0;
    sqlstm.sqharm[6] = (unsigned long )0;
    sqlstm.sqadto[6] = (unsigned short )0;
    sqlstm.sqtdso[6] = (unsigned short )0;
    sqlstm.sqhstv[7] = (unsigned char  *)szBtName;
    sqlstm.sqhstl[7] = (unsigned long )51;
    sqlstm.sqhsts[7] = (         int  )0;
    sqlstm.sqindv[7] = (         short *)0;
    sqlstm.sqinds[7] = (         int  )0;
    sqlstm.sqharm[7] = (unsigned long )0;
    sqlstm.sqadto[7] = (unsigned short )0;
    sqlstm.sqtdso[7] = (unsigned short )0;
    sqlstm.sqhstv[8] = (unsigned char  *)szBtUrl;
    sqlstm.sqhstl[8] = (unsigned long )501;
    sqlstm.sqhsts[8] = (         int  )0;
    sqlstm.sqindv[8] = (         short *)0;
    sqlstm.sqinds[8] = (         int  )0;
    sqlstm.sqharm[8] = (unsigned long )0;
    sqlstm.sqadto[8] = (unsigned short )0;
    sqlstm.sqtdso[8] = (unsigned short )0;
    sqlstm.sqhstv[9] = (unsigned char  *)szButton;
    sqlstm.sqhstl[9] = (unsigned long )3001;
    sqlstm.sqhsts[9] = (         int  )0;
    sqlstm.sqindv[9] = (         short *)0;
    sqlstm.sqinds[9] = (         int  )0;
    sqlstm.sqharm[9] = (unsigned long )0;
    sqlstm.sqadto[9] = (unsigned short )0;
    sqlstm.sqtdso[9] = (unsigned short )0;
    sqlstm.sqhstv[10] = (unsigned char  *)szImgPath;
    sqlstm.sqhstl[10] = (unsigned long )101;
    sqlstm.sqhsts[10] = (         int  )0;
    sqlstm.sqindv[10] = (         short *)0;
    sqlstm.sqinds[10] = (         int  )0;
    sqlstm.sqharm[10] = (unsigned long )0;
    sqlstm.sqadto[10] = (unsigned short )0;
    sqlstm.sqtdso[10] = (unsigned short )0;
    sqlstm.sqhstv[11] = (unsigned char  *)szImgLink;
    sqlstm.sqhstl[11] = (unsigned long )501;
    sqlstm.sqhsts[11] = (         int  )0;
    sqlstm.sqindv[11] = (         short *)0;
    sqlstm.sqinds[11] = (         int  )0;
    sqlstm.sqharm[11] = (unsigned long )0;
    sqlstm.sqadto[11] = (unsigned short )0;
    sqlstm.sqtdso[11] = (unsigned short )0;
    sqlstm.sqhstv[12] = (unsigned char  *)szResMethod;
    sqlstm.sqhstl[12] = (unsigned long )9;
    sqlstm.sqhsts[12] = (         int  )0;
    sqlstm.sqindv[12] = (         short *)0;
    sqlstm.sqinds[12] = (         int  )0;
    sqlstm.sqharm[12] = (unsigned long )0;
    sqlstm.sqadto[12] = (unsigned short )0;
    sqlstm.sqtdso[12] = (unsigned short )0;
    sqlstm.sqhstv[13] = (unsigned char  *)szTimeout;
    sqlstm.sqhstl[13] = (unsigned long )3;
    sqlstm.sqhsts[13] = (         int  )0;
    sqlstm.sqindv[13] = (         short *)0;
    sqlstm.sqinds[13] = (         int  )0;
    sqlstm.sqharm[13] = (unsigned long )0;
    sqlstm.sqadto[13] = (unsigned short )0;
    sqlstm.sqtdso[13] = (unsigned short )0;
    sqlstm.sqhstv[14] = (unsigned char  *)szAdFlag;
    sqlstm.sqhstl[14] = (unsigned long )2;
    sqlstm.sqhsts[14] = (         int  )0;
    sqlstm.sqindv[14] = (         short *)0;
    sqlstm.sqinds[14] = (         int  )0;
    sqlstm.sqharm[14] = (unsigned long )0;
    sqlstm.sqadto[14] = (unsigned short )0;
    sqlstm.sqtdso[14] = (unsigned short )0;
    sqlstm.sqhstv[15] = (unsigned char  *)szWide;
    sqlstm.sqhstl[15] = (unsigned long )2;
    sqlstm.sqhsts[15] = (         int  )0;
    sqlstm.sqindv[15] = (         short *)0;
    sqlstm.sqinds[15] = (         int  )0;
    sqlstm.sqharm[15] = (unsigned long )0;
    sqlstm.sqadto[15] = (unsigned short )0;
    sqlstm.sqtdso[15] = (unsigned short )0;
    sqlstm.sqhstv[16] = (unsigned char  *)szKkoImgUrl;
    sqlstm.sqhstl[16] = (unsigned long )501;
    sqlstm.sqhsts[16] = (         int  )0;
    sqlstm.sqindv[16] = (         short *)0;
    sqlstm.sqinds[16] = (         int  )0;
    sqlstm.sqharm[16] = (unsigned long )0;
    sqlstm.sqadto[16] = (unsigned short )0;
    sqlstm.sqtdso[16] = (unsigned short )0;
    sqlstm.sqhstv[17] = (unsigned char  *)&nSqlCode;
    sqlstm.sqhstl[17] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[17] = (         int  )0;
    sqlstm.sqindv[17] = (         short *)0;
    sqlstm.sqinds[17] = (         int  )0;
    sqlstm.sqharm[17] = (unsigned long )0;
    sqlstm.sqadto[17] = (unsigned short )0;
    sqlstm.sqtdso[17] = (unsigned short )0;
    sqlstm.sqhstv[18] = (unsigned char  *)szSqlErrorMsg;
    sqlstm.sqhstl[18] = (unsigned long )1024;
    sqlstm.sqhsts[18] = (         int  )0;
    sqlstm.sqindv[18] = (         short *)0;
    sqlstm.sqinds[18] = (         int  )0;
    sqlstm.sqharm[18] = (unsigned long )0;
    sqlstm.sqadto[18] = (unsigned short )0;
    sqlstm.sqtdso[18] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



    if( nSqlCode != 0 )
    {
        if( nSqlCode == -999 )
        {
            sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSMSG_FTK_V3 exec failed - MMSID[%s] sqlcode[%d] sqlmsg[%s]",cMMSId, sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,strlen(sqlca.sqlerrm.sqlerrmc)));
            _logPrint(_DATALOG, tmpLog3);
            return -1;
        }
        sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSMSG_FTK_V3 invaled failed - MMSID[%s]otReuslt[%d]errMsg[%s]",cMMSId, nSqlCode, trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
        _logPrint(_DATALOG, tmpLog3);
        return -1;
    }

    return 1;
}

int CDatabaseORA::setMMSMSG_FTKUP(CSenderDbMMSMSG_FTALKUP &que_data) {
	/* EXEC SQL BEGIN DECLARE SECTION; */ 

    char szSqlErrorMsg[1024];
    int nSqlCode = -999;

    int nQNum;
    int nPriority;
    char szSenderKey[40+1];
    //char szDstAddr[12+1];
    char szDstAddr[16+1];
    char szMsgBody[2000+1];
    //char szButton[4000+1];
    char szButton[3000+1];
    char szResMethod[8+1];
    char szTimeout[2+1];
	char szEncoding[20+1];
	char szChatBubbleType[30+1];
	char szTargeting[1+1];
	char szTmplCd    [20+1];
	char szAppUserId [20+1];
	char szPushAlarm [1+1];
	char szMessageVariable	[2000+1];
	char szButtonVariable 	[2000+1];
	char szCouponVariable	[1000+1];
	char szImageVariable	[1000+1];
	char szVideoVariable	[1000+1];
	char szCommerceVariable	[1000+1];
	char szCarouselVariable	[2000+1];
	char szReserve			[4000+1];
    char cMMSId[20];
    long long lMMSId;
    /* EXEC SQL END DECLARE SECTION; */ 


	memset(szSqlErrorMsg, 0x00, sizeof(szSqlErrorMsg));
	memset(szSenderKey, 0x00, sizeof(szSenderKey));
	memset(szDstAddr, 0x00, sizeof(szDstAddr));
	memset(szMsgBody, 0x00, sizeof(szMsgBody));
	memset(szButton, 0x00, sizeof(szButton));
	memset(cMMSId, 0x00, sizeof(cMMSId));
	memset(szResMethod, 0x00, sizeof(szResMethod));
	memset(szTimeout, 0x00, sizeof(szTimeout));
	memset(szEncoding, 0x00, sizeof(szEncoding));
	memset(szChatBubbleType, 0x00, sizeof(szChatBubbleType));
	memset(szTargeting, 0x00, sizeof(szTargeting));
	memset(szTmplCd, 0x00, sizeof(szTmplCd));
	memset(szAppUserId, 0x00, sizeof(szAppUserId));
	memset(szPushAlarm, 0x00, sizeof(szPushAlarm));
	memset(szMessageVariable, 0x00, sizeof(szMessageVariable));
	memset(szButtonVariable, 0x00, sizeof(szButtonVariable));
	memset(szCouponVariable, 0x00, sizeof(szCouponVariable));
	memset(szImageVariable, 0x00, sizeof(szImageVariable));
	memset(szVideoVariable, 0x00, sizeof(szVideoVariable));
	memset(szCommerceVariable, 0x00, sizeof(szCommerceVariable));
	memset(szCarouselVariable, 0x00, sizeof(szCarouselVariable));
	memset(szReserve, 0x00, sizeof(szReserve));
	
	nQNum     = 1;
    nQNum     = atoi(que_data.szQName);
    nPriority = que_data.nPriority;
    sprintf(szSenderKey, que_data.szSenderKey);
    sprintf(szDstAddr, que_data.szDstAddr);
    //strncpy(szButton, que_data.szButton,sizeof(szButton)-1);
    //sprintf(szMsgBody, que_data.szMsgBody);
    strncpy(szMsgBody, que_data.szMsgBody,sizeof(szMsgBody)-1);
    sprintf(szResMethod, que_data.szResMethod);
    sprintf(szTimeout, que_data.szTimeout);
	snprintf(szEncoding, sizeof(szEncoding), que_data.szEncoding);
	snprintf(szChatBubbleType, sizeof(szChatBubbleType), que_data.szChatBubbleType);
	snprintf(szTargeting, sizeof(szTargeting), que_data.szTargeting);
	snprintf(szTmplCd, sizeof(szTmplCd), que_data.szTmplCd);
	snprintf(szAppUserId, sizeof(szAppUserId), que_data.szAppUserId);
	snprintf(szPushAlarm, sizeof(szPushAlarm), que_data.szPushAlarm);
	snprintf(szMessageVariable, sizeof(szMessageVariable), que_data.szMessageVariable);
	snprintf(szButtonVariable, sizeof(szButtonVariable), que_data.szButtonVariable);
	snprintf(szCouponVariable, sizeof(szCouponVariable), que_data.szCouponVariable);
	snprintf(szImageVariable, sizeof(szImageVariable), que_data.szImageVariable);
	snprintf(szVideoVariable, sizeof(szVideoVariable), que_data.szVideoVariable);
	snprintf(szCommerceVariable, sizeof(szCommerceVariable), que_data.szCommerceVariable);
	snprintf(szCarouselVariable, sizeof(szCarouselVariable), que_data.szCarouselVariable);
	snprintf(szReserve, sizeof(szReserve), que_data.szReserve);

    sprintf(cMMSId, "%lld", que_data.nMMSId);
    //lMMSId    = que_data.nMMSId;

	sprintf(tmpLog3,
			"[ERR] CDatabaseORA::setMMSMSG_FTKUP que[%d] Priority[%d] cMMSId[%s] szSenderKey[%s] "
			"szDstAddr[%s] szMsgBody[%s] szResMethod[%s] szTimeout[%s] ",
			nQNum, nPriority, cMMSId, szSenderKey, szDstAddr, szMsgBody, 
			szResMethod, szTimeout
			);
	_logPrint(_DATALOG, tmpLog3);

    /* EXEC SQL EXECUTE
        BEGIN
            PROC_SET_FTKUP_MSG(in_q_num         =>:nQNum
                                 ,in_priority    =>:nPriority
                                 ,in_mms_id      =>:cMMSId
                                 ,in_sender_key  =>:szSenderKey
                                 ,in_dst_addr    =>:szDstAddr
                                 ,in_res_method  =>:szResMethod
                                 ,in_tmout       =>:szTimeout
                                 ,in_encoding_type   =>:szEncoding
								 , in_chat_bubble_type =>:szChatBubbleType
								 , in_targeting =>:szTargeting
								 , in_tmpl_cd =>:szTmplCd
								 , in_app_user_id =>:szAppUserId
								 , in_push_alarm =>:szPushAlarm
								 , in_message_variable =>:szMessageVariable
								 , in_button_variable =>:szButtonVariable
								 , in_coupon_variable =>:szCouponVariable
								 , in_image_variable =>:szImageVariable
								 , in_video_variable =>:szVideoVariable
								 , in_commerce_variable =>:szCommerceVariable
								 , in_carousel_variable =>:szCarouselVariable
								 , in_reserve	=> :szReserve
                                 ,ot_sqlcode     =>:nSqlCode
                                 ,ot_sqlmsg      =>:szSqlErrorMsg
                                 );

        END;
    END-EXEC; */ 

{
    struct sqlexd sqlstm;
    sqlstm.sqlvsn = 13;
    sqlstm.arrsiz = 35;
    sqlstm.sqladtp = &sqladt;
    sqlstm.sqltdsp = &sqltds;
    sqlstm.stmt = "begin PROC_SET_FTKUP_MSG ( in_q_num => :nQNum , in_priori\
ty => :nPriority , in_mms_id => :cMMSId , in_sender_key => :szSenderKey , in_d\
st_addr => :szDstAddr , in_res_method => :szResMethod , in_tmout => :szTimeout\
 , in_encoding_type => :szEncoding , in_chat_bubble_type => :szChatBubbleType \
, in_targeting => :szTargeting , in_tmpl_cd => :szTmplCd , in_app_user_id => :\
szAppUserId , in_push_alarm => :szPushAlarm , in_message_variable => :szMessag\
eVariable , in_button_variable => :szButtonVariable , in_coupon_variable => :s\
zCouponVariable , in_image_variable => :szImageVariable , in_video_variable =>\
 :szVideoVariable , in_commerce_variable => :szCommerceVariable , in_carousel_\
variable => :szCarouselVariable , in_reserve => :szReserve , ot_sqlcode => :nS\
qlCode , ot_sqlmsg => :szSqlErrorMsg ) ; END ;";
    sqlstm.iters = (unsigned int  )1;
    sqlstm.offset = (unsigned int  )782;
    sqlstm.cud = sqlcud0;
    sqlstm.sqlest = (unsigned char  *)&sqlca;
    sqlstm.sqlety = (unsigned short)4352;
    sqlstm.occurs = (unsigned int  )0;
    sqlstm.sqhstv[0] = (unsigned char  *)&nQNum;
    sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[0] = (         int  )0;
    sqlstm.sqindv[0] = (         short *)0;
    sqlstm.sqinds[0] = (         int  )0;
    sqlstm.sqharm[0] = (unsigned long )0;
    sqlstm.sqadto[0] = (unsigned short )0;
    sqlstm.sqtdso[0] = (unsigned short )0;
    sqlstm.sqhstv[1] = (unsigned char  *)&nPriority;
    sqlstm.sqhstl[1] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[1] = (         int  )0;
    sqlstm.sqindv[1] = (         short *)0;
    sqlstm.sqinds[1] = (         int  )0;
    sqlstm.sqharm[1] = (unsigned long )0;
    sqlstm.sqadto[1] = (unsigned short )0;
    sqlstm.sqtdso[1] = (unsigned short )0;
    sqlstm.sqhstv[2] = (unsigned char  *)cMMSId;
    sqlstm.sqhstl[2] = (unsigned long )20;
    sqlstm.sqhsts[2] = (         int  )0;
    sqlstm.sqindv[2] = (         short *)0;
    sqlstm.sqinds[2] = (         int  )0;
    sqlstm.sqharm[2] = (unsigned long )0;
    sqlstm.sqadto[2] = (unsigned short )0;
    sqlstm.sqtdso[2] = (unsigned short )0;
    sqlstm.sqhstv[3] = (unsigned char  *)szSenderKey;
    sqlstm.sqhstl[3] = (unsigned long )41;
    sqlstm.sqhsts[3] = (         int  )0;
    sqlstm.sqindv[3] = (         short *)0;
    sqlstm.sqinds[3] = (         int  )0;
    sqlstm.sqharm[3] = (unsigned long )0;
    sqlstm.sqadto[3] = (unsigned short )0;
    sqlstm.sqtdso[3] = (unsigned short )0;
    sqlstm.sqhstv[4] = (unsigned char  *)szDstAddr;
    sqlstm.sqhstl[4] = (unsigned long )17;
    sqlstm.sqhsts[4] = (         int  )0;
    sqlstm.sqindv[4] = (         short *)0;
    sqlstm.sqinds[4] = (         int  )0;
    sqlstm.sqharm[4] = (unsigned long )0;
    sqlstm.sqadto[4] = (unsigned short )0;
    sqlstm.sqtdso[4] = (unsigned short )0;
    sqlstm.sqhstv[5] = (unsigned char  *)szResMethod;
    sqlstm.sqhstl[5] = (unsigned long )9;
    sqlstm.sqhsts[5] = (         int  )0;
    sqlstm.sqindv[5] = (         short *)0;
    sqlstm.sqinds[5] = (         int  )0;
    sqlstm.sqharm[5] = (unsigned long )0;
    sqlstm.sqadto[5] = (unsigned short )0;
    sqlstm.sqtdso[5] = (unsigned short )0;
    sqlstm.sqhstv[6] = (unsigned char  *)szTimeout;
    sqlstm.sqhstl[6] = (unsigned long )3;
    sqlstm.sqhsts[6] = (         int  )0;
    sqlstm.sqindv[6] = (         short *)0;
    sqlstm.sqinds[6] = (         int  )0;
    sqlstm.sqharm[6] = (unsigned long )0;
    sqlstm.sqadto[6] = (unsigned short )0;
    sqlstm.sqtdso[6] = (unsigned short )0;
    sqlstm.sqhstv[7] = (unsigned char  *)szEncoding;
    sqlstm.sqhstl[7] = (unsigned long )21;
    sqlstm.sqhsts[7] = (         int  )0;
    sqlstm.sqindv[7] = (         short *)0;
    sqlstm.sqinds[7] = (         int  )0;
    sqlstm.sqharm[7] = (unsigned long )0;
    sqlstm.sqadto[7] = (unsigned short )0;
    sqlstm.sqtdso[7] = (unsigned short )0;
    sqlstm.sqhstv[8] = (unsigned char  *)szChatBubbleType;
    sqlstm.sqhstl[8] = (unsigned long )31;
    sqlstm.sqhsts[8] = (         int  )0;
    sqlstm.sqindv[8] = (         short *)0;
    sqlstm.sqinds[8] = (         int  )0;
    sqlstm.sqharm[8] = (unsigned long )0;
    sqlstm.sqadto[8] = (unsigned short )0;
    sqlstm.sqtdso[8] = (unsigned short )0;
    sqlstm.sqhstv[9] = (unsigned char  *)szTargeting;
    sqlstm.sqhstl[9] = (unsigned long )2;
    sqlstm.sqhsts[9] = (         int  )0;
    sqlstm.sqindv[9] = (         short *)0;
    sqlstm.sqinds[9] = (         int  )0;
    sqlstm.sqharm[9] = (unsigned long )0;
    sqlstm.sqadto[9] = (unsigned short )0;
    sqlstm.sqtdso[9] = (unsigned short )0;
    sqlstm.sqhstv[10] = (unsigned char  *)szTmplCd;
    sqlstm.sqhstl[10] = (unsigned long )21;
    sqlstm.sqhsts[10] = (         int  )0;
    sqlstm.sqindv[10] = (         short *)0;
    sqlstm.sqinds[10] = (         int  )0;
    sqlstm.sqharm[10] = (unsigned long )0;
    sqlstm.sqadto[10] = (unsigned short )0;
    sqlstm.sqtdso[10] = (unsigned short )0;
    sqlstm.sqhstv[11] = (unsigned char  *)szAppUserId;
    sqlstm.sqhstl[11] = (unsigned long )21;
    sqlstm.sqhsts[11] = (         int  )0;
    sqlstm.sqindv[11] = (         short *)0;
    sqlstm.sqinds[11] = (         int  )0;
    sqlstm.sqharm[11] = (unsigned long )0;
    sqlstm.sqadto[11] = (unsigned short )0;
    sqlstm.sqtdso[11] = (unsigned short )0;
    sqlstm.sqhstv[12] = (unsigned char  *)szPushAlarm;
    sqlstm.sqhstl[12] = (unsigned long )2;
    sqlstm.sqhsts[12] = (         int  )0;
    sqlstm.sqindv[12] = (         short *)0;
    sqlstm.sqinds[12] = (         int  )0;
    sqlstm.sqharm[12] = (unsigned long )0;
    sqlstm.sqadto[12] = (unsigned short )0;
    sqlstm.sqtdso[12] = (unsigned short )0;
    sqlstm.sqhstv[13] = (unsigned char  *)szMessageVariable;
    sqlstm.sqhstl[13] = (unsigned long )2001;
    sqlstm.sqhsts[13] = (         int  )0;
    sqlstm.sqindv[13] = (         short *)0;
    sqlstm.sqinds[13] = (         int  )0;
    sqlstm.sqharm[13] = (unsigned long )0;
    sqlstm.sqadto[13] = (unsigned short )0;
    sqlstm.sqtdso[13] = (unsigned short )0;
    sqlstm.sqhstv[14] = (unsigned char  *)szButtonVariable;
    sqlstm.sqhstl[14] = (unsigned long )2001;
    sqlstm.sqhsts[14] = (         int  )0;
    sqlstm.sqindv[14] = (         short *)0;
    sqlstm.sqinds[14] = (         int  )0;
    sqlstm.sqharm[14] = (unsigned long )0;
    sqlstm.sqadto[14] = (unsigned short )0;
    sqlstm.sqtdso[14] = (unsigned short )0;
    sqlstm.sqhstv[15] = (unsigned char  *)szCouponVariable;
    sqlstm.sqhstl[15] = (unsigned long )1001;
    sqlstm.sqhsts[15] = (         int  )0;
    sqlstm.sqindv[15] = (         short *)0;
    sqlstm.sqinds[15] = (         int  )0;
    sqlstm.sqharm[15] = (unsigned long )0;
    sqlstm.sqadto[15] = (unsigned short )0;
    sqlstm.sqtdso[15] = (unsigned short )0;
    sqlstm.sqhstv[16] = (unsigned char  *)szImageVariable;
    sqlstm.sqhstl[16] = (unsigned long )1001;
    sqlstm.sqhsts[16] = (         int  )0;
    sqlstm.sqindv[16] = (         short *)0;
    sqlstm.sqinds[16] = (         int  )0;
    sqlstm.sqharm[16] = (unsigned long )0;
    sqlstm.sqadto[16] = (unsigned short )0;
    sqlstm.sqtdso[16] = (unsigned short )0;
    sqlstm.sqhstv[17] = (unsigned char  *)szVideoVariable;
    sqlstm.sqhstl[17] = (unsigned long )1001;
    sqlstm.sqhsts[17] = (         int  )0;
    sqlstm.sqindv[17] = (         short *)0;
    sqlstm.sqinds[17] = (         int  )0;
    sqlstm.sqharm[17] = (unsigned long )0;
    sqlstm.sqadto[17] = (unsigned short )0;
    sqlstm.sqtdso[17] = (unsigned short )0;
    sqlstm.sqhstv[18] = (unsigned char  *)szCommerceVariable;
    sqlstm.sqhstl[18] = (unsigned long )1001;
    sqlstm.sqhsts[18] = (         int  )0;
    sqlstm.sqindv[18] = (         short *)0;
    sqlstm.sqinds[18] = (         int  )0;
    sqlstm.sqharm[18] = (unsigned long )0;
    sqlstm.sqadto[18] = (unsigned short )0;
    sqlstm.sqtdso[18] = (unsigned short )0;
    sqlstm.sqhstv[19] = (unsigned char  *)szCarouselVariable;
    sqlstm.sqhstl[19] = (unsigned long )2001;
    sqlstm.sqhsts[19] = (         int  )0;
    sqlstm.sqindv[19] = (         short *)0;
    sqlstm.sqinds[19] = (         int  )0;
    sqlstm.sqharm[19] = (unsigned long )0;
    sqlstm.sqadto[19] = (unsigned short )0;
    sqlstm.sqtdso[19] = (unsigned short )0;
    sqlstm.sqhstv[20] = (unsigned char  *)szReserve;
    sqlstm.sqhstl[20] = (unsigned long )4001;
    sqlstm.sqhsts[20] = (         int  )0;
    sqlstm.sqindv[20] = (         short *)0;
    sqlstm.sqinds[20] = (         int  )0;
    sqlstm.sqharm[20] = (unsigned long )0;
    sqlstm.sqadto[20] = (unsigned short )0;
    sqlstm.sqtdso[20] = (unsigned short )0;
    sqlstm.sqhstv[21] = (unsigned char  *)&nSqlCode;
    sqlstm.sqhstl[21] = (unsigned long )sizeof(int);
    sqlstm.sqhsts[21] = (         int  )0;
    sqlstm.sqindv[21] = (         short *)0;
    sqlstm.sqinds[21] = (         int  )0;
    sqlstm.sqharm[21] = (unsigned long )0;
    sqlstm.sqadto[21] = (unsigned short )0;
    sqlstm.sqtdso[21] = (unsigned short )0;
    sqlstm.sqhstv[22] = (unsigned char  *)szSqlErrorMsg;
    sqlstm.sqhstl[22] = (unsigned long )1024;
    sqlstm.sqhsts[22] = (         int  )0;
    sqlstm.sqindv[22] = (         short *)0;
    sqlstm.sqinds[22] = (         int  )0;
    sqlstm.sqharm[22] = (unsigned long )0;
    sqlstm.sqadto[22] = (unsigned short )0;
    sqlstm.sqtdso[22] = (unsigned short )0;
    sqlstm.sqphsv = sqlstm.sqhstv;
    sqlstm.sqphsl = sqlstm.sqhstl;
    sqlstm.sqphss = sqlstm.sqhsts;
    sqlstm.sqpind = sqlstm.sqindv;
    sqlstm.sqpins = sqlstm.sqinds;
    sqlstm.sqparm = sqlstm.sqharm;
    sqlstm.sqparc = sqlstm.sqharc;
    sqlstm.sqpadto = sqlstm.sqadto;
    sqlstm.sqptdso = sqlstm.sqtdso;
    sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



    if( nSqlCode != 0 )
    {
        if( nSqlCode == -999 )
        {
            sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSMSG_FTKUP exec failed - MMSID[%s] sqlcode[%d] sqlmsg[%s]",
            	cMMSId, sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,strlen(sqlca.sqlerrm.sqlerrmc)));
            _logPrint(_DATALOG, tmpLog3);
            return -1;
        }
        sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSMSG_FTKUP invaled failed - MMSID[%s]otReuslt[%d]errMsg[%s]",
        	cMMSId, nSqlCode, trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
        _logPrint(_DATALOG, tmpLog3);
        return -1;
    }

    return 1;
	
}

int CDatabaseORA::setMMSMSG_TALKBt(CSenderDbMMSMSG_TALK &que_data)
{
	/* EXEC SQL BEGIN DECLARE SECTION; */ 

	char szSqlErrorMsg[1024];
	int nSqlCode = -999;

	int nQNum;
	int nPriority;
	char szSenderKey[40+1];
	char szDstAddr[12+1];
	char szTmplCd[30+1];
	char szBtName[50+1];
	char szBtUrl[1000+1];
	char szMsgBody[2000+1];
	
	char cMMSId[20];
	long long lMMSId;
	/* EXEC SQL END DECLARE SECTION; */ 


	memset(szSqlErrorMsg, 0x00, sizeof(szSqlErrorMsg));
	memset(szSenderKey, 0x00, sizeof(szSenderKey));
	memset(szDstAddr, 0x00, sizeof(szDstAddr));
	memset(szTmplCd, 0x00, sizeof(szTmplCd));
	memset(szBtName, 0x00, sizeof(szBtName));
	memset(szBtUrl, 0x00, sizeof(szBtUrl));
	memset(szMsgBody, 0x00, sizeof(szMsgBody));
	memset(cMMSId, 0x00, sizeof(cMMSId));

	nQNum     = 1;
	nQNum     = atoi(que_data.szQName);
	nPriority = que_data.nPriority;
	sprintf(szSenderKey, que_data.szSenderKey);
	sprintf(szDstAddr, que_data.szDstAddr);
	sprintf(szTmplCd, que_data.szTmplCd);
	sprintf(szBtName, que_data.szBtName);
	sprintf(szBtUrl, que_data.szBtUrl);
	sprintf(szMsgBody, que_data.szMsgBody);
	
	/*
	 * 아래코드가 된다
	 */
	sprintf(cMMSId, "%lld", que_data.nMMSId);
	//lMMSId	= que_data.nMMSId;
	
	/* EXEC SQL EXECUTE
		BEGIN
			PROC_SET_TALKBT_MSG(in_q_num       =>:nQNum
								 ,in_priority    =>:nPriority
								 ,in_sender_key  =>:szSenderKey
								 ,in_dst_addr    =>:szDstAddr
								 ,in_template_cd =>:szTmplCd
								 ,in_button_name =>:szBtName
								 ,in_button_url  =>:szBtUrl
								 ,in_msg_body    =>:szMsgBody
								 ,in_mms_id      =>:cMMSId
								 ,ot_sqlcode     =>:nSqlCode
								 ,ot_sqlmsg      =>:szSqlErrorMsg
								 );

		END;
	END-EXEC; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 35;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.stmt = "begin PROC_SET_TALKBT_MSG ( in_q_num => :nQNum , in_priority\
 => :nPriority , in_sender_key => :szSenderKey , in_dst_addr => :szDstAddr , i\
n_template_cd => :szTmplCd , in_button_name => :szBtName , in_button_url => :s\
zBtUrl , in_msg_body => :szMsgBody , in_mms_id => :cMMSId , ot_sqlcode => :nSq\
lCode , ot_sqlmsg => :szSqlErrorMsg ) ; END ;";
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )889;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlstm.sqhstv[0] = (unsigned char  *)&nQNum;
 sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[0] = (         int  )0;
 sqlstm.sqindv[0] = (         short *)0;
 sqlstm.sqinds[0] = (         int  )0;
 sqlstm.sqharm[0] = (unsigned long )0;
 sqlstm.sqadto[0] = (unsigned short )0;
 sqlstm.sqtdso[0] = (unsigned short )0;
 sqlstm.sqhstv[1] = (unsigned char  *)&nPriority;
 sqlstm.sqhstl[1] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[1] = (         int  )0;
 sqlstm.sqindv[1] = (         short *)0;
 sqlstm.sqinds[1] = (         int  )0;
 sqlstm.sqharm[1] = (unsigned long )0;
 sqlstm.sqadto[1] = (unsigned short )0;
 sqlstm.sqtdso[1] = (unsigned short )0;
 sqlstm.sqhstv[2] = (unsigned char  *)szSenderKey;
 sqlstm.sqhstl[2] = (unsigned long )41;
 sqlstm.sqhsts[2] = (         int  )0;
 sqlstm.sqindv[2] = (         short *)0;
 sqlstm.sqinds[2] = (         int  )0;
 sqlstm.sqharm[2] = (unsigned long )0;
 sqlstm.sqadto[2] = (unsigned short )0;
 sqlstm.sqtdso[2] = (unsigned short )0;
 sqlstm.sqhstv[3] = (unsigned char  *)szDstAddr;
 sqlstm.sqhstl[3] = (unsigned long )13;
 sqlstm.sqhsts[3] = (         int  )0;
 sqlstm.sqindv[3] = (         short *)0;
 sqlstm.sqinds[3] = (         int  )0;
 sqlstm.sqharm[3] = (unsigned long )0;
 sqlstm.sqadto[3] = (unsigned short )0;
 sqlstm.sqtdso[3] = (unsigned short )0;
 sqlstm.sqhstv[4] = (unsigned char  *)szTmplCd;
 sqlstm.sqhstl[4] = (unsigned long )31;
 sqlstm.sqhsts[4] = (         int  )0;
 sqlstm.sqindv[4] = (         short *)0;
 sqlstm.sqinds[4] = (         int  )0;
 sqlstm.sqharm[4] = (unsigned long )0;
 sqlstm.sqadto[4] = (unsigned short )0;
 sqlstm.sqtdso[4] = (unsigned short )0;
 sqlstm.sqhstv[5] = (unsigned char  *)szBtName;
 sqlstm.sqhstl[5] = (unsigned long )51;
 sqlstm.sqhsts[5] = (         int  )0;
 sqlstm.sqindv[5] = (         short *)0;
 sqlstm.sqinds[5] = (         int  )0;
 sqlstm.sqharm[5] = (unsigned long )0;
 sqlstm.sqadto[5] = (unsigned short )0;
 sqlstm.sqtdso[5] = (unsigned short )0;
 sqlstm.sqhstv[6] = (unsigned char  *)szBtUrl;
 sqlstm.sqhstl[6] = (unsigned long )1001;
 sqlstm.sqhsts[6] = (         int  )0;
 sqlstm.sqindv[6] = (         short *)0;
 sqlstm.sqinds[6] = (         int  )0;
 sqlstm.sqharm[6] = (unsigned long )0;
 sqlstm.sqadto[6] = (unsigned short )0;
 sqlstm.sqtdso[6] = (unsigned short )0;
 sqlstm.sqhstv[7] = (unsigned char  *)szMsgBody;
 sqlstm.sqhstl[7] = (unsigned long )2001;
 sqlstm.sqhsts[7] = (         int  )0;
 sqlstm.sqindv[7] = (         short *)0;
 sqlstm.sqinds[7] = (         int  )0;
 sqlstm.sqharm[7] = (unsigned long )0;
 sqlstm.sqadto[7] = (unsigned short )0;
 sqlstm.sqtdso[7] = (unsigned short )0;
 sqlstm.sqhstv[8] = (unsigned char  *)cMMSId;
 sqlstm.sqhstl[8] = (unsigned long )20;
 sqlstm.sqhsts[8] = (         int  )0;
 sqlstm.sqindv[8] = (         short *)0;
 sqlstm.sqinds[8] = (         int  )0;
 sqlstm.sqharm[8] = (unsigned long )0;
 sqlstm.sqadto[8] = (unsigned short )0;
 sqlstm.sqtdso[8] = (unsigned short )0;
 sqlstm.sqhstv[9] = (unsigned char  *)&nSqlCode;
 sqlstm.sqhstl[9] = (unsigned long )sizeof(int);
 sqlstm.sqhsts[9] = (         int  )0;
 sqlstm.sqindv[9] = (         short *)0;
 sqlstm.sqinds[9] = (         int  )0;
 sqlstm.sqharm[9] = (unsigned long )0;
 sqlstm.sqadto[9] = (unsigned short )0;
 sqlstm.sqtdso[9] = (unsigned short )0;
 sqlstm.sqhstv[10] = (unsigned char  *)szSqlErrorMsg;
 sqlstm.sqhstl[10] = (unsigned long )1024;
 sqlstm.sqhsts[10] = (         int  )0;
 sqlstm.sqindv[10] = (         short *)0;
 sqlstm.sqinds[10] = (         int  )0;
 sqlstm.sqharm[10] = (unsigned long )0;
 sqlstm.sqadto[10] = (unsigned short )0;
 sqlstm.sqtdso[10] = (unsigned short )0;
 sqlstm.sqphsv = sqlstm.sqhstv;
 sqlstm.sqphsl = sqlstm.sqhstl;
 sqlstm.sqphss = sqlstm.sqhsts;
 sqlstm.sqpind = sqlstm.sqindv;
 sqlstm.sqpins = sqlstm.sqinds;
 sqlstm.sqparm = sqlstm.sqharm;
 sqlstm.sqparc = sqlstm.sqharc;
 sqlstm.sqpadto = sqlstm.sqadto;
 sqlstm.sqptdso = sqlstm.sqtdso;
 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}


	
	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSMSG_TALKBt exec failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,strlen(sqlca.sqlerrm.sqlerrmc)));
			_logPrint(_DATALOG, tmpLog3);
			return -1;
		}
		sprintf(tmpLog3, "[ERR] CDatabaseORA::setMMSMSG_TALKBt invaled failed - otReuslt[%d]errMsg[%s]",nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		_logPrint(_DATALOG, tmpLog3);
		return -1;
	}
		
    return 1;
}

// 20170621 MMSID SEQ USE
int CDatabaseORA::selectSEQ()
{

	int nRet = -1;

	/* EXEC SQL BEGIN DECLARE SECTION; */ 

	int seq = -1;
  /* EXEC SQL END DECLARE SECTION; */ 


	
	/* EXEC SQL WHENEVER SQLERROR CONTINUE; */ 

	
	/* EXEC SQL DECLARE cur_proc_id CURSOR FOR
		SELECT SEQ_PROC_ID.NEXTVAL FROM DUAL; */ 


	
	/* EXEC SQL OPEN cur_proc_id; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 35;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.stmt = sq0018;
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )948;
 sqlstm.selerr = (unsigned short)1;
 sqlstm.sqlpfmem = (unsigned int  )0;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlstm.sqcmod = (unsigned int )0;
 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}



	if(sqlca.sqlcode!= 0)
	{
		sprintf(tmpLog3, "[ERR] CDatabaseORA::selectSEQ OPEN failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode, trim(sqlca.sqlerrm.sqlerrmc, strlen(sqlca.sqlerrm.sqlerrmc)));
		_logPrint(_DATALOG, tmpLog3);
		return nRet;		
	}
	

	while(true)
	{

		/* EXEC SQL FETCH cur_proc_id INTO
			:seq ; */ 

{
  struct sqlexd sqlstm;
  sqlstm.sqlvsn = 13;
  sqlstm.arrsiz = 35;
  sqlstm.sqladtp = &sqladt;
  sqlstm.sqltdsp = &sqltds;
  sqlstm.iters = (unsigned int  )1;
  sqlstm.offset = (unsigned int  )963;
  sqlstm.selerr = (unsigned short)1;
  sqlstm.sqlpfmem = (unsigned int  )0;
  sqlstm.cud = sqlcud0;
  sqlstm.sqlest = (unsigned char  *)&sqlca;
  sqlstm.sqlety = (unsigned short)4352;
  sqlstm.occurs = (unsigned int  )0;
  sqlstm.sqfoff = (         int )0;
  sqlstm.sqfmod = (unsigned int )2;
  sqlstm.sqhstv[0] = (unsigned char  *)&seq;
  sqlstm.sqhstl[0] = (unsigned long )sizeof(int);
  sqlstm.sqhsts[0] = (         int  )0;
  sqlstm.sqindv[0] = (         short *)0;
  sqlstm.sqinds[0] = (         int  )0;
  sqlstm.sqharm[0] = (unsigned long )0;
  sqlstm.sqadto[0] = (unsigned short )0;
  sqlstm.sqtdso[0] = (unsigned short )0;
  sqlstm.sqphsv = sqlstm.sqhstv;
  sqlstm.sqphsl = sqlstm.sqhstl;
  sqlstm.sqphss = sqlstm.sqhsts;
  sqlstm.sqpind = sqlstm.sqindv;
  sqlstm.sqpins = sqlstm.sqinds;
  sqlstm.sqparm = sqlstm.sqharm;
  sqlstm.sqparc = sqlstm.sqharc;
  sqlstm.sqpadto = sqlstm.sqadto;
  sqlstm.sqptdso = sqlstm.sqtdso;
  sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}

 	

		sprintf(tmpLog3, "[INF] CDatabaseORA::selectSEQ seq[%d] sqlcode[%d] sqlmsg[%s]",seq, sqlca.sqlcode, trim(sqlca.sqlerrm.sqlerrmc, strlen(sqlca.sqlerrm.sqlerrmc)));
            _logPrint(_DATALOG, tmpLog3);
            			
		if( seq > 0 )
			break;
				
		if(sqlca.sqlcode != 0)
		{
            sprintf(tmpLog3, "[ERR] CDatabaseORA::selectSEQ FETCH failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode, trim(sqlca.sqlerrm.sqlerrmc, strlen(sqlca.sqlerrm.sqlerrmc)));
            _logPrint(_DATALOG, tmpLog3);
            
			/* EXEC SQL CLOSE cur_proc_id; */ 

{
   struct sqlexd sqlstm;
   sqlstm.sqlvsn = 13;
   sqlstm.arrsiz = 35;
   sqlstm.sqladtp = &sqladt;
   sqlstm.sqltdsp = &sqltds;
   sqlstm.iters = (unsigned int  )1;
   sqlstm.offset = (unsigned int  )982;
   sqlstm.cud = sqlcud0;
   sqlstm.sqlest = (unsigned char  *)&sqlca;
   sqlstm.sqlety = (unsigned short)4352;
   sqlstm.occurs = (unsigned int  )0;
   sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}

            
			return nRet;
		}

	}

	/* EXEC SQL CLOSE cur_proc_id; */ 

{
 struct sqlexd sqlstm;
 sqlstm.sqlvsn = 13;
 sqlstm.arrsiz = 35;
 sqlstm.sqladtp = &sqladt;
 sqlstm.sqltdsp = &sqltds;
 sqlstm.iters = (unsigned int  )1;
 sqlstm.offset = (unsigned int  )997;
 sqlstm.cud = sqlcud0;
 sqlstm.sqlest = (unsigned char  *)&sqlca;
 sqlstm.sqlety = (unsigned short)4352;
 sqlstm.occurs = (unsigned int  )0;
 sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}


	
	return seq;
}

int CDatabaseORA::setSendReportData(CSenderDbMMSRPTQUE &rpt_data)
{
	/* EXEC SQL BEGIN DECLARE SECTION; */ 

	int ot_sqlcode = -1;
	char ot_sqlmsg[1024];
	int ot_queue_sqlcode = -1;
	char ot_queue_sqlmsg[1024];

	char msg_id[50+1];
	long long mms_id;
	char cmms_id[32+1];
	char dlv_date[14+1];
	char snd_numb[12+1];
	char rcv_numb[12+1];
	char res_code[4+1];
	char res_text[200+1];
	char cid[10+1];
	char ptn_sn[16+1];
	int telco_id;
	int res_type;
	char end_telco[5+1];
	char resv_data[200+1];
	

	struct sqlca sqlca;
	/* EXEC SQL END DECLARE SECTION; */ 

	
	int type;
	string cTime;
	time_t tm_time;
	struct tm *st_time;
	char buff[1024] = {0x00,};

	/* EXEC SQL WHENEVER NOT FOUND CONTINUE; */ 

    /* EXEC SQL WHENEVER SQLERROR CONTINUE; */ 
   /* don't trap errors */

	memset(ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
	memset(ot_queue_sqlmsg, 0x00, sizeof ot_queue_sqlmsg);
	memset(msg_id, 0x00, sizeof msg_id);
	memset(dlv_date, 0x00, sizeof dlv_date);
	memset(snd_numb, 0x00, sizeof snd_numb);
	memset(rcv_numb, 0x00, sizeof rcv_numb);
	memset(res_code, 0x00, sizeof res_code);
	memset(res_text, 0x00, sizeof res_text);
	memset(cid, 0x00, sizeof cid);
	memset(ptn_sn, 0x00, sizeof ptn_sn);
	memset(end_telco, 0x00, sizeof end_telco);
	memset(cmms_id,	0x00, sizeof(cmms_id));
	memset(resv_data,	0x00, sizeof(resv_data));

	char sNum[24+1];
	char rNum[24+1];
	memset(sNum, 0x00, 24+1);
	memset(rNum, 0x00, 24+1);
	
	//map<string, string>::iterator FindItr;
	
	//FindItr = mapReport.find("msg_id");
	
	mms_id = rpt_data.nMMSId;
	
	sprintf(cmms_id,"%lld", mms_id);
	
	sprintf(msg_id, "%lld", mms_id); //msg_id
	
	sprintf(cid, "%s", rpt_data.szCid); //msg_id
	
	time(&tm_time);
	st_time = localtime(&tm_time);
	strftime(buff, 1024, "%Y%m%d%H%M%S", st_time);
	cTime = buff;
	
	sprintf(dlv_date, "%s", cTime.c_str()); //reg_snd_dttm
	if (strcmp(dlv_date, "") == 0 || strcmp(dlv_date, "              ") == 0)
	{//통신사로 발송된 시간이 없으면 임의의 값 입력
		strcpy(dlv_date,"19710101000000");
	}

			
	sprintf(res_code, "%d", rpt_data.res_code); //rslt_val

	sprintf(end_telco, "%s", "KKO");

	sprintf(res_text, "%s", rpt_data.res_text);
	
	sprintf(ptn_sn, "%s", rpt_data.szPtnSn);
	
	telco_id = rpt_data.nTelcoId;
	res_type = 0;

	type = rpt_data.nType;
	//strcpy(snd_numb, sNum);
	//FindItr = mapReport.find("rcv_numb");
	//sprintf(rcv_numb, FindItr->second.c_str());
	
	if(type != 1)
	{
		sprintf(rcv_numb, "%s", rpt_data.szDstAddr);
	}
	
	//int retry_cnt = 0;
	
	
//retry:
	if(type == 1)
	{
		/* EXEC SQL EXECUTE
		BEGIN
			proc_set_rpt_skb(:cmms_id, :msg_id, :dlv_date, :snd_numb, :rcv_numb, 
			    :res_code, :res_text, :telco_id, :res_type, :end_telco, 
			    :ot_sqlcode, :ot_sqlmsg, :ot_queue_sqlcode, :ot_queue_sqlmsg);
		END;
		END-EXEC; */ 

{
  struct sqlexd sqlstm;
  sqlstm.sqlvsn = 13;
  sqlstm.arrsiz = 35;
  sqlstm.sqladtp = &sqladt;
  sqlstm.sqltdsp = &sqltds;
  sqlstm.stmt = "begin proc_set_rpt_skb ( :cmms_id , :msg_id , :dlv_date , :\
snd_numb , :rcv_numb , :res_code , :res_text , :telco_id , :res_type , :end_te\
lco , :ot_sqlcode , :ot_sqlmsg , :ot_queue_sqlcode , :ot_queue_sqlmsg ) ; END \
;";
  sqlstm.iters = (unsigned int  )1;
  sqlstm.offset = (unsigned int  )1012;
  sqlstm.cud = sqlcud0;
  sqlstm.sqlest = (unsigned char  *)&sqlca;
  sqlstm.sqlety = (unsigned short)4352;
  sqlstm.occurs = (unsigned int  )0;
  sqlstm.sqhstv[0] = (unsigned char  *)cmms_id;
  sqlstm.sqhstl[0] = (unsigned long )33;
  sqlstm.sqhsts[0] = (         int  )0;
  sqlstm.sqindv[0] = (         short *)0;
  sqlstm.sqinds[0] = (         int  )0;
  sqlstm.sqharm[0] = (unsigned long )0;
  sqlstm.sqadto[0] = (unsigned short )0;
  sqlstm.sqtdso[0] = (unsigned short )0;
  sqlstm.sqhstv[1] = (unsigned char  *)msg_id;
  sqlstm.sqhstl[1] = (unsigned long )51;
  sqlstm.sqhsts[1] = (         int  )0;
  sqlstm.sqindv[1] = (         short *)0;
  sqlstm.sqinds[1] = (         int  )0;
  sqlstm.sqharm[1] = (unsigned long )0;
  sqlstm.sqadto[1] = (unsigned short )0;
  sqlstm.sqtdso[1] = (unsigned short )0;
  sqlstm.sqhstv[2] = (unsigned char  *)dlv_date;
  sqlstm.sqhstl[2] = (unsigned long )15;
  sqlstm.sqhsts[2] = (         int  )0;
  sqlstm.sqindv[2] = (         short *)0;
  sqlstm.sqinds[2] = (         int  )0;
  sqlstm.sqharm[2] = (unsigned long )0;
  sqlstm.sqadto[2] = (unsigned short )0;
  sqlstm.sqtdso[2] = (unsigned short )0;
  sqlstm.sqhstv[3] = (unsigned char  *)snd_numb;
  sqlstm.sqhstl[3] = (unsigned long )13;
  sqlstm.sqhsts[3] = (         int  )0;
  sqlstm.sqindv[3] = (         short *)0;
  sqlstm.sqinds[3] = (         int  )0;
  sqlstm.sqharm[3] = (unsigned long )0;
  sqlstm.sqadto[3] = (unsigned short )0;
  sqlstm.sqtdso[3] = (unsigned short )0;
  sqlstm.sqhstv[4] = (unsigned char  *)rcv_numb;
  sqlstm.sqhstl[4] = (unsigned long )13;
  sqlstm.sqhsts[4] = (         int  )0;
  sqlstm.sqindv[4] = (         short *)0;
  sqlstm.sqinds[4] = (         int  )0;
  sqlstm.sqharm[4] = (unsigned long )0;
  sqlstm.sqadto[4] = (unsigned short )0;
  sqlstm.sqtdso[4] = (unsigned short )0;
  sqlstm.sqhstv[5] = (unsigned char  *)res_code;
  sqlstm.sqhstl[5] = (unsigned long )5;
  sqlstm.sqhsts[5] = (         int  )0;
  sqlstm.sqindv[5] = (         short *)0;
  sqlstm.sqinds[5] = (         int  )0;
  sqlstm.sqharm[5] = (unsigned long )0;
  sqlstm.sqadto[5] = (unsigned short )0;
  sqlstm.sqtdso[5] = (unsigned short )0;
  sqlstm.sqhstv[6] = (unsigned char  *)res_text;
  sqlstm.sqhstl[6] = (unsigned long )201;
  sqlstm.sqhsts[6] = (         int  )0;
  sqlstm.sqindv[6] = (         short *)0;
  sqlstm.sqinds[6] = (         int  )0;
  sqlstm.sqharm[6] = (unsigned long )0;
  sqlstm.sqadto[6] = (unsigned short )0;
  sqlstm.sqtdso[6] = (unsigned short )0;
  sqlstm.sqhstv[7] = (unsigned char  *)&telco_id;
  sqlstm.sqhstl[7] = (unsigned long )sizeof(int);
  sqlstm.sqhsts[7] = (         int  )0;
  sqlstm.sqindv[7] = (         short *)0;
  sqlstm.sqinds[7] = (         int  )0;
  sqlstm.sqharm[7] = (unsigned long )0;
  sqlstm.sqadto[7] = (unsigned short )0;
  sqlstm.sqtdso[7] = (unsigned short )0;
  sqlstm.sqhstv[8] = (unsigned char  *)&res_type;
  sqlstm.sqhstl[8] = (unsigned long )sizeof(int);
  sqlstm.sqhsts[8] = (         int  )0;
  sqlstm.sqindv[8] = (         short *)0;
  sqlstm.sqinds[8] = (         int  )0;
  sqlstm.sqharm[8] = (unsigned long )0;
  sqlstm.sqadto[8] = (unsigned short )0;
  sqlstm.sqtdso[8] = (unsigned short )0;
  sqlstm.sqhstv[9] = (unsigned char  *)end_telco;
  sqlstm.sqhstl[9] = (unsigned long )6;
  sqlstm.sqhsts[9] = (         int  )0;
  sqlstm.sqindv[9] = (         short *)0;
  sqlstm.sqinds[9] = (         int  )0;
  sqlstm.sqharm[9] = (unsigned long )0;
  sqlstm.sqadto[9] = (unsigned short )0;
  sqlstm.sqtdso[9] = (unsigned short )0;
  sqlstm.sqhstv[10] = (unsigned char  *)&ot_sqlcode;
  sqlstm.sqhstl[10] = (unsigned long )sizeof(int);
  sqlstm.sqhsts[10] = (         int  )0;
  sqlstm.sqindv[10] = (         short *)0;
  sqlstm.sqinds[10] = (         int  )0;
  sqlstm.sqharm[10] = (unsigned long )0;
  sqlstm.sqadto[10] = (unsigned short )0;
  sqlstm.sqtdso[10] = (unsigned short )0;
  sqlstm.sqhstv[11] = (unsigned char  *)ot_sqlmsg;
  sqlstm.sqhstl[11] = (unsigned long )1024;
  sqlstm.sqhsts[11] = (         int  )0;
  sqlstm.sqindv[11] = (         short *)0;
  sqlstm.sqinds[11] = (         int  )0;
  sqlstm.sqharm[11] = (unsigned long )0;
  sqlstm.sqadto[11] = (unsigned short )0;
  sqlstm.sqtdso[11] = (unsigned short )0;
  sqlstm.sqhstv[12] = (unsigned char  *)&ot_queue_sqlcode;
  sqlstm.sqhstl[12] = (unsigned long )sizeof(int);
  sqlstm.sqhsts[12] = (         int  )0;
  sqlstm.sqindv[12] = (         short *)0;
  sqlstm.sqinds[12] = (         int  )0;
  sqlstm.sqharm[12] = (unsigned long )0;
  sqlstm.sqadto[12] = (unsigned short )0;
  sqlstm.sqtdso[12] = (unsigned short )0;
  sqlstm.sqhstv[13] = (unsigned char  *)ot_queue_sqlmsg;
  sqlstm.sqhstl[13] = (unsigned long )1024;
  sqlstm.sqhsts[13] = (         int  )0;
  sqlstm.sqindv[13] = (         short *)0;
  sqlstm.sqinds[13] = (         int  )0;
  sqlstm.sqharm[13] = (unsigned long )0;
  sqlstm.sqadto[13] = (unsigned short )0;
  sqlstm.sqtdso[13] = (unsigned short )0;
  sqlstm.sqphsv = sqlstm.sqhstv;
  sqlstm.sqphsl = sqlstm.sqhstl;
  sqlstm.sqphss = sqlstm.sqhsts;
  sqlstm.sqpind = sqlstm.sqindv;
  sqlstm.sqpins = sqlstm.sqinds;
  sqlstm.sqparm = sqlstm.sqharm;
  sqlstm.sqparc = sqlstm.sqharc;
  sqlstm.sqpadto = sqlstm.sqadto;
  sqlstm.sqptdso = sqlstm.sqtdso;
  sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}


    	
		if( ot_sqlcode != 0 )
		{
			sprintf(tmpLog3, "CDatabaseORA::setReportData() SNDTBL ERROR MMSID[%lld][%d][%.100s]", 
				mms_id, ot_sqlcode, ot_sqlmsg);
			_logPrint(_DATALOG, tmpLog3);
			return -1;
		}
		
		if( ot_queue_sqlcode != 0 )
		{
			sprintf(tmpLog3, "CDatabaseORA::setReportData() SNDTBL queue ERROR MMSID[%lld][%d][%.100s]", 
				mms_id, ot_queue_sqlcode, ot_queue_sqlmsg);
			_logPrint(_DATALOG, tmpLog3);
			return -1;
		}
	}
	else
	{
		/* EXEC SQL EXECUTE
		BEGIN
			proc_set_mms_rpt_queue(:cid, :ptn_sn, :res_code, :telco_id, :end_telco, 
			    :cmms_id, :resv_data, :rcv_numb, :dlv_date, :ot_sqlcode, :ot_sqlmsg);
		END;
		END-EXEC; */ 

{
  struct sqlexd sqlstm;
  sqlstm.sqlvsn = 13;
  sqlstm.arrsiz = 35;
  sqlstm.sqladtp = &sqladt;
  sqlstm.sqltdsp = &sqltds;
  sqlstm.stmt = "begin proc_set_mms_rpt_queue ( :cid , :ptn_sn , :res_code ,\
 :telco_id , :end_telco , :cmms_id , :resv_data , :rcv_numb , :dlv_date , :ot_\
sqlcode , :ot_sqlmsg ) ; END ;";
  sqlstm.iters = (unsigned int  )1;
  sqlstm.offset = (unsigned int  )1083;
  sqlstm.cud = sqlcud0;
  sqlstm.sqlest = (unsigned char  *)&sqlca;
  sqlstm.sqlety = (unsigned short)4352;
  sqlstm.occurs = (unsigned int  )0;
  sqlstm.sqhstv[0] = (unsigned char  *)cid;
  sqlstm.sqhstl[0] = (unsigned long )11;
  sqlstm.sqhsts[0] = (         int  )0;
  sqlstm.sqindv[0] = (         short *)0;
  sqlstm.sqinds[0] = (         int  )0;
  sqlstm.sqharm[0] = (unsigned long )0;
  sqlstm.sqadto[0] = (unsigned short )0;
  sqlstm.sqtdso[0] = (unsigned short )0;
  sqlstm.sqhstv[1] = (unsigned char  *)ptn_sn;
  sqlstm.sqhstl[1] = (unsigned long )17;
  sqlstm.sqhsts[1] = (         int  )0;
  sqlstm.sqindv[1] = (         short *)0;
  sqlstm.sqinds[1] = (         int  )0;
  sqlstm.sqharm[1] = (unsigned long )0;
  sqlstm.sqadto[1] = (unsigned short )0;
  sqlstm.sqtdso[1] = (unsigned short )0;
  sqlstm.sqhstv[2] = (unsigned char  *)res_code;
  sqlstm.sqhstl[2] = (unsigned long )5;
  sqlstm.sqhsts[2] = (         int  )0;
  sqlstm.sqindv[2] = (         short *)0;
  sqlstm.sqinds[2] = (         int  )0;
  sqlstm.sqharm[2] = (unsigned long )0;
  sqlstm.sqadto[2] = (unsigned short )0;
  sqlstm.sqtdso[2] = (unsigned short )0;
  sqlstm.sqhstv[3] = (unsigned char  *)&telco_id;
  sqlstm.sqhstl[3] = (unsigned long )sizeof(int);
  sqlstm.sqhsts[3] = (         int  )0;
  sqlstm.sqindv[3] = (         short *)0;
  sqlstm.sqinds[3] = (         int  )0;
  sqlstm.sqharm[3] = (unsigned long )0;
  sqlstm.sqadto[3] = (unsigned short )0;
  sqlstm.sqtdso[3] = (unsigned short )0;
  sqlstm.sqhstv[4] = (unsigned char  *)end_telco;
  sqlstm.sqhstl[4] = (unsigned long )6;
  sqlstm.sqhsts[4] = (         int  )0;
  sqlstm.sqindv[4] = (         short *)0;
  sqlstm.sqinds[4] = (         int  )0;
  sqlstm.sqharm[4] = (unsigned long )0;
  sqlstm.sqadto[4] = (unsigned short )0;
  sqlstm.sqtdso[4] = (unsigned short )0;
  sqlstm.sqhstv[5] = (unsigned char  *)cmms_id;
  sqlstm.sqhstl[5] = (unsigned long )33;
  sqlstm.sqhsts[5] = (         int  )0;
  sqlstm.sqindv[5] = (         short *)0;
  sqlstm.sqinds[5] = (         int  )0;
  sqlstm.sqharm[5] = (unsigned long )0;
  sqlstm.sqadto[5] = (unsigned short )0;
  sqlstm.sqtdso[5] = (unsigned short )0;
  sqlstm.sqhstv[6] = (unsigned char  *)resv_data;
  sqlstm.sqhstl[6] = (unsigned long )201;
  sqlstm.sqhsts[6] = (         int  )0;
  sqlstm.sqindv[6] = (         short *)0;
  sqlstm.sqinds[6] = (         int  )0;
  sqlstm.sqharm[6] = (unsigned long )0;
  sqlstm.sqadto[6] = (unsigned short )0;
  sqlstm.sqtdso[6] = (unsigned short )0;
  sqlstm.sqhstv[7] = (unsigned char  *)rcv_numb;
  sqlstm.sqhstl[7] = (unsigned long )13;
  sqlstm.sqhsts[7] = (         int  )0;
  sqlstm.sqindv[7] = (         short *)0;
  sqlstm.sqinds[7] = (         int  )0;
  sqlstm.sqharm[7] = (unsigned long )0;
  sqlstm.sqadto[7] = (unsigned short )0;
  sqlstm.sqtdso[7] = (unsigned short )0;
  sqlstm.sqhstv[8] = (unsigned char  *)dlv_date;
  sqlstm.sqhstl[8] = (unsigned long )15;
  sqlstm.sqhsts[8] = (         int  )0;
  sqlstm.sqindv[8] = (         short *)0;
  sqlstm.sqinds[8] = (         int  )0;
  sqlstm.sqharm[8] = (unsigned long )0;
  sqlstm.sqadto[8] = (unsigned short )0;
  sqlstm.sqtdso[8] = (unsigned short )0;
  sqlstm.sqhstv[9] = (unsigned char  *)&ot_sqlcode;
  sqlstm.sqhstl[9] = (unsigned long )sizeof(int);
  sqlstm.sqhsts[9] = (         int  )0;
  sqlstm.sqindv[9] = (         short *)0;
  sqlstm.sqinds[9] = (         int  )0;
  sqlstm.sqharm[9] = (unsigned long )0;
  sqlstm.sqadto[9] = (unsigned short )0;
  sqlstm.sqtdso[9] = (unsigned short )0;
  sqlstm.sqhstv[10] = (unsigned char  *)ot_sqlmsg;
  sqlstm.sqhstl[10] = (unsigned long )1024;
  sqlstm.sqhsts[10] = (         int  )0;
  sqlstm.sqindv[10] = (         short *)0;
  sqlstm.sqinds[10] = (         int  )0;
  sqlstm.sqharm[10] = (unsigned long )0;
  sqlstm.sqadto[10] = (unsigned short )0;
  sqlstm.sqtdso[10] = (unsigned short )0;
  sqlstm.sqphsv = sqlstm.sqhstv;
  sqlstm.sqphsl = sqlstm.sqhstl;
  sqlstm.sqphss = sqlstm.sqhsts;
  sqlstm.sqpind = sqlstm.sqindv;
  sqlstm.sqpins = sqlstm.sqinds;
  sqlstm.sqparm = sqlstm.sqharm;
  sqlstm.sqparc = sqlstm.sqharc;
  sqlstm.sqpadto = sqlstm.sqadto;
  sqlstm.sqptdso = sqlstm.sqtdso;
  sqlcxt((void **)0, &sqlctx, &sqlstm, &sqlfpn);
}


    	
		if( ot_sqlcode != 0 )
		{
			sprintf(tmpLog3, "CDatabaseORA::setReportData() SND queue ERROR MMSID[%lld][%d][%.100s]", 
				mms_id, ot_sqlcode, ot_sqlmsg);
			_logPrint(_DATALOG, tmpLog3);
			return -1;
		}
	}
	
	return 0;
}

}
