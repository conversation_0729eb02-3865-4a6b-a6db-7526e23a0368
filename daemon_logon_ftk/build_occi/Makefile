# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/CLionProjects/ftalk_up/daemon_logon_ftk

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/CLionProjects/ftalk_up/daemon_logon_ftk/build_occi

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/ftalk_up/daemon_logon_ftk/build_occi/CMakeFiles /home/<USER>/CLionProjects/ftalk_up/daemon_logon_ftk/build_occi//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/ftalk_up/daemon_logon_ftk/build_occi/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named daemon_logon_lib

# Build rule for target.
daemon_logon_lib: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 daemon_logon_lib
.PHONY : daemon_logon_lib

# fast build rule for target.
daemon_logon_lib/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/build
.PHONY : daemon_logon_lib/fast

#=============================================================================
# Target rules for targets named database_ora_mms

# Build rule for target.
database_ora_mms: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 database_ora_mms
.PHONY : database_ora_mms

# fast build rule for target.
database_ora_mms/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/database_ora_mms.dir/build.make CMakeFiles/database_ora_mms.dir/build
.PHONY : database_ora_mms/fast

#=============================================================================
# Target rules for targets named logonDB

# Build rule for target.
logonDB: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 logonDB
.PHONY : logonDB

# fast build rule for target.
logonDB/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/logonDB.dir/build.make CMakeFiles/logonDB.dir/build
.PHONY : logonDB/fast

#=============================================================================
# Target rules for targets named logonSession

# Build rule for target.
logonSession: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 logonSession
.PHONY : logonSession

# fast build rule for target.
logonSession/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/logonSession.dir/build.make CMakeFiles/logonSession.dir/build
.PHONY : logonSession/fast

#=============================================================================
# Target rules for targets named adminProcess

# Build rule for target.
adminProcess: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 adminProcess
.PHONY : adminProcess

# fast build rule for target.
adminProcess/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/adminProcess.dir/build.make CMakeFiles/adminProcess.dir/build
.PHONY : adminProcess/fast

#=============================================================================
# Target rules for targets named monitorProcess

# Build rule for target.
monitorProcess: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 monitorProcess
.PHONY : monitorProcess

# fast build rule for target.
monitorProcess/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/monitorProcess.dir/build.make CMakeFiles/monitorProcess.dir/build
.PHONY : monitorProcess/fast

#=============================================================================
# Target rules for targets named senderFtalkProDB

# Build rule for target.
senderFtalkProDB: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 senderFtalkProDB
.PHONY : senderFtalkProDB

# fast build rule for target.
senderFtalkProDB/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/senderFtalkProDB.dir/build.make CMakeFiles/senderFtalkProDB.dir/build
.PHONY : senderFtalkProDB/fast

#=============================================================================
# Target rules for targets named admin

# Build rule for target.
admin: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 admin
.PHONY : admin

# fast build rule for target.
admin/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/admin.dir/build.make CMakeFiles/admin.dir/build
.PHONY : admin/fast

#=============================================================================
# Target rules for targets named reportMMSDB

# Build rule for target.
reportMMSDB: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 reportMMSDB
.PHONY : reportMMSDB

# fast build rule for target.
reportMMSDB/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/reportMMSDB.dir/build.make CMakeFiles/reportMMSDB.dir/build
.PHONY : reportMMSDB/fast

#=============================================================================
# Target rules for targets named reportMMSProcDB

# Build rule for target.
reportMMSProcDB: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 reportMMSProcDB
.PHONY : reportMMSProcDB

# fast build rule for target.
reportMMSProcDB/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/reportMMSProcDB.dir/build.make CMakeFiles/reportMMSProcDB.dir/build
.PHONY : reportMMSProcDB/fast

#=============================================================================
# Target rules for targets named makefile_build_daemon_logon

# Build rule for target.
makefile_build_daemon_logon: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 makefile_build_daemon_logon
.PHONY : makefile_build_daemon_logon

# fast build rule for target.
makefile_build_daemon_logon/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/makefile_build_daemon_logon.dir/build.make CMakeFiles/makefile_build_daemon_logon.dir/build
.PHONY : makefile_build_daemon_logon/fast

#=============================================================================
# Target rules for targets named makefile_clean_daemon_logon

# Build rule for target.
makefile_clean_daemon_logon: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 makefile_clean_daemon_logon
.PHONY : makefile_clean_daemon_logon

# fast build rule for target.
makefile_clean_daemon_logon/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/makefile_clean_daemon_logon.dir/build.make CMakeFiles/makefile_clean_daemon_logon.dir/build
.PHONY : makefile_clean_daemon_logon/fast

DatabaseORA_MMS.o: DatabaseORA_MMS.cpp.o
.PHONY : DatabaseORA_MMS.o

# target to build an object file
DatabaseORA_MMS.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/database_ora_mms.dir/build.make CMakeFiles/database_ora_mms.dir/DatabaseORA_MMS.cpp.o
.PHONY : DatabaseORA_MMS.cpp.o

DatabaseORA_MMS.i: DatabaseORA_MMS.cpp.i
.PHONY : DatabaseORA_MMS.i

# target to preprocess a source file
DatabaseORA_MMS.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/database_ora_mms.dir/build.make CMakeFiles/database_ora_mms.dir/DatabaseORA_MMS.cpp.i
.PHONY : DatabaseORA_MMS.cpp.i

DatabaseORA_MMS.s: DatabaseORA_MMS.cpp.s
.PHONY : DatabaseORA_MMS.s

# target to generate assembly for a file
DatabaseORA_MMS.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/database_ora_mms.dir/build.make CMakeFiles/database_ora_mms.dir/DatabaseORA_MMS.cpp.s
.PHONY : DatabaseORA_MMS.cpp.s

lib/Curl.o: lib/Curl.cpp.o
.PHONY : lib/Curl.o

# target to build an object file
lib/Curl.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/Curl.cpp.o
.PHONY : lib/Curl.cpp.o

lib/Curl.i: lib/Curl.cpp.i
.PHONY : lib/Curl.i

# target to preprocess a source file
lib/Curl.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/Curl.cpp.i
.PHONY : lib/Curl.cpp.i

lib/Curl.s: lib/Curl.cpp.s
.PHONY : lib/Curl.s

# target to generate assembly for a file
lib/Curl.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/Curl.cpp.s
.PHONY : lib/Curl.cpp.s

lib/Encrypt.o: lib/Encrypt.cpp.o
.PHONY : lib/Encrypt.o

# target to build an object file
lib/Encrypt.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/Encrypt.cpp.o
.PHONY : lib/Encrypt.cpp.o

lib/Encrypt.i: lib/Encrypt.cpp.i
.PHONY : lib/Encrypt.i

# target to preprocess a source file
lib/Encrypt.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/Encrypt.cpp.i
.PHONY : lib/Encrypt.cpp.i

lib/Encrypt.s: lib/Encrypt.cpp.s
.PHONY : lib/Encrypt.s

# target to generate assembly for a file
lib/Encrypt.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/Encrypt.cpp.s
.PHONY : lib/Encrypt.cpp.s

lib/adminUtil.o: lib/adminUtil.cpp.o
.PHONY : lib/adminUtil.o

# target to build an object file
lib/adminUtil.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/adminUtil.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/admin.dir/build.make CMakeFiles/admin.dir/lib/adminUtil.cpp.o
.PHONY : lib/adminUtil.cpp.o

lib/adminUtil.i: lib/adminUtil.cpp.i
.PHONY : lib/adminUtil.i

# target to preprocess a source file
lib/adminUtil.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/adminUtil.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/admin.dir/build.make CMakeFiles/admin.dir/lib/adminUtil.cpp.i
.PHONY : lib/adminUtil.cpp.i

lib/adminUtil.s: lib/adminUtil.cpp.s
.PHONY : lib/adminUtil.s

# target to generate assembly for a file
lib/adminUtil.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/adminUtil.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/admin.dir/build.make CMakeFiles/admin.dir/lib/adminUtil.cpp.s
.PHONY : lib/adminUtil.cpp.s

lib/checkCallback.o: lib/checkCallback.cpp.o
.PHONY : lib/checkCallback.o

# target to build an object file
lib/checkCallback.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/checkCallback.cpp.o
.PHONY : lib/checkCallback.cpp.o

lib/checkCallback.i: lib/checkCallback.cpp.i
.PHONY : lib/checkCallback.i

# target to preprocess a source file
lib/checkCallback.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/checkCallback.cpp.i
.PHONY : lib/checkCallback.cpp.i

lib/checkCallback.s: lib/checkCallback.cpp.s
.PHONY : lib/checkCallback.s

# target to generate assembly for a file
lib/checkCallback.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/checkCallback.cpp.s
.PHONY : lib/checkCallback.cpp.s

lib/child_common.o: lib/child_common.cpp.o
.PHONY : lib/child_common.o

# target to build an object file
lib/child_common.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/child_common.cpp.o
.PHONY : lib/child_common.cpp.o

lib/child_common.i: lib/child_common.cpp.i
.PHONY : lib/child_common.i

# target to preprocess a source file
lib/child_common.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/child_common.cpp.i
.PHONY : lib/child_common.cpp.i

lib/child_common.s: lib/child_common.cpp.s
.PHONY : lib/child_common.s

# target to generate assembly for a file
lib/child_common.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/child_common.cpp.s
.PHONY : lib/child_common.cpp.s

lib/cust_lib_common.o: lib/cust_lib_common.cpp.o
.PHONY : lib/cust_lib_common.o

# target to build an object file
lib/cust_lib_common.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/cust_lib_common.cpp.o
.PHONY : lib/cust_lib_common.cpp.o

lib/cust_lib_common.i: lib/cust_lib_common.cpp.i
.PHONY : lib/cust_lib_common.i

# target to preprocess a source file
lib/cust_lib_common.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/cust_lib_common.cpp.i
.PHONY : lib/cust_lib_common.cpp.i

lib/cust_lib_common.s: lib/cust_lib_common.cpp.s
.PHONY : lib/cust_lib_common.s

# target to generate assembly for a file
lib/cust_lib_common.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/cust_lib_common.cpp.s
.PHONY : lib/cust_lib_common.cpp.s

lib/dbUtil.o: lib/dbUtil.cpp.o
.PHONY : lib/dbUtil.o

# target to build an object file
lib/dbUtil.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/dbUtil.cpp.o
.PHONY : lib/dbUtil.cpp.o

lib/dbUtil.i: lib/dbUtil.cpp.i
.PHONY : lib/dbUtil.i

# target to preprocess a source file
lib/dbUtil.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/dbUtil.cpp.i
.PHONY : lib/dbUtil.cpp.i

lib/dbUtil.s: lib/dbUtil.cpp.s
.PHONY : lib/dbUtil.s

# target to generate assembly for a file
lib/dbUtil.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/dbUtil.cpp.s
.PHONY : lib/dbUtil.cpp.s

lib/jsoncpp.o: lib/jsoncpp.cpp.o
.PHONY : lib/jsoncpp.o

# target to build an object file
lib/jsoncpp.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/jsoncpp.cpp.o
.PHONY : lib/jsoncpp.cpp.o

lib/jsoncpp.i: lib/jsoncpp.cpp.i
.PHONY : lib/jsoncpp.i

# target to preprocess a source file
lib/jsoncpp.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/jsoncpp.cpp.i
.PHONY : lib/jsoncpp.cpp.i

lib/jsoncpp.s: lib/jsoncpp.cpp.s
.PHONY : lib/jsoncpp.s

# target to generate assembly for a file
lib/jsoncpp.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/jsoncpp.cpp.s
.PHONY : lib/jsoncpp.cpp.s

lib/logonUtil.o: lib/logonUtil.cpp.o
.PHONY : lib/logonUtil.o

# target to build an object file
lib/logonUtil.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/logonUtil.cpp.o
.PHONY : lib/logonUtil.cpp.o

lib/logonUtil.i: lib/logonUtil.cpp.i
.PHONY : lib/logonUtil.i

# target to preprocess a source file
lib/logonUtil.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/logonUtil.cpp.i
.PHONY : lib/logonUtil.cpp.i

lib/logonUtil.s: lib/logonUtil.cpp.s
.PHONY : lib/logonUtil.s

# target to generate assembly for a file
lib/logonUtil.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/logonUtil.cpp.s
.PHONY : lib/logonUtil.cpp.s

lib/mmsFileProcess.o: lib/mmsFileProcess.cpp.o
.PHONY : lib/mmsFileProcess.o

# target to build an object file
lib/mmsFileProcess.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/mmsFileProcess.cpp.o
.PHONY : lib/mmsFileProcess.cpp.o

lib/mmsFileProcess.i: lib/mmsFileProcess.cpp.i
.PHONY : lib/mmsFileProcess.i

# target to preprocess a source file
lib/mmsFileProcess.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/mmsFileProcess.cpp.i
.PHONY : lib/mmsFileProcess.cpp.i

lib/mmsFileProcess.s: lib/mmsFileProcess.cpp.s
.PHONY : lib/mmsFileProcess.s

# target to generate assembly for a file
lib/mmsFileProcess.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/mmsFileProcess.cpp.s
.PHONY : lib/mmsFileProcess.cpp.s

lib/mmsFileProcessBS.o: lib/mmsFileProcessBS.cpp.o
.PHONY : lib/mmsFileProcessBS.o

# target to build an object file
lib/mmsFileProcessBS.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/mmsFileProcessBS.cpp.o
.PHONY : lib/mmsFileProcessBS.cpp.o

lib/mmsFileProcessBS.i: lib/mmsFileProcessBS.cpp.i
.PHONY : lib/mmsFileProcessBS.i

# target to preprocess a source file
lib/mmsFileProcessBS.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/mmsFileProcessBS.cpp.i
.PHONY : lib/mmsFileProcessBS.cpp.i

lib/mmsFileProcessBS.s: lib/mmsFileProcessBS.cpp.s
.PHONY : lib/mmsFileProcessBS.s

# target to generate assembly for a file
lib/mmsFileProcessBS.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/mmsFileProcessBS.cpp.s
.PHONY : lib/mmsFileProcessBS.cpp.s

lib/mmsPacketBase.o: lib/mmsPacketBase.cpp.o
.PHONY : lib/mmsPacketBase.o

# target to build an object file
lib/mmsPacketBase.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/mmsPacketBase.cpp.o
.PHONY : lib/mmsPacketBase.cpp.o

lib/mmsPacketBase.i: lib/mmsPacketBase.cpp.i
.PHONY : lib/mmsPacketBase.i

# target to preprocess a source file
lib/mmsPacketBase.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/mmsPacketBase.cpp.i
.PHONY : lib/mmsPacketBase.cpp.i

lib/mmsPacketBase.s: lib/mmsPacketBase.cpp.s
.PHONY : lib/mmsPacketBase.s

# target to generate assembly for a file
lib/mmsPacketBase.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/mmsPacketBase.cpp.s
.PHONY : lib/mmsPacketBase.cpp.s

lib/mmsPacketSend.o: lib/mmsPacketSend.cpp.o
.PHONY : lib/mmsPacketSend.o

# target to build an object file
lib/mmsPacketSend.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/mmsPacketSend.cpp.o
.PHONY : lib/mmsPacketSend.cpp.o

lib/mmsPacketSend.i: lib/mmsPacketSend.cpp.i
.PHONY : lib/mmsPacketSend.i

# target to preprocess a source file
lib/mmsPacketSend.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/mmsPacketSend.cpp.i
.PHONY : lib/mmsPacketSend.cpp.i

lib/mmsPacketSend.s: lib/mmsPacketSend.cpp.s
.PHONY : lib/mmsPacketSend.s

# target to generate assembly for a file
lib/mmsPacketSend.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/mmsPacketSend.cpp.s
.PHONY : lib/mmsPacketSend.cpp.s

lib/monitor.o: lib/monitor.cpp.o
.PHONY : lib/monitor.o

# target to build an object file
lib/monitor.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/monitor.cpp.o
.PHONY : lib/monitor.cpp.o

lib/monitor.i: lib/monitor.cpp.i
.PHONY : lib/monitor.i

# target to preprocess a source file
lib/monitor.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/monitor.cpp.i
.PHONY : lib/monitor.cpp.i

lib/monitor.s: lib/monitor.cpp.s
.PHONY : lib/monitor.s

# target to generate assembly for a file
lib/monitor.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/monitor.cpp.s
.PHONY : lib/monitor.cpp.s

lib/packetUtil.o: lib/packetUtil.cpp.o
.PHONY : lib/packetUtil.o

# target to build an object file
lib/packetUtil.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/packetUtil.cpp.o
.PHONY : lib/packetUtil.cpp.o

lib/packetUtil.i: lib/packetUtil.cpp.i
.PHONY : lib/packetUtil.i

# target to preprocess a source file
lib/packetUtil.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/packetUtil.cpp.i
.PHONY : lib/packetUtil.cpp.i

lib/packetUtil.s: lib/packetUtil.cpp.s
.PHONY : lib/packetUtil.s

# target to generate assembly for a file
lib/packetUtil.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/daemon_logon_lib.dir/build.make CMakeFiles/daemon_logon_lib.dir/lib/packetUtil.cpp.s
.PHONY : lib/packetUtil.cpp.s

reportMMSDB.o: reportMMSDB.cpp.o
.PHONY : reportMMSDB.o

# target to build an object file
reportMMSDB.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/reportMMSDB.dir/build.make CMakeFiles/reportMMSDB.dir/reportMMSDB.cpp.o
.PHONY : reportMMSDB.cpp.o

reportMMSDB.i: reportMMSDB.cpp.i
.PHONY : reportMMSDB.i

# target to preprocess a source file
reportMMSDB.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/reportMMSDB.dir/build.make CMakeFiles/reportMMSDB.dir/reportMMSDB.cpp.i
.PHONY : reportMMSDB.cpp.i

reportMMSDB.s: reportMMSDB.cpp.s
.PHONY : reportMMSDB.s

# target to generate assembly for a file
reportMMSDB.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/reportMMSDB.dir/build.make CMakeFiles/reportMMSDB.dir/reportMMSDB.cpp.s
.PHONY : reportMMSDB.cpp.s

reportMMSProcessDB.o: reportMMSProcessDB.cpp.o
.PHONY : reportMMSProcessDB.o

# target to build an object file
reportMMSProcessDB.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/reportMMSProcDB.dir/build.make CMakeFiles/reportMMSProcDB.dir/reportMMSProcessDB.cpp.o
.PHONY : reportMMSProcessDB.cpp.o

reportMMSProcessDB.i: reportMMSProcessDB.cpp.i
.PHONY : reportMMSProcessDB.i

# target to preprocess a source file
reportMMSProcessDB.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/reportMMSProcDB.dir/build.make CMakeFiles/reportMMSProcDB.dir/reportMMSProcessDB.cpp.i
.PHONY : reportMMSProcessDB.cpp.i

reportMMSProcessDB.s: reportMMSProcessDB.cpp.s
.PHONY : reportMMSProcessDB.s

# target to generate assembly for a file
reportMMSProcessDB.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/reportMMSProcDB.dir/build.make CMakeFiles/reportMMSProcDB.dir/reportMMSProcessDB.cpp.s
.PHONY : reportMMSProcessDB.cpp.s

senderFtalkProDB.o: senderFtalkProDB.cpp.o
.PHONY : senderFtalkProDB.o

# target to build an object file
senderFtalkProDB.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/senderFtalkProDB.dir/build.make CMakeFiles/senderFtalkProDB.dir/senderFtalkProDB.cpp.o
.PHONY : senderFtalkProDB.cpp.o

senderFtalkProDB.i: senderFtalkProDB.cpp.i
.PHONY : senderFtalkProDB.i

# target to preprocess a source file
senderFtalkProDB.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/senderFtalkProDB.dir/build.make CMakeFiles/senderFtalkProDB.dir/senderFtalkProDB.cpp.i
.PHONY : senderFtalkProDB.cpp.i

senderFtalkProDB.s: senderFtalkProDB.cpp.s
.PHONY : senderFtalkProDB.s

# target to generate assembly for a file
senderFtalkProDB.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/senderFtalkProDB.dir/build.make CMakeFiles/senderFtalkProDB.dir/senderFtalkProDB.cpp.s
.PHONY : senderFtalkProDB.cpp.s

src/admin.o: src/admin.cpp.o
.PHONY : src/admin.o

# target to build an object file
src/admin.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/admin.dir/build.make CMakeFiles/admin.dir/src/admin.cpp.o
.PHONY : src/admin.cpp.o

src/admin.i: src/admin.cpp.i
.PHONY : src/admin.i

# target to preprocess a source file
src/admin.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/admin.dir/build.make CMakeFiles/admin.dir/src/admin.cpp.i
.PHONY : src/admin.cpp.i

src/admin.s: src/admin.cpp.s
.PHONY : src/admin.s

# target to generate assembly for a file
src/admin.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/admin.dir/build.make CMakeFiles/admin.dir/src/admin.cpp.s
.PHONY : src/admin.cpp.s

src/adminProcess.o: src/adminProcess.cpp.o
.PHONY : src/adminProcess.o

# target to build an object file
src/adminProcess.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/adminProcess.dir/build.make CMakeFiles/adminProcess.dir/src/adminProcess.cpp.o
.PHONY : src/adminProcess.cpp.o

src/adminProcess.i: src/adminProcess.cpp.i
.PHONY : src/adminProcess.i

# target to preprocess a source file
src/adminProcess.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/adminProcess.dir/build.make CMakeFiles/adminProcess.dir/src/adminProcess.cpp.i
.PHONY : src/adminProcess.cpp.i

src/adminProcess.s: src/adminProcess.cpp.s
.PHONY : src/adminProcess.s

# target to generate assembly for a file
src/adminProcess.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/adminProcess.dir/build.make CMakeFiles/adminProcess.dir/src/adminProcess.cpp.s
.PHONY : src/adminProcess.cpp.s

src/logonDB.o: src/logonDB.cpp.o
.PHONY : src/logonDB.o

# target to build an object file
src/logonDB.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/logonDB.dir/build.make CMakeFiles/logonDB.dir/src/logonDB.cpp.o
.PHONY : src/logonDB.cpp.o

src/logonDB.i: src/logonDB.cpp.i
.PHONY : src/logonDB.i

# target to preprocess a source file
src/logonDB.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/logonDB.dir/build.make CMakeFiles/logonDB.dir/src/logonDB.cpp.i
.PHONY : src/logonDB.cpp.i

src/logonDB.s: src/logonDB.cpp.s
.PHONY : src/logonDB.s

# target to generate assembly for a file
src/logonDB.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/logonDB.dir/build.make CMakeFiles/logonDB.dir/src/logonDB.cpp.s
.PHONY : src/logonDB.cpp.s

src/logonSession.o: src/logonSession.cpp.o
.PHONY : src/logonSession.o

# target to build an object file
src/logonSession.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/logonSession.dir/build.make CMakeFiles/logonSession.dir/src/logonSession.cpp.o
.PHONY : src/logonSession.cpp.o

src/logonSession.i: src/logonSession.cpp.i
.PHONY : src/logonSession.i

# target to preprocess a source file
src/logonSession.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/logonSession.dir/build.make CMakeFiles/logonSession.dir/src/logonSession.cpp.i
.PHONY : src/logonSession.cpp.i

src/logonSession.s: src/logonSession.cpp.s
.PHONY : src/logonSession.s

# target to generate assembly for a file
src/logonSession.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/logonSession.dir/build.make CMakeFiles/logonSession.dir/src/logonSession.cpp.s
.PHONY : src/logonSession.cpp.s

src/monitorProcess.o: src/monitorProcess.cpp.o
.PHONY : src/monitorProcess.o

# target to build an object file
src/monitorProcess.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/monitorProcess.dir/build.make CMakeFiles/monitorProcess.dir/src/monitorProcess.cpp.o
.PHONY : src/monitorProcess.cpp.o

src/monitorProcess.i: src/monitorProcess.cpp.i
.PHONY : src/monitorProcess.i

# target to preprocess a source file
src/monitorProcess.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/monitorProcess.dir/build.make CMakeFiles/monitorProcess.dir/src/monitorProcess.cpp.i
.PHONY : src/monitorProcess.cpp.i

src/monitorProcess.s: src/monitorProcess.cpp.s
.PHONY : src/monitorProcess.s

# target to generate assembly for a file
src/monitorProcess.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/monitorProcess.dir/build.make CMakeFiles/monitorProcess.dir/src/monitorProcess.cpp.s
.PHONY : src/monitorProcess.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... makefile_build_daemon_logon"
	@echo "... makefile_clean_daemon_logon"
	@echo "... admin"
	@echo "... adminProcess"
	@echo "... daemon_logon_lib"
	@echo "... database_ora_mms"
	@echo "... logonDB"
	@echo "... logonSession"
	@echo "... monitorProcess"
	@echo "... reportMMSDB"
	@echo "... reportMMSProcDB"
	@echo "... senderFtalkProDB"
	@echo "... DatabaseORA_MMS.o"
	@echo "... DatabaseORA_MMS.i"
	@echo "... DatabaseORA_MMS.s"
	@echo "... lib/Curl.o"
	@echo "... lib/Curl.i"
	@echo "... lib/Curl.s"
	@echo "... lib/Encrypt.o"
	@echo "... lib/Encrypt.i"
	@echo "... lib/Encrypt.s"
	@echo "... lib/adminUtil.o"
	@echo "... lib/adminUtil.i"
	@echo "... lib/adminUtil.s"
	@echo "... lib/checkCallback.o"
	@echo "... lib/checkCallback.i"
	@echo "... lib/checkCallback.s"
	@echo "... lib/child_common.o"
	@echo "... lib/child_common.i"
	@echo "... lib/child_common.s"
	@echo "... lib/cust_lib_common.o"
	@echo "... lib/cust_lib_common.i"
	@echo "... lib/cust_lib_common.s"
	@echo "... lib/dbUtil.o"
	@echo "... lib/dbUtil.i"
	@echo "... lib/dbUtil.s"
	@echo "... lib/jsoncpp.o"
	@echo "... lib/jsoncpp.i"
	@echo "... lib/jsoncpp.s"
	@echo "... lib/logonUtil.o"
	@echo "... lib/logonUtil.i"
	@echo "... lib/logonUtil.s"
	@echo "... lib/mmsFileProcess.o"
	@echo "... lib/mmsFileProcess.i"
	@echo "... lib/mmsFileProcess.s"
	@echo "... lib/mmsFileProcessBS.o"
	@echo "... lib/mmsFileProcessBS.i"
	@echo "... lib/mmsFileProcessBS.s"
	@echo "... lib/mmsPacketBase.o"
	@echo "... lib/mmsPacketBase.i"
	@echo "... lib/mmsPacketBase.s"
	@echo "... lib/mmsPacketSend.o"
	@echo "... lib/mmsPacketSend.i"
	@echo "... lib/mmsPacketSend.s"
	@echo "... lib/monitor.o"
	@echo "... lib/monitor.i"
	@echo "... lib/monitor.s"
	@echo "... lib/packetUtil.o"
	@echo "... lib/packetUtil.i"
	@echo "... lib/packetUtil.s"
	@echo "... reportMMSDB.o"
	@echo "... reportMMSDB.i"
	@echo "... reportMMSDB.s"
	@echo "... reportMMSProcessDB.o"
	@echo "... reportMMSProcessDB.i"
	@echo "... reportMMSProcessDB.s"
	@echo "... senderFtalkProDB.o"
	@echo "... senderFtalkProDB.i"
	@echo "... senderFtalkProDB.s"
	@echo "... src/admin.o"
	@echo "... src/admin.i"
	@echo "... src/admin.s"
	@echo "... src/adminProcess.o"
	@echo "... src/adminProcess.i"
	@echo "... src/adminProcess.s"
	@echo "... src/logonDB.o"
	@echo "... src/logonDB.i"
	@echo "... src/logonDB.s"
	@echo "... src/logonSession.o"
	@echo "... src/logonSession.i"
	@echo "... src/logonSession.s"
	@echo "... src/monitorProcess.o"
	@echo "... src/monitorProcess.i"
	@echo "... src/monitorProcess.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

