#------------------------------------------------------------------------------
# Copyright (c) 2017, 2024, Oracle and/or its affiliates.
#
# This software is dual-licensed to you under the Universal Permissive License
# (UPL) 1.0 as shown at https://oss.oracle.com/licenses/upl and Apache License
# 2.0 as shown at http://www.apache.org/licenses/LICENSE-2.0. You may choose
# either license.
#
# If you elect to accept the software under the Apache License, Version 2.0,
# the following applies:
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#------------------------------------------------------------------------------

#------------------------------------------------------------------------------
#
# Sample nmake Makefile showing how ODPI-C can be built as a Windows DLL and
# import library.
#
# This file is expected to be processed by nmake as in the following:
#     nmake /f Makefile.win32
#
# Installation instructions:
# https://oracle.github.io/odpi/doc/installation.html
#
#------------------------------------------------------------------------------

SRC_DIR = src
INCLUDE_DIR = include
BUILD_DIR = build
LIB_DIR = lib

DLL_NAME = $(LIB_DIR)\odpic.dll
LIB_NAME = $(LIB_DIR)\odpic.lib

OBJS = $(BUILD_DIR)\dpiConn.obj $(BUILD_DIR)\dpiContext.obj \
       $(BUILD_DIR)\dpiData.obj $(BUILD_DIR)\dpiEnv.obj \
       $(BUILD_DIR)\dpiError.obj $(BUILD_DIR)\dpiGen.obj \
       $(BUILD_DIR)\dpiGlobal.obj $(BUILD_DIR)\dpiLob.obj \
       $(BUILD_DIR)\dpiObject.obj $(BUILD_DIR)\dpiObjectAttr.obj \
       $(BUILD_DIR)\dpiObjectType.obj $(BUILD_DIR)\dpiPool.obj \
       $(BUILD_DIR)\dpiStmt.obj $(BUILD_DIR)\dpiUtils.obj \
       $(BUILD_DIR)\dpiVar.obj $(BUILD_DIR)\dpiOracleType.obj \
       $(BUILD_DIR)\dpiSubscr.obj $(BUILD_DIR)\dpiDeqOptions.obj \
       $(BUILD_DIR)\dpiEnqOptions.obj $(BUILD_DIR)\dpiMsgProps.obj \
       $(BUILD_DIR)\dpiRowid.obj $(BUILD_DIR)\dpiOci.obj \
       $(BUILD_DIR)\dpiDebug.obj $(BUILD_DIR)\dpiHandlePool.obj \
       $(BUILD_DIR)\dpiHandleList.obj $(BUILD_DIR)\dpiSodaColl.obj \
       $(BUILD_DIR)\dpiSodaCollCursor.obj $(BUILD_DIR)\dpiSodaDb.obj \
       $(BUILD_DIR)\dpiSodaDoc.obj $(BUILD_DIR)\dpiSodaDocCursor.obj \
       $(BUILD_DIR)\dpiQueue.obj $(BUILD_DIR)\dpiJson.obj \
       $(BUILD_DIR)\dpiStringList.obj $(BUILD_DIR)\dpiVector.obj

all: $(BUILD_DIR) $(LIB_DIR) $(DLL_NAME) $(LIB_NAME)

clean:
	@if exist $(BUILD_DIR) rmdir /S /Q $(BUILD_DIR)
	@if exist $(LIB_DIR) rmdir /S /Q $(LIB_DIR)

$(BUILD_DIR) $(LIB_DIR):
	@if not exist $(BUILD_DIR) mkdir $(BUILD_DIR)
	@if not exist $(LIB_DIR) mkdir $(LIB_DIR)

{$(SRC_DIR)}.c{$(BUILD_DIR)}.obj:
	cl /nologo /c /Fo$(BUILD_DIR)\ /I$(INCLUDE_DIR) /DDPI_EXPORTS $<

$(DLL_NAME) $(LIB_NAME): $(OBJS)
	link /nologo /dll /OUT:$(DLL_NAME) $(OBJS)
