# 시작시 '=' 으로 시작
# 마지막 '=' 으로 마침
# 첫번째 두번재는 ' ' ( 스페이스 ) 로 구분 되며 세번재 인자는 '='까지 값을 가져옴
# ex)= 100 00 성공=ss
= 1000 1000 성공=
= 1001 1001 NoJsonBody=
= 1002 1002 InvalidHubPartnerKey=
= 1003 1003 InvalidSenderKey=
= 1004 1004 NoValueJsonElement=
= 2003 2003 FailedToSendMessageByNoFriendshipException=
= 2004 2004 FailedToMatchTemplateException=
= 3000 3000 UnexpectedException=
= 3001 3001 NoUserException=
= 3002 3002 NoVisitUserException=
= 3003 3003 UnsupportedClientException=
= 3004 3004 UserBlockedDeliveryException=
= 3005 3005 AckTimeoutException=
= 3006 3006 FailedToSendMessageException=
= 3007 3007 NotConnectedUserException=
= 3010 3010 JsonParseException=
= 3011 3011 MessageNotFoundException=
= 3012 3012 SerialNumberDuplicatedException=
= 3013 3013 MessageEmptyException=
= 3014 3014 MessageLengthOverLimitException=
= 3015 3015 TemplateNotFoundException=
= 3016 3016 NoMatchedTemplateException=
= 3017 3017 DuplicatedException=
= 3018 3018 NoSendAvaliableStatusException=
= 4000 4000 ResponseHistoryNotFoundException=
= 4001 4001 UnknownMessageStatusError=
= 9998 9998 현재 서비스를 제공하고 있지 않습니다.=
= 9999 9999 시스템에서 알 수 없는 문제가 발생하였습니다. 담당자가 확인 중입니다.= 
