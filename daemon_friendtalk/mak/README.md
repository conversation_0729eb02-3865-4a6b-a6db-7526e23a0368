# Database Configuration for Makefile

이 프로젝트는 Pro*C 컴파일 시 데이터베이스 연결 정보가 필요합니다. 보안을 위해 데이터베이스 정보를 makefile에 하드코딩하지 않고 다음 두 가지 방법 중 하나를 사용할 수 있습니다.

## 방법 1: 환경변수 사용 (권장 - CI/CD 환경)

빌드 전에 다음 환경변수를 설정하세요:

```bash
export SMS_DBSTRING=your_sms_database_string
export SMS_DBID=your_sms_database_id
export SMS_DBPASS=your_sms_database_password
export MMS_DBSTRING=your_mms_database_string
export MMS_DBID=your_mms_database_id
export MMS_DBPASS=your_mms_database_password

make
```

## 방법 2: 설정 파일 사용 (권장 - 로컬 개발)

1. 템플릿 파일을 복사하여 설정 파일을 생성:
```bash
cp db_config.mk.template db_config.mk
```

2. `db_config.mk` 파일을 편집하여 실제 데이터베이스 정보를 입력:
```makefile
SMS_DBSTRING=actual_sms_database_string
SMS_DBID=actual_sms_database_id
SMS_DBPASS=actual_sms_database_password
MMS_DBSTRING=actual_mms_database_string
MMS_DBID=actual_mms_database_id
MMS_DBPASS=actual_mms_database_password
```

3. 빌드 실행:
```bash
make
```

## 주의사항

- `db_config.mk` 파일은 `.gitignore`에 포함되어 있어 버전 관리에서 제외됩니다.
- 실제 데이터베이스 정보를 포함한 파일을 절대 커밋하지 마세요.
- 환경변수나 설정 파일 중 어느 것도 설정되지 않으면 빌드 시 오류가 발생합니다.

## 빌드 타겟

- `make all` 또는 `make`: ftalk_send_v3 빌드
- `make friendtalk_mms`: friendtalk_mms 빌드
- `make ftalk_send_v2`: ftalk_send_v2 빌드
- `make clean`: 빌드 파일 정리

## CMake 빌드

이 프로젝트는 CMake도 지원합니다. CMake 사용 시에는 `../README_CMAKE.md` 파일을 참조하세요.

CMake 빌드 시에도 동일한 환경변수를 사용하거나 `db_config.cmake` 파일을 생성하여 사용할 수 있습니다.
