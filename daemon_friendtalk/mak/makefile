PROC=proc
CC=g++
COPY=cp
RM=rm
LINT=lint
# Legacy CFLAGS (commented for compatibility)
# CFLAGS = -g -lrt -w

# New CFLAGS with OpenSSL compatibility (aligned with daemon_logon_ftk)
CFLAGS = -g -lrt -Wall -std=gnu++11 $(OPENSSL_CFLAGS) -DCLION_BUILD -DDEBUG=5 -DNOSEND
CURLFLAGS = -g -Wall -D_URL_MODE -DCLION_BUILD $(OPENSSL_CFLAGS)
CMMSFLAGS = -g -Wall -D_MMS_MODE -DCLION_BUILD $(OPENSSL_CFLAGS)

# Database connection configuration
# Option 1: Use environment variables (recommended for CI/CD)
# Option 2: Use separate config file (recommended for local development)

# Try to include database config file if it exists
-include db_config.mk
-include oracle_config.mk

# If config file doesn't exist, check environment variables
ifndef SMS_DBSTRING
ifdef DB_CONFIG_FILE
$(error Database config file db_config.mk not found. Copy db_config.mk.template to db_config.mk and set your values)
else
$(error SMS_DBSTRING not set. Either set environment variables or create db_config.mk from template)
endif
endif

ifndef SMS_DBID
$(error SMS_DBID not set. Either set environment variables or create db_config.mk from template)
endif

ifndef SMS_DBPASS
$(error SMS_DBPASS not set. Either set environment variables or create db_config.mk from template)
endif

ifndef MMS_DBSTRING
$(error MMS_DBSTRING not set. Either set environment variables or create db_config.mk from template)
endif

ifndef MMS_DBID
$(error MMS_DBID not set. Either set environment variables or create db_config.mk from template)
endif

ifndef MMS_DBPASS
$(error MMS_DBPASS not set. Either set environment variables or create db_config.mk from template)
endif

# Legacy path settings (commented for compatibility with existing systems)
# ORG_D=${HOME}/daemon_friendtalk

# New path settings (relative paths for current project structure)
ORG_D=..
BIN_D=${ORG_D}/bin
OBJ_D=${ORG_D}/obj
LIB_D=${ORG_D}/lib
INC_D=${ORG_D}/inc
SRC_D=${ORG_D}/src

# Original paths - commented out
# EXT_LIB=${HOME}/command_friendtalk/obj/sms_ctrlsub++.o
# EXT_INC=${HOME}/command_friendtalk/inc
# Updated paths for CLion project (relative paths)
EXT_LIB=../../command_friendtalk/obj/sms_ctrlsub++.o
EXT_INC=../../command_friendtalk/inc

# Oracle 환경 설정 (환경변수 우선, 기본값 제공)
ORACLE_HOME ?= /usr/lib/oracle/21/client64
TNS_ADMIN ?= $(ORACLE_HOME)/network/admin
NLS_LANG ?= KOREAN_KOREA.AL32UTF8
ORACLE_VERSION ?= 21

# Pro*C 설정 파일 경로
PROC_CONFIG ?= $(ORACLE_HOME)/lib/precomp/admin/pcscfg.cfg

# Oracle include 경로 (버전별 대응)
ORACLE_INCLUDES ?= -I/usr/include/oracle/$(ORACLE_VERSION)/client64

# Library path settings (aligned with daemon_logon_ftk)
KSLIBRARY_PATH=$(HOME)/library
KSLIBRARY_INC=$(HOME)/library

ORALIB1 = $(ORACLE_HOME)/lib
ORALIB2 = $(ORACLE_HOME)/plsql/lib
ORALIB3 = $(ORACLE_HOME)/network/lib
#ORA_INC = $(ORACLE_HOME)/precomp/public
ORA_INC ?= /usr/include/oracle/$(ORACLE_VERSION)/client64

# OpenSSL version detection and configuration
OPENSSL_VERSION := $(shell pkg-config --modversion openssl 2>/dev/null || echo "unknown")
OPENSSL_MAJOR := $(shell echo $(OPENSSL_VERSION) | cut -d. -f1)

# OpenSSL path configuration based on version
ifeq ($(OPENSSL_MAJOR),3)
    # OpenSSL 3.x configuration
    OPENSSL_CFLAGS = -DOPENSSL_API_COMPAT=0x10100000L -DOPENSSL_SUPPRESS_DEPRECATED -DROCKY_LINUX_9
    OPENSSL_INCLUDES = -I/usr/include/openssl
    OPENSSL_LINKFLAGS = -L/usr/lib64
    OPENSSL_LIBS = -lcrypto -lssl
else ifeq ($(OPENSSL_MAJOR),1)
    # OpenSSL 1.x configuration
    OPENSSL_CFLAGS = -DOPENSSL_API_COMPAT=0x10100000L -DOPENSSL_SUPPRESS_DEPRECATED
    OPENSSL_INCLUDES = -I/usr/local/openssl/include
    OPENSSL_LINKFLAGS = -L/usr/local/openssl/lib
    OPENSSL_LIBS = -lcrypto -lssl
else
    # Default/fallback configuration
    OPENSSL_CFLAGS = -DOPENSSL_API_COMPAT=0x10100000L -DOPENSSL_SUPPRESS_DEPRECATED
    OPENSSL_INCLUDES = -I/usr/include/openssl
    OPENSSL_LINKFLAGS = -L/usr/lib64
    OPENSSL_LIBS = -lcrypto -lssl
endif

INCLUDE = -I$(INC_D) -I$(LIB_D) -I$(ORA_INC) -I/usr/include/curl $(OPENSSL_INCLUDES)
LINKFLAGS = -L$(ORALIB1) -L$(ORALIB2) -L$(ORALIB3) $(OPENSSL_LINKFLAGS) -L/usr/lib64
ORACLE_LIBS = -L$(ORACLE_HOME)/lib
RPATH_FLAGS = -Wl,-rpath,$(ORACLE_HOME)/lib
ORALIB = -lclntsh
LIBS = -lcurl -lpthread -lnsl -lksbase64 -lkssocket -lksconfig -lksthread $(OPENSSL_LIBS)

#all: friendtalk_mms ftalk_send_v2
#all: friendtalk_mms ftalk_send_v3
all: ftkup_send_v1

ftalk_send_v3 : $(OBJ_D)/ftalk_send_v3.o $(OBJ_D)/Properties.o $(OBJ_D)/DatabaseORA_MMS.o $(OBJ_D)/myException.o $(OBJ_D)/Curl.o $(OBJ_D)/alimTalkApi.o $(OBJ_D)/jsoncpp.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} $(ORACLE_LIBS) $(ORALIB) $(RPATH_FLAGS) -I${INC_D} -o $(BIN_D)/ftalk_send_v3

ftkup_send_v1 : $(OBJ_D)/ftkup_send_v1.o $(OBJ_D)/Properties.o $(OBJ_D)/DatabaseORA_MMS.o $(OBJ_D)/myException.o $(OBJ_D)/Curl.o $(OBJ_D)/alimTalkApi.o $(OBJ_D)/jsoncpp.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) ${LIBS} -L$(KSLIBRARY_PATH) ${LINKFLAGS} $(ORACLE_LIBS) $(ORALIB) $(RPATH_FLAGS) -I${INC_D} -o $(BIN_D)/ftkup_send_v1

$(OBJ_D)/ftalk_send_v3.o: $(SRC_D)/ftalk_send_v3.cpp
	$(RM) -rf $(OBJ_D)/ftalk_send_v3.*
	$(COPY) $(SRC_D)/ftalk_send_v3.cpp $(OBJ_D)/ftalk_send_v3.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/ftalk_send_v3.pc \
		include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS CHAR_MAP=STRING \
		config=$(PROC_CONFIG) userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) $(CFLAGS) -o $(OBJ_D)/ftalk_send_v3.o $(ORACLE_INCLUDES) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/ftalk_send_v3.cpp

$(OBJ_D)/ftkup_send_v1.o: $(SRC_D)/ftkup_send_v1.cpp
	$(RM) -rf $(OBJ_D)/ftkup_send_v1.*
	$(COPY) $(SRC_D)/ftkup_send_v1.cpp $(OBJ_D)/ftkup_send_v1.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/ftkup_send_v1.pc \
		include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS CHAR_MAP=STRING \
		config=$(PROC_CONFIG) userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) $(CFLAGS) -o $(OBJ_D)/ftkup_send_v1.o $(ORACLE_INCLUDES) -I$(KSLIBRARY_INC) $(INCLUDE) -I$(EXT_INC) -c $(OBJ_D)/ftkup_send_v1.cpp
	
$(OBJ_D)/Properties.o: $(LIB_D)/Properties.cpp
	$(RM) -rf $(OBJ_D)/Properties.*
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(KSLIBRARY_INC) -I$(EXT_INC) -c $^

$(OBJ_D)/ksbase64.o: $(LIB_D)/ksbase64.cpp
	$(RM) -rf $(OBJ_D)/ksbase64.*
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(KSLIBRARY_INC) -I$(EXT_INC) -c $^

$(OBJ_D)/DatabaseORA_MMS.o: $(LIB_D)/DatabaseORA_MMS.cpp
	$(RM) -rf $(OBJ_D)/DatabaseORA_MMS.*
	$(COPY) $(LIB_D)/DatabaseORA_MMS.cpp $(OBJ_D)/DatabaseORA_MMS.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/DatabaseORA_MMS.pc \
		include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE SQLCHECK=SEMANTICS CHAR_MAP=STRING \
		config=$(PROC_CONFIG) userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS) -o $(OBJ_D)/DatabaseORA_MMS.o ${LINKFLAGS} $(ORALIB) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} \
	-I$(LIB_D) -I$(EXT_INC) -c $(OBJ_D)/DatabaseORA_MMS.cpp

$(OBJ_D)/myException.o: $(LIB_D)/myException.cpp
	$(RM) -rf $(OBJ_D)/myException.*
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(KSLIBRARY_INC) -I$(EXT_INC) -c $^

$(OBJ_D)/LogManager.o: $(LIB_D)/LogManager.cpp
	$(RM) -rf $(OBJ_D)/LogManager.*
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(KSLIBRARY_INC) -I$(EXT_INC) -c $^

$(OBJ_D)/Curl.o: $(LIB_D)/Curl.cpp
	$(RM) -rf $(OBJ_D)/Curl.*
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I /usr/include/curl -I$(KSLIBRARY_INC) -I$(EXT_INC) -c $^

$(OBJ_D)/alimTalkApi.o: $(LIB_D)/alimTalkApi.cpp
	$(RM) -rf $(OBJ_D)/alimTalkApi.*
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(KSLIBRARY_INC) -I$(EXT_INC) -c $^

$(OBJ_D)/jsoncpp.o: $(LIB_D)/jsoncpp.cpp
	$(RM) -rf $(OBJ_D)/jsoncpp.*
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(KSLIBRARY_INC) -I$(EXT_INC) -c $^

clean:
	rm  -rf $(OBJ_D)/*.o tp* $(OBJ_D)/*.lis $(OBJ_D)/*.pc $(OBJ_D)/*.cpp

install:
	mv $(BIN_D)/friendtalk_mms_tmp $(BIN_D)/friendtalk_mms
	rm tp*
