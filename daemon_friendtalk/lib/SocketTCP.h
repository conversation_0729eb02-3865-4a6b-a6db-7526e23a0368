/*
 * SocketTCP.h
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#ifndef SOCKETTCP_H_
#define SOCKETTCP_H_

#include <sys/socket.h>
#include <sys/types.h>
#include <fcntl.h>
#include <stdlib.h>
#include <arpa/inet.h>


typedef struct _HEADER_SKB {			/* KskyB 전문 Header */
	char szJobCode[2];			//job code(S1,A2,D2,P2,O3,C2)
	char szInvoketype[2];		//Request - 00, response - 01
	char szVersion[8];			//protocol version(최소 버전 02.10.00) 예) 02.10.00
	char szClientType[2];		//Client 종류 00:DB Agent, 01:WEB(개별전송), 02:WEB(대량발송), 03:OAM, 04:Excel Messager, 05:API, 06:직접연동 %직접연동이므로 06으로 설정
	char szClientVersion[8];	//Client version(최소 버전 02.00.00) 예) 02.00.00
	char szTID[10];				//transactio ID. Client 최초 실행시, 최대값 이상일때 reset됨.(0000000000 ~ 9999999999)
	char szBodySize[10];		//total body size (0000000000 ~ 9999999999)
} HEADER_SKB;
namespace KSKYB
{

class CSocketTCP
{
protected:
	void setSocketId(int socketFd)
	{
		m_nSocketId = socketFd;
	}

public:
	CSocketTCP() {};
	CSocketTCP(int);
	virtual ~CSocketTCP();

	void setDebug(int);
	void setReuseAddr(int);
	void setKeepAlive(int);
	void setLingerOnOff(bool);
	void setLingerSeconds(int);
	void setLingerSeconds(int,int);
	void setRecvTimeout(int seconds);
	void setSocketBlocking(int);
	void setSendBufSize(int);
	void setReceiveBufSize(int);
	int getSocketId()
	{
		return m_nSocketId;
	}
	int getPortNumber()
	{
		return m_nPortNumb;
	}
	int sendMessage(char*,int);
	int recieveMessage(char*);
	int checkSelect(int);
	void bindSocket();
	void listenToClient(int);
	CSocketTCP* acceptClient();
	void connectToServer(char*);
	void Wait_A_Moment(int nSec, int nUsc);
	void SocketClose();

private:
	int m_nPortNumb; // Socket port number
	int m_nSocketId; // Socket file descriptor
	int m_nBlocking; // Blocking flag
	int m_nBindFlag; // Binding flag
	struct sockaddr_in m_ClientAddr; // Address of the client that sent data
};

}

#endif /* SOCKETTCP_H_ */
