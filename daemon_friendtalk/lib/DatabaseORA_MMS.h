/*
 * DatabaseORA.h
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#ifndef DATABASEORA_H_
#define DATABASEORA_H_

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string>
#include <vector>
#include <map>
using namespace std;
#include <ml_ctrlsub.h>

namespace KSKYB
{

class CDatabaseORA
{
public:
	CDatabaseORA() {};
	virtual ~CDatabaseORA() {};

	int setEnableThreads();
	int initThread(sql_context& ctx);
	int freeThread(sql_context& ctx);
	int connectToOracle(sql_context ctx, char*, char*);
	int closeFromOracle(sql_context ctx);
	int setSndAckData(int telcoid, sql_context ctx, vector<string>& vtSndAck);
	int setReportData(int telcoid, sql_context ctx, map<string, string>& mapReport);
	//int getMsgData(sql_context ctx, char *q_name, vector<string>& vtSend);
	long long getMsgData(sql_context ctx, char *q_name, map<string,string> &mapSend);
	long long getMsgData_V2(sql_context ctx, char *q_name, map<string,string> &mapSend);
	long long getMsgData_V3(sql_context ctx, char *q_name, map<string,string> &mapSend);
    long long getMsgData_V4(sql_context ctx, char *q_name, map<string,string> &mapSend);
	int getCtnData(sql_context ctx, int cid, vector<string>& vtCtnData);
//	int _putMsgRetryDB(int mmsid, char *q_name, int telcoid, sql_context ctx);
	int _putMsgRetryDB(long long mmsid, char *q_name, int telcoid, sql_context ctx);

private:
	bool m_bThread;
	inline string trimR(const string& str)
	{
		string::size_type n = str.find_last_not_of(" \t\v\n");
		return n == string::npos ? str : str.substr(0, n + 1);
	}
};

}

#endif /* DATABASEORA_H_ */
