//
// DatabaseORA_MMS_dev.cpp - Development version without PL/SQL blocks
// This file is used for building without database connection
//

#include "DatabaseORA_MMS.h"
#include <sqlca.h>
#include <iostream>
using namespace std;

char tmpLog3[1024];
void log3(char *buf, int st, int err);
void get_timestring(char *fmt, long n, char *s);
char* trim(char* szOrg, int leng);

// Oracle includes for basic types
EXEC SQL INCLUDE sqlca;
EXEC SQL INCLUDE sqlda;
EXEC SQL INCLUDE oraca;

namespace KSKYB
{

// Development mode - stub implementations for CDatabaseORA class
long long CDatabaseORA::getMsgData(sql_context ctx, char *q_name, map<string,string> &mapSend)
{
    // Development stub - return dummy data
    printf("Development mode: getMsgData called for queue=%s\n", q_name);

    // Return dummy data
    mapSend["mms_id"] = "12345";
    mapSend["sender_key"] = "test_sender";
    mapSend["dst_addr"] = "01012345678";
    mapSend["user_key"] = "test_user";
    mapSend["msg_grp_cd"] = "TEST";
    mapSend["msg_body"] = "Test message";
    mapSend["button_name"] = "Test Button";
    mapSend["button_url"] = "http://test.com";
    mapSend["img_path"] = "/test/image.jpg";
    mapSend["img_link"] = "http://test.com/image";

    return 12345; // dummy mms_id
}

long long CDatabaseORA::getMsgData_V2(sql_context ctx, char *q_name, map<string,string> &mapSend)
{
    // Development stub - return dummy data
    printf("Development mode: getMsgData_V2 called for queue=%s\n", q_name);

    // Return dummy data
    mapSend["mms_id"] = "12345";
    mapSend["sender_key"] = "test_sender";
    mapSend["dst_addr"] = "01012345678";
    mapSend["user_key"] = "test_user";
    mapSend["msg_body"] = "Test message V2";
    mapSend["button_name"] = "Test Button";
    mapSend["button_url"] = "http://test.com";
    mapSend["img_path"] = "/test/image.jpg";
    mapSend["img_link"] = "http://test.com/image";
    mapSend["res_method"] = "GET";
    mapSend["timeout"] = "30";

    return 12345; // dummy mms_id
}

long long CDatabaseORA::getMsgData_V3(sql_context ctx, char *q_name, map<string,string> &mapSend)
{
    // Development stub - return dummy data
    printf("Development mode: getMsgData_V3 called for queue=%s\n", q_name);

    // Return dummy data
    mapSend["mms_id"] = "12345";
    mapSend["sender_key"] = "test_sender";
    mapSend["dst_addr"] = "01012345678";
    mapSend["user_key"] = "test_user";
    mapSend["msg_body"] = "Test message V3";
    mapSend["button_name"] = "Test Button";
    mapSend["button_url"] = "http://test.com";
    mapSend["button"] = "{}";
    mapSend["img_path"] = "/test/image.jpg";
    mapSend["img_link"] = "http://test.com/image";
    mapSend["res_method"] = "GET";
    mapSend["timeout"] = "30";
    mapSend["ad_flag"] = "N";
    mapSend["wide"] = "N";
    mapSend["kko_img_url"] = "http://test.com/kko";

    return 12345; // dummy mms_id
}

int CDatabaseORA::setSndAckData(int telcoid, sql_context ctx, vector<string>& vtSndAck)
{
    // Development stub - return success
    printf("Development mode: setSndAckData called for telcoid=%d\n", telcoid);
    return 0;
}

int CDatabaseORA::setReportData(int telcoid, sql_context ctx, map<string, string>& mapReport)
{
    // Development stub - return success
    printf("Development mode: setReportData called for telcoid=%d\n", telcoid);
    return 0;
}

int CDatabaseORA::_putMsgRetryDB(long long mmsid, char *q_name, int telcoid, sql_context ctx)
{
    // Development stub - return success
    printf("Development mode: _putMsgRetryDB called for mmsid=%lld, queue=%s, telcoid=%d\n", mmsid, q_name, telcoid);
    return 0;
}

int CDatabaseORA::setEnableThreads()
{
    printf("Development mode: setEnableThreads - returning success\n");
    m_bThread = true;
    return 1;
}

int CDatabaseORA::initThread(sql_context& ctx)
{
    printf("Development mode: initThread - returning success\n");
    return 1;
}

int CDatabaseORA::freeThread(sql_context& ctx)
{
    printf("Development mode: freeThread - returning success\n");
    return 1;
}

int CDatabaseORA::connectToOracle(sql_context ctx, char* szUID, char* szDSN)
{
    printf("Development mode: connectToOracle - returning success\n");
    return 1;
}

int CDatabaseORA::closeFromOracle(sql_context ctx)
{
    printf("Development mode: closeFromOracle - returning success\n");
    return 1;
}

int CDatabaseORA::getCtnData(sql_context ctx, int cid, vector<string>& vtCtnData)
{
    printf("Development mode: getCtnData called for cid=%d\n", cid);
    return 0;
}

}
