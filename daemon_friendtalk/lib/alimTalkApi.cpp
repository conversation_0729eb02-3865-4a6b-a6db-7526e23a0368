#include "alimTalkApi.h"
#include <iconv.h>
#include <string.h>
#include <sys/time.h>
#include <time.h>

void RemoveFirst(char *buf);

void CAlimtalkApi::makeFtalkRequestMsg(map<string,string> &_mapSend, string &parameter, long long msgid)
{
	string sender_key;
	string dstaddr;	
	string user_key;	
	string msg_grp_cd;
	string msg_body;
	string button_name;
	string button_url;
	string img_url;
	string img_link;

	sender_key = _mapSend["sender_key"];
	dstaddr = _mapSend["dst_addr"];	
	user_key = _mapSend["user_key"];
	msg_grp_cd = _mapSend["msg_grp_cd"];
	msg_body = _mapSend["msg_body"];
	button_name = _mapSend["button_name"];
	button_url = _mapSend["button_url"];
	img_url = _mapSend["img_url"];
	img_link = _mapSend["img_link"];
	
	Json::Value root;
	char cmsgid[32] = {0x00,};
	sprintf(cmsgid, "%lld", msgid);

	char cmsg_body[8192] = {0x00,};
	char cmsg_body_utf8[8192] = {0x00,};
	sprintf(cmsg_body, "%s", msg_body.c_str());
	euckrToUtf8(cmsg_body, cmsg_body_utf8, sizeof(cmsg_body_utf8));

	//cerr<<"cmsg_body:"<<cmsg_body<<endl;
	//cerr<<"cmsg_body_utf8:"<<cmsg_body_utf8<<endl;
	root["message_type"] = "FT";
	root["serial_number"] = cmsgid;
	
	root["sender_key"] = sender_key;
	root["phone_number"] = dstaddr;
	root["user_key"] = user_key;
	//root["message"] = msg_body;
	root["message"] = cmsg_body_utf8;
	root["response_method"] = "realtime";
	root["timeout"] = 10;

	Json::Value attachment;

	//button set
	if(button_name.size() > 0)
	{
		char cbutton_name[100] = {0x00,};
		char cbutton_name_utf8[100] = {0x00,};
		sprintf(cbutton_name, "%s", button_name.c_str());
		euckrToUtf8(cbutton_name, cbutton_name_utf8, sizeof(cbutton_name_utf8));
		Json::Value button;
		button["name"] = cbutton_name_utf8;
		button["url"] = button_url;
		attachment["button"] = button;
	}

	//image set
	if(img_url.size() > 0)
	{
		Json::Value img;
		img["img_url"] = img_url;
		if(img_link.size() > 0)
		{
			img["img_link"] = img_link;
		}

		attachment["image"] = img;
	}

	root["attachment"] = attachment;

	Json::FastWriter writer;
    parameter = writer.write(root);
}

void CAlimtalkApi::makeFtalkRequestMsg_V2(map<string,string> &_mapSend, string &parameter, long long msgid)
{
    string sender_key;
    string dstaddr;
    string user_key;
    string msg_grp_cd;
    string msg_body;
    string button_name;
    string button_url;
    string img_url;
    string img_link;
    string res_method;
    string timeout;

    sender_key = _mapSend["sender_key"];
    dstaddr = _mapSend["dst_addr"];
    user_key = _mapSend["user_key"];
    msg_body = _mapSend["msg_body"];
    button_name = _mapSend["button_name"];
    button_url = _mapSend["button_url"];
    img_url = _mapSend["img_url"];
    img_link = _mapSend["img_link"];

    if(res_method.size() <= 0)
        res_method = "realtime";

    timeout = _mapSend["timeout"];
    if(timeout.size() <= 0)
    {
        timeout = "10";
    }

    Json::Value root;
    char cmsgid[32] = {0x00,};
    sprintf(cmsgid, "%lld", msgid);

    char cmsg_body[4096] = {0x00,};
    char cmsg_body_utf8[4096] = {0x00,};
    sprintf(cmsg_body, "%s", msg_body.c_str());
    euckrToUtf8(cmsg_body, cmsg_body_utf8, sizeof(cmsg_body_utf8));

//  cerr<<"cmsg_body_utf8:"<<cmsg_body_utf8<<endl;
    root["message_type"] = "ft";
    root["serial_number"] = cmsgid;

    root["sender_key"] = sender_key;
    root["phone_number"] = dstaddr;
    root["user_key"] = user_key;
    //root["message"] = msg_body;
    root["message"] = cmsg_body_utf8;
    root["response_method"] = "realtime";
    root["timeout"] = atoi(timeout.c_str());;

    Json::Value attachment;

    //button set
    if(button_name.size() > 0)
    {
        char cbutton_name[100] = {0x00,};
        char cbutton_name_utf8[100] = {0x00,};
        sprintf(cbutton_name, "%s", button_name.c_str());
        euckrToUtf8(cbutton_name, cbutton_name_utf8, sizeof(cbutton_name_utf8));
        Json::Value button;
        button["name"] = cbutton_name_utf8;
        button["url"] = button_url;
        attachment["button"] = button;
    }
	
	//image set
    if(img_url.size() > 0)
    {
        Json::Value img;
        img["img_url"] = img_url;
        
        /*if(img_link.size() > 0)
        {
            img["img_link"] = img_link;
        }*/

        attachment["image"] = img;
    }

    root["attachment"] = attachment;

    Json::FastWriter writer;
    parameter = writer.write(root);
}

void CAlimtalkApi::makeFtalkRequestMsg_V3(map<string,string> &_mapSend, string &parameter, long long msgid)
{
    string sender_key;
    string dstaddr;
    string user_key;
    string msg_grp_cd;
    string msg_body;
    string button_name;
    string button_url;
    string button_data;
    string img_url;
    string img_link;
    string res_method;
    string timeout;
    string ad_flag;
    string wide;
    
    //added due to new button functionality
	string button_data_tmp;

    sender_key = _mapSend["sender_key"];
    dstaddr = _mapSend["dst_addr"];
    user_key = _mapSend["user_key"];
    msg_body = _mapSend["msg_body"];
    button_name = _mapSend["button_name"];
    button_url = _mapSend["button_url"];
    button_data = _mapSend["button"];
    img_url = _mapSend["img_url"];
    img_link = _mapSend["img_link"];
    ad_flag = _mapSend["ad_flag"];
    wide = _mapSend["wide"];

    if(res_method.size() <= 0)
        res_method = "realtime";

    timeout = _mapSend["timeout"];
    if(timeout.size() <= 0)
    {
        timeout = "10";
    }

    Json::Value root;
    //char cmsgid[32] = {0x00,};
    //char cmsgid[39] = {0x00,};
    char cmsgid[39];
	memset(cmsgid,0x00,sizeof(cmsgid));
    //sprintf(cmsgid, "%lld", msgid);
    
    char	pch[30];
	char	pchlast[30];
    time_t	the_time;
	struct	timeval val;
	struct	tm	*tm_ptr;
	
	struct timespec tmv;
	struct tm	tp;

	clock_gettime(CLOCK_REALTIME, &tmv);
	localtime_r(&tmv.tv_sec, &tp);

	memset(pch				,0x00		,sizeof(pch));	
	sprintf(pch, "%04d%02d%02d%02d%02d%02d%09d"
				,tp.tm_year+1900
				,tp.tm_mon+1
				,tp.tm_mday
				,tp.tm_hour
				,tp.tm_min
				,tp.tm_sec
				,(int)tmv.tv_nsec
				);

		
	//memset(pchlast		,0x00		,sizeof(pchlast));
	sprintf(cmsgid,"%.8s-%lld", pch,msgid);

    //char cmsg_body[4096+1] = {0x00,};
    //char cmsg_body_utf8[4096+1] = {0x00,};
    
    char cmsg_body[4096];
	char cmsg_body_utf8[4096];
	
	memset(cmsg_body,0x00,sizeof(cmsg_body));
    memset(cmsg_body_utf8,0x00,sizeof(cmsg_body_utf8));
    //sprintf(cmsg_body, "%s", msg_body.c_str());
    strncpy(cmsg_body, msg_body.c_str(),sizeof(cmsg_body)-1);
    euckrToUtf8(cmsg_body, cmsg_body_utf8, sizeof(cmsg_body_utf8));

//  cerr<<"cmsg_body_utf8:"<<cmsg_body_utf8<<endl;
    root["message_type"] = "FT";
    root["serial_number"] = cmsgid;
    
    root["sender_key"] = sender_key;
    root["phone_number"] = dstaddr;
    root["user_key"] = user_key;
    //root["message"] = msg_body;
    root["message"] = cmsg_body_utf8;
    root["response_method"] = "realtime";
    root["timeout"] = atoi(timeout.c_str());
    root["ad_flag"] = ad_flag;
    root["wide"] = wide;

    Json::Value attachment;

    //button set
    if(button_name.size() > 0 && button_data.size() == 0)
    {
    	//char cbutton_type[2] = {0x00,};
    	char cbutton_type[3];
		memset(cbutton_type,0x00,sizeof(cbutton_type));
		//char cbutton_name[100] = {0x00,};
		char cbutton_name[100];
		memset(cbutton_name,0x00,sizeof(cbutton_name));
        //char cbutton_name_utf8[100] = {0x00,};
        char cbutton_name_utf8[200];
		memset(cbutton_name_utf8,0x00,sizeof(cbutton_name_utf8));
        //char cbutton_url_pc[2]={0x00,};
		char cbutton_url_pc[2];
		memset(cbutton_url_pc,0x00,sizeof(cbutton_url_pc));
		
		//strcpy(cbutton_type, "WL");
		strncpy(cbutton_type, "WL", sizeof(cbutton_type)-1);
		
        //sprintf(cbutton_name, "%s", button_name.c_str());
        snprintf(cbutton_name,sizeof(cbutton_name),"%s", button_name.c_str());
        euckrToUtf8(cbutton_name, cbutton_name_utf8, sizeof(cbutton_name_utf8));
        
        Json::Value button;
        button["name"] = cbutton_name_utf8;
        button["type"] = cbutton_type;
		if(button_url.length()>0)
		{
			char cbutton_url[300];
			char cbutton_url_utf8[600];
			memset(cbutton_url,0x00,sizeof(cbutton_url));
    		memset(cbutton_url_utf8,0x00,sizeof(cbutton_url_utf8));
			strncpy(cbutton_url,button_url.c_str(),sizeof(cbutton_url)-1);
			euckrToUtf8(cbutton_url, cbutton_url_utf8, sizeof(cbutton_url_utf8));
			button["url_pc"] = cbutton_url_pc;
			//button["url_mobile"] = button_url;
			button["url_mobile"] = cbutton_url_utf8;
		}
        attachment["button"] = button;
        
        if(img_url.size() > 0)
	    {
	        Json::Value img;
	        img["img_url"] = img_url;
	        
	        /*if(img_link.size() > 0)
	        {
	            img["img_link"] = img_link;
	        }*/
	        
	        if(img_link.length() > 0)
	        {
	            img["img_link"] = img_link;
	        }
	
	        attachment["image"] = img;
	    }
	    root["attachment"] = attachment;
	    
	    Json::FastWriter writer;
    	parameter = writer.write(root);
    }
    else if(button_data.size() > 0 && button_name.size() == 0)
	{
		Json::Value attachment;
		Json::Reader reader;
		
		//char cbutton_data[4000+1] = {0x00,};
		//char cbutton_data_utf8[4000+1] = {0x00,};
		//char cbutton_data[3100+1] = {0x00,};
		char cbutton_data[3100+1];
		memset(cbutton_data,0x00,sizeof(cbutton_data));
		//char cbutton_data_utf8[3100+1] = {0x00,};
		char cbutton_data_utf8[6200+1];
		memset(cbutton_data_utf8,0x00,sizeof(cbutton_data_utf8));
		string button_data_format;
		
		if(img_url.size() > 0)
	    {
			button_data_format = "{\"attachment\":{\"button\":[";
			button_data_format += button_data;
			button_data_format += "],";
		}else{
			button_data_format = "{\"attachment\":{\"button\":[";
			button_data_format += button_data;
			button_data_format += "]},";	
		}
		
		//sprintf(cbutton_data, "%s", button_data_format.c_str());
		strncpy(cbutton_data, button_data_format.c_str(),sizeof(cbutton_data)-1);
		euckrToUtf8(cbutton_data, cbutton_data_utf8, sizeof(cbutton_data_utf8));
		
		button_data_tmp = cbutton_data_utf8;
		//button_data_tmp = button_data_format;
		if(img_url.size() > 0)
	    {
    		string szImg;
    		
    		szImg ="\"image\":{\"img_url\":\""+img_url+"\"}},";
    		/*if(img_link.size() > 0)
	        {
	            szImg ="\"image\":{\"img_url\":\""+img_url+"\",\"img_link\":\""+img_link+"\"+}},";
	        }*/
    		
    		if(img_link.length() > 0)
	        {
	            szImg ="\"image\":{\"img_url\":\""+img_url+"\",\"img_link\":\""+img_link+"\"}},";
	        }
    		
    		button_data_tmp += szImg.c_str();
    	}
    	
    	Json::FastWriter writer;
		string parameter_tmp;
		
    	parameter_tmp = writer.write(root);
    	RemoveFirst((char*)parameter_tmp.c_str());	
    	button_data_tmp += parameter_tmp.c_str();
    	parameter = button_data_tmp.c_str();
		
	}
	else
	{
		if(img_url.size() > 0)
	    {
	        Json::Value img;
	        img["img_url"] = img_url;
	        
	        /*if(img_link.size() > 0)
	        {
	            img["img_link"] = img_link;
	        }*/
	        
	        if(img_link.length() > 0)
	        {
	            img["img_link"] = img_link;
	        }
	
	        attachment["image"] = img;
	    }
	    root["attachment"] = attachment;
	    
		Json::FastWriter writer;
    	parameter = writer.write(root);		
	}
	
	
	cout<<"parameter : "<<parameter.c_str()<<endl; 
}

void CAlimtalkApi::makeFtalkRequestMsg_V4(map<string,string> &_mapSend, string &parameter, long long msgid)
{
    string sender_key;
    string dstaddr;
    //string msg_grp_cd;
    string res_method;
    string timeout;
    string chat_bubble_type;
    string targeting;
    string tmpl_cd;
    string app_user_id;
    string push_alarm;
    string message_variable;
    string button_variable;
    string coupon_variable;
    string image_variable;
    string video_variable;
    string commerce_variable;
    string carousel_variable;
    string reserve;
    //added due to new button functionality
    //string button_data_tmp;

    sender_key = _mapSend["sender_key"];
    dstaddr = _mapSend["dst_addr"];
    chat_bubble_type = _mapSend["chat_bubble_type"];
    targeting = _mapSend["targeting"];
    tmpl_cd = _mapSend["tmpl_cd"];
    app_user_id = _mapSend["app_user_id"];
    push_alarm = _mapSend["push_alarm"];
    message_variable = _mapSend["message_variable"];
    button_variable = _mapSend["button_variable"];
    coupon_variable = _mapSend["coupon_variable"];
    image_variable = _mapSend["image_variable"];
    video_variable = _mapSend["video_variable"];
    commerce_variable = _mapSend["commerce_variable"];
    carousel_variable = _mapSend["carousel_variable"];
    reserve = _mapSend["reserve"];    

    //if(res_method.size() <= 0)
     //   res_method = "realtime";

    //timeout = _mapSend["timeout"];
    //if(timeout.size() <= 0)
    //{
    //    timeout = "10";
    //}

    Json::Value root;

    root["sender_key"] = sender_key;
    root["chat_bubble_type"] = chat_bubble_type;
    root["targeting"] = targeting;
    root["template_code"] = tmpl_cd;

	if (dstaddr.size() > 0) {
		root["phone_number"] = dstaddr;
	}

	if (app_user_id.size() > 0) {
		root["app_user_id"] = app_user_id;
	}    
    
    if (push_alarm != "N") {
        push_alarm = "Y";
    }
    root["push_alarm"] = push_alarm;    
    
    string encoding_type = _mapSend["encoding_type"];

    // define variable information as structure
    struct VariableInfo {
        const string& source;
        char* buffer;
        char* utf8_buffer;
        size_t buffer_size;
        size_t utf8_buffer_size;
    };

    // buffer declaration
    char cmessage_variable[2000] = {0};
    char cmessage_variable_utf8[2000] = {0};
    char cbutton_variable[2000] = {0};
    char cbutton_variable_utf8[2000] = {0};
    char ccoupon_variable[1000] = {0};
    char ccoupon_variable_utf8[1000] = {0};
    char cimage_variable[1000] = {0};
    char cimage_variable_utf8[1000] = {0};
    char cvideo_variable[1000] = {0};
    char cvideo_variable_utf8[1000] = {0};
    char ccommerce_variable[1000] = {0};
    char ccommerce_variable_utf8[1000] = {0};
    char ccarousel_variable[2000] = {0};
    char ccarousel_variable_utf8[2000] = {0};
    char creserve[4000] = {0};
    char creserve_utf8[4000] = {0};

    // variable information array
    VariableInfo variables[] = {
        {message_variable, cmessage_variable, cmessage_variable_utf8, sizeof(cmessage_variable), sizeof(cmessage_variable_utf8)},
        {button_variable, cbutton_variable, cbutton_variable_utf8, sizeof(cbutton_variable), sizeof(cbutton_variable_utf8)},
        {coupon_variable, ccoupon_variable, ccoupon_variable_utf8, sizeof(ccoupon_variable), sizeof(ccoupon_variable_utf8)},
        {image_variable, cimage_variable, cimage_variable_utf8, sizeof(cimage_variable), sizeof(cimage_variable_utf8)},
        {video_variable, cvideo_variable, cvideo_variable_utf8, sizeof(cvideo_variable), sizeof(cvideo_variable_utf8)},
        {commerce_variable, ccommerce_variable, ccommerce_variable_utf8, sizeof(ccommerce_variable), sizeof(ccommerce_variable_utf8)},
        {carousel_variable, ccarousel_variable, ccarousel_variable_utf8, sizeof(ccarousel_variable), sizeof(ccarousel_variable_utf8)}
        // reserve is commented out so excluded
    };

    // encoding processing
    const int variable_count = sizeof(variables) / sizeof(variables[0]);
    for (int i = 0; i < variable_count; i++) {
        VariableInfo& var = variables[i];
        if (encoding_type == "euc_kr") {
            // convert from EUC-KR to UTF-8
            strncpy(var.buffer, var.source.c_str(), var.buffer_size - 1);
            euckrToUtf8(var.buffer, var.utf8_buffer, var.utf8_buffer_size);
        } else {
            // direct copy if already UTF-8
            strncpy(var.utf8_buffer, var.source.c_str(), var.utf8_buffer_size - 1);
        }
    }

    // add message_variable only if size > 0
    if (strlen(cmessage_variable_utf8) > 0) {
        root["message_variable"] = cmessage_variable_utf8;
    }

    // add button_variable only if size > 0
    if (strlen(cbutton_variable_utf8) > 0) {
        root["button_variable"] = cbutton_variable_utf8;
    }

    // add coupon_variable only if size > 0
    if (strlen(ccoupon_variable_utf8) > 0) {
        root["coupon_variable"] = ccoupon_variable_utf8;
    }

    // parse image_variable as JSON array (brackets need to be added)
    if (strlen(cimage_variable_utf8) > 0) {
        Json::Reader reader;
        Json::Value imageArray;

        // add brackets to make complete JSON array format
        string jsonArrayString = "[" + string(cimage_variable_utf8) + "]";

        if (reader.parse(jsonArrayString, imageArray) && imageArray.isArray()) {
            root["image_variable"] = imageArray;
        } else {
            // set to empty array on parsing failure
            root["image_variable"] = Json::Value(Json::arrayValue);
        }
    }

    // add video_variable only if size > 0
    if (strlen(cvideo_variable_utf8) > 0) {
        root["video_variable"] = cvideo_variable_utf8;
    }

    // add commerce_variable only if size > 0
    if (strlen(ccommerce_variable_utf8) > 0) {
        root["commerce_variable"] = ccommerce_variable_utf8;
    }

    // parse carousel_variable as JSON array (brackets need to be added)
    if (strlen(ccarousel_variable_utf8) > 0) {
        Json::Reader reader;
        Json::Value carouselArray;

        // add brackets to make complete JSON array format
        string jsonArrayString = "[" + string(ccarousel_variable_utf8) + "]";

        if (reader.parse(jsonArrayString, carouselArray) && carouselArray.isArray()) {
            root["carousel_variable"] = carouselArray;
        } else {
            // set to empty array on parsing failure
            root["carousel_variable"] = Json::Value(Json::arrayValue);
        }
    }
    

//  cerr<<"cmsg_body_utf8:"<<cmsg_body_utf8<<endl;
    //root["message_type"] = "FT";
    //root["serial_number"] = cmsgid;

    //root["response_method"] = "realtime";
    //root["timeout"] = atoi(timeout.c_str());

    Json::FastWriter writer;
    parameter = writer.write(root);

#if (DEBUG >= 5)
    cout<<"parameter : "<<parameter.c_str()<<endl;
#endif
}

int CAlimtalkApi::parsingResponse(string response, ST_TALK_RES &res)
{
	Json::Value root;
	Json::Reader reader;

	bool parsingSuccessful = reader.parse(response, root);
	if(!parsingSuccessful)
	{
		cout<<"Failed to parse response msg"<<endl;
		return -1;
	}

	// Check if this is the new response format (has "status" field)
	if(root.isMember("status"))
	{
		// New response format
		res.status = root.get("status", "").asString();

		if(res.status == "200")
		{
			// Success response: {"status": 200, "result": "N"}
			res.result = root.get("result", "").asString();
			res.code = "0000"; // Set success code for backward compatibility
		}
		else
		{
			// Error response: {"status": 500, "detail": "...", "error": {...}}
			res.detail = root.get("detail", "").asString();

			if(root.isMember("error"))
			{
				Json::Value error = root["error"];
				res.error_code = error.get("code", "").asString();
				res.error_detail = error.get("detail", "").asString();
				res.error_location = error.get("location", "").asString();

				// Set legacy fields for backward compatibility
				res.code = res.error_code;
				res.message = res.error_detail;
			}
			else
			{
				// Fallback if no error object
				res.code = res.status;
				res.message = res.detail;
			}
		}
	}
	else
	{
		// Old response format (backward compatibility)
		//res.received_at = root.get("received_at", "").asString();
		res.code = root.get("code", "").asString();

		if(res.code != "0000")
		{
			res.message = root.get("message", "").asString();
		}
	}

	return 0;
}

int CAlimtalkApi::parsingImgResponse(string response, ST_IMG_RES &res)
{
	Json::Value root;
	Json::Reader reader;

	bool parsingSuccessful = reader.parse(response, root);	
	if(!parsingSuccessful)
	{
		cout<<"Failed to parse response msg"<<endl;
		return -1;
	}

	res.code = root.get("code", "").asString();

	if(res.code != "200")
	{
		res.message = root.get("message", "").asString();
	}
	else
	{
		res.img_url = root.get("image", "").asString();
	}

	return 0;
}

int CAlimtalkApi::euckrToUtf8(char *source, char *dest, int dest_size)
{
	iconv_t it;
	char *pout;
	size_t in_size, out_size;

	it = iconv_open("UTF-8", "EUC-KR");
	in_size = strlen(source);
	out_size = dest_size;
	pout = dest;

	if(iconv(it, &source, &in_size, &pout, &out_size) < 0)
		return -1;


	iconv_close(it);
	return (pout - dest);	
}

void CAlimtalkApi::makeDateString(const string orgDate, string &dateString)
{
	char yyyy[4+1]; char mm[2+1]; char dd[2+1];
	char hh[2+1]; char mi[2+1]; char ss[2+1];

	int ret = 0;
	ret = sscanf(orgDate.c_str(), "%4s-%2s-%2s %2s:%2s:%2s", yyyy, mm, dd, hh, mi, ss);
	if(ret < 0)
	{
		throw -1;
	}

	dateString.append(yyyy);
	dateString.append(mm);
	dateString.append(dd);
	dateString.append(hh);
	dateString.append(mi);
	dateString.append(ss);

}

void RemoveFirst(char *buf)
{
    int i = 0;
    for (i = 1; buf[i]; i++)//repeat if buf[i] is true (not null character)
    {
        buf[i - 1] = buf[i]; //move buf[i] character to buf[i-1]
    }
    //current i is the position of null character, i-1 is the position of last character
    buf[i - 1] = '\0';
}