/*
 * AllimtalkApi.h
 *
 * 2015.10.28
 * by <PERSON><PERSON>
 */

#ifndef _ALIMTALKAPI_H_
#define _ALIMTALKAPI_H

#include <iostream>
using namespace std;
#include <string>
#include <vector>
#include <cstdio>
#include <map>
#include "myException.h"
#include "json.h"


typedef struct st_talk_res
{
	string received_at;
	string code;
	string message;
	// New fields for updated response format
	string status;
	string result;
	string detail;
	string error_code;
	string error_detail;
	string error_location;
}ST_TALK_RES;

typedef struct st_img_res
{
	string code;
	string message;
	string img_url;
}ST_IMG_RES;

class CAlimtalkApi
{
public:
	CAlimtalkApi() {};
	~CAlimtalkApi() {};

	void makeSmsRequestMsg(const vector<string> &vtBuff, string &parameter, long long msgid);
	void makeFtalkRequestMsg(map<string,string> &_mapSend, string &parameter, long long msgid);
	void makeFtalkRequestMsg_V2(map<string,string> &_mapSend, string &parameter, long long msgid);
	//void makeFtalkRequestMsg_V3(map<string,string> &_mapSend, string &parameter, long long msgid, string &button_gb);
	void makeFtalkRequestMsg_V3(map<string,string> &_mapSend, string &parameter, long long msgid);
    void makeFtalkRequestMsg_V4(map<string,string> &_mapSend, string &parameter, long long msgid);
	int parsingResponse(string response, ST_TALK_RES &res);
	int parsingImgResponse(string response, ST_IMG_RES &res);
	int euckrToUtf8(char *source, char *dest, int dest_size);
	void makeDateString(const string orgDate, string &dateString);
};

#endif
