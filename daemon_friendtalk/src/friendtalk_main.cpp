//============================================================================
//============================================================================
// Name        : friendtalk_main.cpp
// Author      : KskyB Inc.
// Version     :
// Copyright   : Copyright@KSKYB
// Description : Hello World in C++, Ansi-style
//============================================================================

#include <iostream>
#include <map>
using namespace std;

#include <signal.h>
#include <errno.h>
#include <pthread.h>

#include "Properties.h"
#include "SocketTCP.h"
#include "myException.h"
#include "Curl.h"
#include "json.h"
#include "alimTalkApi.h"

#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>

#include <code_info.h>
#include <message_info.h>
#include <ml_ctrlsub.h>

#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <string.h>

#include "DatabaseORA_MMS.h"
#include "PacketCtrlSKB_MMS.h"


typedef struct _THREAD_PARAM
{
	int sockfd;
	pthread_t tid;
	sql_context ctx;
	time_t sThisT, sLastT;
	time_t rThisT, rLastT;
	int nServer;
	char buff[MAX_TCP_BUF];
	char logMsg[1024];
} ThreadParam;

CODETABLE* pstCodeTable = NULL;
int nCntCode = 0;
char *base64 = (char *)NULL;
vector<string> vtBuff;

KSKYB::CProperties g_prop;
KSKYB::CDatabaseORA g_oracle;
int activeProcess = true;
char PROCESS_NO[7], PROCESS_NAME[36];
struct _message_info message_info;
struct _shm_info *shm_info;
void* procSendRept(void* param);
int CheckThreadStatus(ThreadParam** param, int nCnt);
int getAllocResultTable(char* filename, CODETABLE** ppstCodeTable);
void Init_Server();
void CloseProcess(int sig);
void mnt(char *buf, int st, int err);
void log(char *buf, int st, int err);
int getImgUrl(char *_target_url, char *_file_path, ThreadParam *tp, ST_IMG_RES &_res);
int makeImgErrReport(map<string, string> &_mapReport, ST_IMG_RES &_img_res, long long _msgid);

int main(int argc, char* argv[])
{
	int idx;
	char logMsg[512];
	g_prop.load(argv[1]);

	int nThreadCnt = g_prop.getPropertyInt("gw.tcnt");
	int nServer = g_prop.getPropertyInt("gw.server");

	Init_Server();
	printf("FRIENDTALK THREAD CNT:[%d]\n", nThreadCnt);

	if (ml_sub_init(PROCESS_NO, PROCESS_NAME, (char*)0, (char*)0) < 0) 
	{
		printf("ml_sub_init ERROR.\n");
		return 0;
	}
	
	if (g_oracle.setEnableThreads()<0)
	{
		sprintf(logMsg, "[%s():%d][setEnableThreads ERROR. process return;]", __func__, __LINE__);
		mnt(logMsg, 0, 0);

		return 0;
	}

	sprintf(logMsg, "[%s():%d][g_oracle.setEnableThreads() Success.]", __func__, __LINE__);
	mnt(logMsg, 0, 0);

	ThreadParam pParam[nThreadCnt];
	for (idx = 0; idx < nThreadCnt; idx++)
	{
		memset(&pParam[idx], 0x00, sizeof(ThreadParam));
		vtBuff.reserve(100);
		pParam[idx].sockfd = idx;
		if (g_oracle.initThread(pParam[idx].ctx)<0 || pParam[idx].ctx == NULL)
		{
			sprintf(logMsg, "[%s():%d][g_oracle.initThread Fail.]", __func__, __LINE__);
			mnt(logMsg, 0, 0);
			continue;
		}
		
		nCntCode = getAllocResultTable(g_prop.getProperty("code.reportCodeFile"), &pstCodeTable);

		if (nCntCode <= 0)
		{
			sprintf(logMsg, "[%s():%d][getAllocResultTable Fail.]", __func__, __LINE__);
			mnt(logMsg, 0, 0);
			continue;
		}
		pParam[idx].nServer = nServer;

		pthread_create(&pParam[idx].tid, NULL, procSendRept, &pParam[idx]);
	}

	CheckThreadStatus((ThreadParam**)pParam, nThreadCnt);

	sprintf(logMsg, "[%s():%d][main Process End.]", __func__, __LINE__);
	mnt(logMsg, 0, 0);
	
	ml_sub_end();

	return 0;
}

int CheckThreadStatus(ThreadParam** param, int nThreadCnt)
{
	char logMsg[256];
	int idx, status;
	KSKYB::CSocketTCP sockInst;
	ThreadParam tpSub[nThreadCnt];
	memcpy(tpSub, param, sizeof(ThreadParam) * nThreadCnt);

	sprintf(logMsg, "[%s():%d][CheckThreadStatus Start]", __func__, __LINE__);
	mnt(logMsg, 0, 0);

	while (true) {
		sockInst.Wait_A_Moment(0, 10);
		for (idx = 0; idx < nThreadCnt; idx++) {
			if (pthread_kill(tpSub[idx].tid, 0) != 0) {
				pthread_join(tpSub[idx].tid, (void**)&status);
				activeProcess = false;
				sprintf(logMsg, "[%s():%d][CheckThreadStatus activeProcess = false;]", __func__, __LINE__);
				mnt(logMsg, 0, 0);
				break;
			}
		}
		if (activeProcess == false) break;
	}

	sleep(3);

	for (idx = 0; idx < nThreadCnt; idx++) 
	{
		sprintf(logMsg, "[%s():%d][CheckThreadStatus Sleep(3)]", __func__, __LINE__);
		mnt(logMsg, 0, 0);
		if (pthread_kill(tpSub[idx].tid, 0) != 0) {
			pthread_join(tpSub[idx].tid, (void**)&status);
			g_oracle.freeThread(tpSub[idx].ctx);

			if( pstCodeTable != NULL)
			{
				free(pstCodeTable);
				pstCodeTable = NULL;
			}

			sprintf(logMsg, "[%s():%d][g_oracle.freeThread Success.]", __func__, __LINE__);
			mnt(logMsg, 0, 0);
			sprintf(logMsg, "[%s():%d][pWorkThread::[%d] Closed..]", __func__, __LINE__, idx);
			mnt(logMsg, 0, 0);
		}
	}

	sprintf(logMsg, "[%s():%d][CheckThreadStatus End]", __func__, __LINE__);
	mnt(logMsg, 0, 0);

	return 0;
}

void* procSendRept(void* param)
{
	ThreadParam* tp = (ThreadParam*)param;

	sprintf(tp->logMsg, "[%d][%s():%d][procSendRept() Thread Start]", tp->sockfd, __func__, __LINE__);
	mnt(tp->logMsg, 0, 0);

	int msgSize = 0;
	int nRet = 0;
	long long msgid = 0;

	char quid[32] = {0x00,};
	sprintf(quid,"%s",g_prop.getProperty("gw.quid"));
	char qname[32] = {0x00,};
	sprintf(qname,"%s",g_prop.getProperty("gw.qname"));

	CAlimtalkApi ata;
	CCurl curl;
    curl.init();
	curl.setHeaderPost("Accept: application/json");
	curl.setHeaderPost("Content-type: application/json");

	char targetUrl[128] = {0x00,};
	sprintf(targetUrl, "%s", g_prop.getProperty("gw.target_url"));
	
	char imgTargetUrl[128] = {0x00,};
	sprintf(imgTargetUrl, "%s", g_prop.getProperty("gw.target_img_url"));

	KSKYB::CSocketTCP sockInst;

	if(g_oracle.connectToOracle(tp->ctx, g_prop.getProperty("db.uid"), g_prop.getProperty("db.dsn")) < 0)
	{
		activeProcess = false;
		sprintf(tp->logMsg, "[%d][%s():%d][g_oracle.connectToOracle() Fail..activeProcess = false;]", tp->sockfd, __func__, __LINE__);
		mnt(tp->logMsg, 0, 0);
	}

	try {
		while (activeProcess) 
		{
			sockInst.Wait_A_Moment(0, 10000);
			map<string,string> mapSend;

			msgid = g_oracle.getMsgData(tp->ctx, qname, mapSend);
	
			if (msgid < 0)
			{
				activeProcess = false;
				sprintf(tp->logMsg, "[%d][%s:%d][msgid is -1(ERROR).activeProcess = false;]", tp->sockfd, __FILE__, __LINE__);
				log(tp->logMsg, 0, 0);
				break;
			}
			else if (msgid > 0)
			{
				string imgPath = mapSend["img_path"];

				//image check
				if(imgPath.size() > 0)
				{
					sprintf(tp->logMsg, "[%d][%s:%d]img exist[msgid:%lld][%s]", 
							tp->sockfd, __FILE__, __LINE__, msgid, imgPath.c_str());
					log(tp->logMsg, 0, 0);
					ST_IMG_RES img_res;
					if(getImgUrl(imgTargetUrl, (char*)imgPath.c_str(), tp, img_res) < 0)
					{
						sprintf(tp->logMsg, "[%d][%s:%d]get img url Fail[msgid:%lld]", 
								tp->sockfd, __FILE__, __LINE__, msgid);
						log(tp->logMsg, 0, 0);
						map<string, string> mapReport;
						makeImgErrReport(mapReport, img_res, msgid);
						g_oracle.setReportData((int)atoi(quid), tp->ctx, mapReport);

						mapReport.clear();	

						continue;
					}
					else
					{
						sprintf(tp->logMsg, "[%d][%s:%d]get img url success[msgid:%lld][img_url:%s]", 
								tp->sockfd, __FILE__, __LINE__, msgid, img_res.img_url.c_str());
						log(tp->logMsg, 0, 0);
						mapSend["img_url"] = img_res.img_url;
					}
				}
							
				//mnt(tp->logMsg, 0, 0);
				string parameter;
				ata.makeFtalkRequestMsg(mapSend, parameter, msgid);
				snprintf(tp->logMsg, sizeof(tp->logMsg), "send[%s]", parameter.c_str());
				log(tp->logMsg, 0, 0);
				
				curl.setOptPost(targetUrl, parameter);

				curl.response = "";
				CURLcode rVal = curl.perform();
				sprintf(tp->logMsg, "rVal:[%d]", rVal);
				log(tp->logMsg, 0, 0);

				if(CURLE_OK == rVal)
				{
					sprintf(tp->logMsg, "msgid[%lld]res:[%s]", msgid, curl.response.c_str());
					log(tp->logMsg, 0, 0);

					//response Json데이터 파싱
					ST_TALK_RES res;
				
					int ret = ata.parsingResponse(curl.response.c_str(), res);
					//int ret = ata.parsingResponse("parse data if you want", res);

					//Json 파싱 에러
					if(ret < 0)
					{
						sprintf(tp->logMsg, "msgid[%lld]response parsing error", msgid);
						log(tp->logMsg, 0, 0);
						map<string, string> mapReport;
						char cmsg_id[32] = {0x00,};
						sprintf(cmsg_id, "%lld", msgid);
						mapReport["msg_id"] = cmsg_id;
						time_t tm_time;
						struct tm *st_time;
						char buff[1024] = {0x00,};
						time(&tm_time);
						st_time = localtime(&tm_time);
						strftime(buff, 1024, "%Y%m%d%H%M%S", st_time);

						mapReport["dlv_date"] = buff;
						mapReport["res_code"] = "8001";
						mapReport["res_text"] = "fail";

						mapReport["end_telco"] = "KKO";
						mapReport["rcv_numb"] = mapSend["dst_addr"];
						g_oracle.setReportData((int)atoi(quid), tp->ctx, mapReport);

						curl.response.clear();
						mapReport.clear();	

						continue;
					}
					
					map<string, string> mapReport;

					//msg_id
					char cmsg_id[32] = {0x00,};
					sprintf(cmsg_id, "%lld", msgid);
					mapReport["msg_id"] = cmsg_id;

					//received_at 휴대폰이 메시지를 수신한 시간(읽은 시간 아님)
					if(res.received_at.size() > 0)
					{
						string report_dt;
						ata.makeDateString(res.received_at, report_dt);
						mapReport["dlv_date"] = report_dt;
					}
					else //시간 정보가 없으면 현재시간으로 set
					{
						time_t tm_time;
						struct tm *st_time;
						char buff[1024] = {0x00,};
						time(&tm_time);
						st_time = localtime(&tm_time);
						strftime(buff, 1024, "%Y%m%d%H%M%S", st_time);
						
						mapReport["dlv_date"] = buff;
					}

					if(res.code == "0000") 
					{
						mapReport["res_code"] = "1000";
						mapReport["res_text"] = "success";
					}
					else
					{
						mapReport["res_code"] = res.code;
						mapReport["res_text"] = res.message;
					}

					mapReport["end_telco"] = "KKO";
					mapReport["rcv_numb"] = mapSend["dst_addr"];
					g_oracle.setReportData((int)atoi(quid), tp->ctx, mapReport);

					curl.response.clear();
					mapReport.clear();
				}
				else
				{
					sprintf(tp->logMsg, "no res msgid[%lld]", msgid);
					log(tp->logMsg, 0, 0);
					
					map<string, string> mapReport;
					char cmsg_id[32] = {0x00,};
					sprintf(cmsg_id, "%lld", msgid);
					mapReport["msg_id"] = cmsg_id;
					time_t tm_time;
					struct tm *st_time;
					char buff[1024] = {0x00,};
					time(&tm_time);
					st_time = localtime(&tm_time);
					strftime(buff, 1024, "%Y%m%d%H%M%S", st_time);

					mapReport["dlv_date"] = buff;
					mapReport["res_code"] = "9999";
					mapReport["res_text"] = "fail";

					mapReport["end_telco"] = "KKO";
					mapReport["rcv_numb"] = mapSend["dst_addr"];
					g_oracle.setReportData((int)atoi(quid), tp->ctx, mapReport);

					curl.response.clear();
					mapReport.clear();
				}

			}
		}
	}
	catch (myException* excp) {
		sprintf(tp->logMsg, "[%d][%s():%d][%s]", tp->sockfd, __func__, __LINE__, (char*)(excp->getErrMsg().c_str()));
		mnt(tp->logMsg, 0, 0);
		sprintf(tp->logMsg, "[%s():%d][catch (myException* excp) return -1;]", __func__, __LINE__);
		mnt(tp->logMsg, 0, 0);
		delete excp;
	}
	catch (...) {

	}
		

	if( pstCodeTable != NULL)
	{
		free(pstCodeTable);
		pstCodeTable = NULL;
	}

	g_oracle.closeFromOracle(tp->ctx);
	sockInst.SocketClose();
	activeProcess = false;

#ifdef DEBUG
	sprintf(tp->logMsg, "[%d][%s():%d][procSendRept Thread End.]", tp->sockfd, __func__, __LINE__);
	mnt(tp->logMsg, 0, 0);
#endif

	return NULL;
}

void Init_Server()
{
	setpgrp();
	printf("Process Running.. Please wait 2 seconds.\n");
	sleep(2);
	signal(SIGHUP, CloseProcess);
	signal(SIGCLD, SIG_IGN);
	signal(SIGPIPE, SIG_IGN);
	signal(SIGTERM, CloseProcess);
	signal(SIGINT, CloseProcess);
	signal(SIGQUIT, CloseProcess);
	signal(SIGKILL, CloseProcess);
	signal(SIGSTOP, CloseProcess);
	signal(SIGUSR1, CloseProcess);
}

void CloseProcess(int sig)
{

	char logMsg[512];
	printf("CloseProcess Start & Exit [%d]\n", sig);
	sprintf(logMsg,"CloseProcess Start & Exit [%d]\n", sig);
	mnt(logMsg, 0, 0);
	activeProcess = false;
}

void log(char *buf, int st, int err)
{
	char log[1024] = {0x00,};
	snprintf(log, sizeof(log)-1, "[%s]%s", PROCESS_NAME, buf);

	if (ml_sub_send_log((char*)log, sizeof(log), 3, st, err) <= 0) {
		printf("%s ml_sub_send_log ERROR. %d %s\n", PROCESS_NAME, errno, strerror(errno));
	}
}

void mnt(char *buf, int st, int err)
{
	char log[1024] = {0x00,};
	snprintf(log, sizeof(log)-1, "[%s]%s", PROCESS_NAME, buf);

	if (ml_sub_send_moni((char*)log, sizeof(log), 3, st, err) <= 0) {
		printf("%s ml_sub_send_moni ERROR. %d %s\n", PROCESS_NAME, errno, strerror(errno));
	}
}

int getAllocResultTable(char* filename, CODETABLE** ppstCodeTable)
{
    FILE* fp=NULL;
    char buff[1024];
    int cnt=0;
    char* szTmp;

    fp = fopen(filename,"rt");
    if( fp == NULL )
    {
        printf("ERROR [%s]\n",strerror(errno));
        return -1;
    }

// count
    memset(buff,0x00,sizeof buff);
    while(fgets(buff,sizeof(buff),fp) )
    {
        if(buff[0] == '=')
            cnt++;
    }

    *ppstCodeTable = (CODETABLE*)malloc(sizeof(CODETABLE)*cnt);
    memset(*ppstCodeTable,0x00,sizeof(CODETABLE)*cnt);

    fseek(fp,0L,SEEK_SET);
    cnt=0;
    memset(buff,0x00,sizeof buff);
// set data
    while(fgets(buff,sizeof(buff),fp) )
    {
        if(buff[0] != '=')
            continue;

        szTmp = (char*)strtok(buff," =");
        if( szTmp == NULL)
            continue;

        memcpy((*ppstCodeTable)[cnt].tCode, szTmp,strlen(szTmp));
        szTmp = (char*)strtok(NULL," =");
        if( szTmp == NULL )
            continue;

        memcpy((*ppstCodeTable)[cnt].kCode, szTmp,strlen(szTmp));

        szTmp = (char*)strtok(NULL,"=");
        if( szTmp == NULL )
            continue;

        memcpy((*ppstCodeTable)[cnt].desc, szTmp,strlen(szTmp));
        cnt++;
    }


    fclose(fp);

    return cnt;
}

int getImgUrl(char *_target_url, char *_file_path, ThreadParam *tp, ST_IMG_RES &_res)
{
	CCurl curl_img;
	curl_img.init();
	curl_img.setHeaderPost("Content-type: multipart/form-data");
	curl_img.setHeaderPost("Expect:");
	
	//알림톡 센터에 등록된 운영자 계정 아무거나
	curl_img.setHeaderPost("account: <EMAIL>");

	sprintf(tp->logMsg, "[%d][%s:%d]file_path[%s]", tp->sockfd, __FILE__, __LINE__, _file_path);
	log(tp->logMsg, 0, 0);

	curl_img.makeMultiFormData("image", _file_path);
	curl_img.setOptHttpPost(_target_url);
	curl_img.response = "";
	
	CURLcode rVal = curl_img.perform();

	if(CURLE_OK == rVal)
	{
		CAlimtalkApi ata;
		int ret = ata.parsingImgResponse(curl_img.response.c_str(), _res);
	
		if(ret < 0)
		{
			sprintf(tp->logMsg, "[%d][%s:%d]imgUrlResponse Parsing Error[%s]", 
					tp->sockfd, __FILE__, __LINE__, curl_img.response.c_str());
			log(tp->logMsg, 0, 0);
			return -1;
		}
		
		if(strncmp(_res.code.c_str(), "200", 3) != 0)
		{
			sprintf(tp->logMsg, "[%d][%s:%d]img_url code Fail[%s]", 
					tp->sockfd, __FILE__, __LINE__, curl_img.response.c_str());
			log(tp->logMsg, 0, 0);
			return -1;
		}
		else //success
		{
			return 0;
		}	

	}
	else
	{
		sprintf(tp->logMsg, "no res getImgUrl[%d]", rVal);
		log(tp->logMsg, 0, 0);
	}

	//curl_img.cleanAll();

	return -1;
}

int makeImgErrReport(map<string, string> &_mapReport, ST_IMG_RES &_img_res, long long _msgid)
{
	char cmsg_id[32] = {0x00,};
	sprintf(cmsg_id, "%lld", _msgid);
	_mapReport["msg_id"] = cmsg_id;
	time_t tm_time;
	struct tm *st_time;
	char buff[1024] = {0x00,};
	time(&tm_time);
	st_time = localtime(&tm_time);
	strftime(buff, 1024, "%Y%m%d%H%M%S", st_time);

	_mapReport["dlv_date"] = buff;
	_mapReport["res_code"] = _img_res.code;
	_mapReport["res_text"] = "fail";

	_mapReport["end_telco"] = "KKO";
	//_mapReport["rcv_numb"] = vtBuff[2];
}
