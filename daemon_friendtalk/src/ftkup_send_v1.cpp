//============================================================================
// Name        : telco_skb_new.cpp
// Author      : KskyB Inc.
// Version     :
// Copyright   : Copyright@KSKYB
// Description : Hello World in C++, Ansi-style
//============================================================================

//**************************************************
// Includes
//**************************************************
#include <iostream>
#include <map>

#include <sys/timeb.h>

#include <signal.h>
#include <errno.h>
#include <pthread.h>

#include <unistd.h>
#include <sys/socket.h>
#include <sys/stat.h>
#include <arpa/inet.h>
#include <sys/un.h>

#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <string.h>

#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>

#include <code_info.h>
#include <message_info.h>
#include <ml_ctrlsub.h>

#include "Properties.h"
#include "myException.h"
#include "Curl.h"
#include "json.h"
#include "alimTalkApi.h"
#include "DatabaseORA_MMS.h"

using namespace std;
//**************************************************
// Defines
//**************************************************
// maximum thread POOL size
#define MAX_THREAD_POOL 128

//#define DEBUG

//**************************************************
// Structureas
//**************************************************
typedef struct _basicInfo
{
	pthread_t tid;
	int inum;
}basicInfo;

// global thread structure
typedef struct _ph
{
	//int data;    // currently used socket fd
	int index_num;// index number
}ph;

// global thread structure
// to understand current thread status
struct schedul_info
{
	map<string,string> mapSendMsg;// most recently created socket descriptor
	bool setFlag;
	multimap<int, ph> phinfo;
};

//**************************************************
// Declaration variables
//**************************************************
struct _message_info  message_info;
struct _shm_info*     shm_info;

pthread_mutex_t mutex_lock= PTHREAD_MUTEX_INITIALIZER; // mutex for critical section designation of condition variables per thread
pthread_cond_t *mycond; // condition variable per thread

pthread_mutex_t async_mutex = PTHREAD_MUTEX_INITIALIZER; // mutex for critical section designation of condition variables for thread synchronization
pthread_cond_t async_cond = PTHREAD_COND_INITIALIZER; // condition variable for thread synchronization

pthread_mutex_t mutex_db_lock= PTHREAD_MUTEX_INITIALIZER; 
pthread_mutex_t schedule_lock = PTHREAD_MUTEX_INITIALIZER; // mutex for thread schedule management

pthread_mutex_t setFlag_lock = PTHREAD_MUTEX_INITIALIZER; // mutex for data flag

schedul_info s_info;
sql_context ctx;

int activeProcess = true;
int nThreadCnt = 0;
int nServer = 0;

volatile bool bFlagClosed = false;

char quid[32];
char qname[32];
char target_url[128];
char img_target_url[128];
char wide_img_target_url[128];
char PROCESS_NO[7];
char PROCESS_NAME[36];

KSKYB::CProperties  g_prop;
KSKYB::CDatabaseORA g_oracle;

//**************************************************
// Define functions
//**************************************************
void* procSendRept(void* param);
void Init_Server();
void CloseProcess(int sig);
void mnt(char *buf, int st, int err);
void log(char *buf, int st, int err);
void makeCurrentTime(string &cTime);
int CheckThreadStatus(basicInfo** param, int nCnt);
int getImgUrl(char *_target_url, char *_file_path, ST_IMG_RES &_res, int inum);
int makeImgErrReport(map<string, string> &_mapReport, ST_IMG_RES &_img_res, long long _msgid);

int main(int argc, char* argv[])
{
	int i;
	ph myph;
	char logMsg[1024];

	g_prop.load(argv[1]);

	memset(quid,           0x00, sizeof(quid));
	memset(qname,          0x00, sizeof(qname));
	memset(target_url,     0x00, sizeof(target_url));
	memset(img_target_url, 0x00, sizeof(img_target_url));
	memset(wide_img_target_url, 0x00, sizeof(wide_img_target_url));

	nThreadCnt = g_prop.getPropertyInt("gw.tcnt");
	nServer    = g_prop.getPropertyInt("gw.server");

	sprintf(quid,                "%s", g_prop.getProperty("gw.quid"));
	sprintf(qname,               "%s", g_prop.getProperty("gw.qname"));
	sprintf(target_url,          "%s", g_prop.getProperty("gw.target_url"));
	sprintf(img_target_url,      "%s", g_prop.getProperty("gw.target_img_url"));
	sprintf(wide_img_target_url, "%s", g_prop.getProperty("gw.target_wide_img_url"));

	Init_Server();

	printf("ALIMTALK THREAD CNT:[%d]\n", nThreadCnt);

	if(ml_sub_init(PROCESS_NO, PROCESS_NAME, (char*)0, (char*)0) < 0) 
	{
		printf("ml_sub_init ERROR.\n");
		return 0;
	}
	//////////////////////////////////////////////////
	// initialize oracle
	if (g_oracle.setEnableThreads() < 0)
	{
		sprintf(logMsg, "[%s():%d][setEnableThreads ERROR. process return;]",
		        __func__, __LINE__);
		mnt(logMsg, 0, 0);

		return 0;
	}
	
	if (g_oracle.initThread(ctx) < 0 || ctx == NULL)
	{
		sprintf(logMsg, "[%s():%d][g_oracle.initThread Fail.]",
		        __func__, __LINE__);
		mnt(logMsg, 0, 0);
		return -1;
	}

	if(g_oracle.connectToOracle(ctx, g_prop.getProperty("db.uid"), g_prop.getProperty("db.dsn")) < 0)
	{
		sprintf(logMsg, "[%s():%d][g_oracle.connectToOracle() Fail..]",
		        __func__, __LINE__);
		mnt(logMsg, 0, 0);
		return -1;
	}

	//////////////////////////////////////////////////
	// initialize curl
	curl_global_init(CURL_GLOBAL_DEFAULT);

	//////////////////////////////////////////////////
	// initialize thread pool
	// create condition variables equal to the number of threads
	mycond = (pthread_cond_t *)malloc(sizeof(pthread_cond_t)*nThreadCnt);
	// initialize thread global variables
	s_info.mapSendMsg.clear(); 

	basicInfo tBasicInfo[nThreadCnt];
	memset((void *)&tBasicInfo, 0x00, sizeof(tBasicInfo));
	// create thread POOL
	for(i = 0; i < nThreadCnt; i++)
	{
		memset((void *)&myph, 0x00, sizeof(myph));
		myph.index_num = i;
		s_info.phinfo.insert(pair<int, ph>(0,  myph));
	
		tBasicInfo[i].inum = i;
		//snprintf(tBasicInfo[i].quid, sizeof(tBasicInfo[i].quid), quid); 
		//snprintf(tBasicInfo[i].qname, sizeof(tBasicInfo[i].qname), qname); 
		//snprintf(tBasicInfo[i].target_url, sizeof(tBasicInfo[i].target_url), targetUrl); 
		//snprintf(tBasicInfo[i].img_target_url, sizeof(tBasicInfo[i].img_target_url), imgTargetUrl); 

		if(0 != pthread_cond_init(&mycond[i], NULL))
		{
			sprintf(logMsg, "[%s():%d][initialization error on condition "\
			                "variable (mycond[%d]): create treads]",
			        __func__, __LINE__, i);
			mnt(logMsg, 0, 0);
			return -1;
		}
		// perform thread synchronization using condition variables
		if(0 != pthread_mutex_lock(&async_mutex))
		{
			sprintf(logMsg, "[%s():%d][mutex lock error (async_mutex): create treads]",
			        __func__, __LINE__);
			mnt(logMsg, 0, 0);
			return -1;
		}

		if(pthread_create(&tBasicInfo[i].tid, NULL, procSendRept, &tBasicInfo[i]) < 0)
		{
			sprintf(logMsg, "[%s():%d][""thread can not be created""]",
			        __func__, __LINE__);
			mnt(logMsg, 0, 0);
			return -1;
		}

		if(0 != pthread_cond_wait(&async_cond, &async_mutex))
		{
			sprintf(logMsg, "[%s():%d][condition wait(async_cond): create threads]",
			        __func__, __LINE__);
			mnt(logMsg, 0, 0);
			return -1;
		}

		if(0 != pthread_mutex_unlock(&async_mutex))
		{
			sprintf(logMsg, "[%s():%d][mutex unlock error (async_mutex): create treads]",
			        __func__, __LINE__);
			mnt(logMsg, 0, 0);
			return -1;
		}
	}

	//flag to prevent duplicate data retrieval
	s_info.setFlag = true;

	//////////////////////////////////////////////////
	// check time elapse
	double diff;
	time_t now, last;
	time(&last);

	while(activeProcess)
	{
		//check threads alive
		time(&now);
		diff = difftime(now, last);
		if(diff > 120)
		{
			if(0)
			{
				if(CheckThreadStatus((basicInfo**)tBasicInfo, nThreadCnt) < 0)
					break;
			}
			CheckThreadStatus((basicInfo**)tBasicInfo, nThreadCnt);
			time(&last);
			usleep(100000);
		}

		if(true == bFlagClosed)
		{
			sprintf(logMsg, "[INF][Start Close process]");
			mnt(logMsg, 0, 0);
			
			multimap<int, ph>::iterator mi;
			while(1)
			{
				mi = s_info.phinfo.end();
				mi--;
				// 2. look into schedule_info
				if(0 == mi->first)
				{
					curl_global_cleanup();
					sprintf(logMsg, "[INF][Check schedule_info: end properly]");
					mnt(logMsg, 0, 0);
					break;
				}
			} //while(1)
			activeProcess = false; // kill main thread
			continue;
		} // if( bFlagClosed)

		//every 0.02 seconds
		usleep(20000);

		map<string, string> mapSendMsg;
		long long msgid = 0;

		if( 0 != pthread_mutex_lock(&mutex_db_lock))
		{
			sprintf(logMsg, "[%s():%d][Mutex lock error(mutex_lock): while]",
			        __func__, __LINE__);
			mnt(logMsg, 0, 0);
			return -1;
		}
		//msgid = g_oracle.getMsgData_V2(ctx, qname, mapSendMsg);
		msgid = g_oracle.getMsgData_V4(ctx, qname, mapSendMsg);
		if( 0 != pthread_mutex_unlock(&mutex_db_lock))
		{
			sprintf(logMsg, "[%s():%d][Mutex unlock error(mutex_lock): while]",
			        __func__, __LINE__);
			mnt(logMsg, 0, 0);
			return -1;
		}

		if(msgid < 0)
		{
			//activeProcess = false;
			bFlagClosed = true;
			sprintf(logMsg, "[%s:%d][msgid is -1(ERROR)activeProcess = false]",
			        __FILE__, __LINE__);
			log(logMsg, 0, 0);
			//break;
			continue;
		}
		else if(msgid > 0)
		{	
			//20170918
			sprintf(logMsg, "getMsg id[%lld]", msgid);
			log(logMsg, 0, 0);
			
			//**************************************************
			// DEBUG
			//**************************************************
#if (DEBUG >= 5)
			snprintf(logMsg, sizeof(logMsg),
					"[DEB][check values] mms_id[%s] "
					"sender_key[%s] dst_addr[%s] res_method[%s] timeout[%s] "
					"encoding_type[%s] chat_bubble_type[%s] targeting[%s] tmpl_cd[%s] "
					"app_user_id[%s] push_alarm[%s] ",
					mapSendMsg["mms_id"].c_str(), mapSendMsg["sender_key"].c_str(),
					mapSendMsg["dst_addr"].c_str(), mapSendMsg["res_method"].c_str(),
					mapSendMsg["timeout"].c_str(), mapSendMsg["encoding_type"].c_str(),
					mapSendMsg["chat_bubble_type"].c_str(), mapSendMsg["targeting"].c_str(),
					mapSendMsg["tmpl_cd"].c_str(), mapSendMsg["app_user_id"].c_str(),
					mapSendMsg["push_alarm"].c_str()
			);
			log(logMsg, 0, 0);

			snprintf(logMsg, sizeof(logMsg),
				"[DEB][check values] mms_id[%s] message_variable[%s] button_variable[%s] ", 
				mapSendMsg["mms_id"].c_str(),mapSendMsg["message_variable"].c_str(),mapSendMsg["button_variable"].c_str()
				);
			log(logMsg, 0, 0);

			snprintf(logMsg, sizeof(logMsg),
					"[DEB][check values] mms_id[%s] "
					"coupon_variable[%s] image_variable[%s] video_variable[%s] "
					, mapSendMsg["mms_id"].c_str(), mapSendMsg["coupon_variable"].c_str(),
					mapSendMsg["image_variable"].c_str(), mapSendMsg["video_variable"].c_str()					
					);
			log(logMsg, 0, 0);

			snprintf(logMsg, sizeof(logMsg),
					"[DEB][check values] mms_id[%s] "					
					"commerce_variable[%s] carousel_variable[%s] reserve[%s]",
					mapSendMsg["mms_id"].c_str(), mapSendMsg["commerce_variable"].c_str(),
					mapSendMsg["carousel_variable"].c_str(),	mapSendMsg["reserve"].c_str()
					);
			log(logMsg, 0, 0);
#endif

			while(true)
			{
				multimap<int, ph>::iterator mi;
				mi = s_info.phinfo.begin();

				if(mi->first == 1)
				{
					//20170918
					sprintf(logMsg, "[no thread available]");
					log(logMsg, 0, 0);

					//if no available threads, retry after 0.1 seconds
					usleep(100000);
					continue;
				}

				if(s_info.setFlag == false)
				{
					//cout << "data unavailable" << endl;
					//20170918
					sprintf(logMsg, "[data unavailable]");
					log(logMsg, 0, 0);
					
					usleep(100000);
					continue;
				}

				ph tmpph;

				if (0 != pthread_mutex_lock(&setFlag_lock)){
					sprintf(logMsg, "[%s():%d][Mutex lock (setFlag_lock): main]",
					        __func__, __LINE__);
					mnt(logMsg, 0, 0);
					return -1;
				}

				//flag to prevent duplicate data transmission
				s_info.setFlag = false;
				s_info.mapSendMsg = mapSendMsg;

				if (0 != pthread_mutex_unlock(&setFlag_lock)){
					sprintf(logMsg, "[%s():%d][Mutex lock (setFlag_lock): main]",
					        __func__, __LINE__);
					mnt(logMsg, 0, 0);
					return -1;
				}
				
				tmpph.index_num = mi->second.index_num;
				
				if( 0 != pthread_mutex_lock(&schedule_lock))
				{
					//sprintf(logMsg, "[TST]Mutex lock (mutex_lock): while");
					sprintf(logMsg, "[%s():%d][Mutex lock (schedule_lock): main]",
					        __func__, __LINE__);
					mnt(logMsg, 0, 0);
					return -1;
				}	
				
				s_info.phinfo.erase(mi);
				s_info.phinfo.insert(pair<int, ph>(1, tmpph));
				
				if( 0 != pthread_mutex_unlock(&schedule_lock))
				{
					//sprintf(logMsg, "[TST]Mutex lock (mutex_lock): while");
					sprintf(logMsg, "[%s():%d][Mutex unlock (schedule_lock): main]",
					        __func__, __LINE__);
					mnt(logMsg, 0, 0);
					return -1;
				}
				
				//pthread_mutex_lock(&mutex_lock);
				if( 0 != pthread_cond_signal(&mycond[mi->second.index_num]))
				{
					sprintf(logMsg, "[%s():%d][Condition variables error(mycond)]",
					        __func__, __LINE__);
					mnt(logMsg, 0, 0);
					return -1;
				}
				//pthread_mutex_unlock(&mutex_lock);
				break;
			}
		}
		else
		{
			continue;
		}
		mapSendMsg.clear();
	}

	sprintf(logMsg, "[%s()][main Process End.]", __func__);
	mnt(logMsg, 0, 0);
	
	free(mycond);
	
	ml_sub_end();
	g_oracle.closeFromOracle(ctx);

	printf("ftalk ml_sub_end\n");

	return 0;
}

int CheckThreadStatus(basicInfo** param, int nThreadCnt)
{
  char logMsg[256];
  int idx, status;
  basicInfo tpSub[nThreadCnt];
  memset((void *)&tpSub, 0x00, sizeof(tpSub));
  memcpy(tpSub, param, (sizeof(basicInfo) * nThreadCnt));

	sprintf(logMsg, "[%s()][thread check]", __func__);
	mnt(logMsg, 0, 0);

	for(idx = 0; idx < nThreadCnt; idx++) 
	{
		if(pthread_kill(tpSub[idx].tid, 0) != 0)
		{
			pthread_join(tpSub[idx].tid, (void**)&status);
			//activeProcess = false;
			bFlagClosed = true;
			sprintf(logMsg, "[%s()][CheckThreadStatus fail]", __func__);
			mnt(logMsg, 0, 0);
			return -1;
		}
	}

	return 0;
}

void* procSendRept(void* param)
{
	basicInfo bi;
	memset((void *)&bi, 0x00, sizeof(bi));
	memcpy(&bi, param, sizeof(bi));
	int mynum = bi.inum;
	//char target_url[128] = {0x00,};
	//char quid[32] = {0x00,};
	//char qname[32] = {0x00,};
	char logMsg[1024];
	
	memset(logMsg, 0x00, sizeof(logMsg));

 	// condition variable for thread synchronization
	if( 0 != pthread_mutex_lock(&async_mutex))
	{
		sprintf(logMsg, "[%s():%d][Mutex lock error (async_mutex): TID[%d]]",
		        __func__, __LINE__, mynum);
		mnt(logMsg, 0, 0);
		return NULL;
	}
	if( 0 != pthread_cond_signal(&async_cond))
	{
		sprintf(logMsg, "[%s():%d][Signal Condition varible error (async_cond): TID[%d]]",
		        __func__, __LINE__, mynum);
		mnt(logMsg, 0, 0);
		return NULL;
	}
	if( 0 != pthread_mutex_unlock(&async_mutex))
	{
		sprintf(logMsg, "[%s():%d][Mutex unlock error (async_mutex): TID[%d]]",
		        __func__, __LINE__, mynum);
		mnt(logMsg, 0, 0);
		return NULL;
	}

	sprintf(logMsg, "procSendRept() [mynum:%d]Thread Start", mynum);
	log(logMsg, 0, 0);

	try {
		while(activeProcess) 
		{
			//////////////////////////////////////////////////
			// get message data
			map<string,string> mapSendMsg;
			if(0 != pthread_mutex_lock(&mutex_lock))
			{
				sprintf(logMsg, "[%s():%d][Mutex lock error(mutex_lock): TID[%d]]",
				        __func__, __LINE__, mynum);
				mnt(logMsg, 0, 0);
				return NULL;
			}
			if(0 != pthread_cond_wait(&mycond[mynum], &mutex_lock))
			{
				sprintf(logMsg, "[%s():%d][Condition variables error(mycond): TID[%d]]",
				        __func__, __LINE__, mynum);
				mnt(logMsg, 0, 0);
				return NULL;
			}
			if(0 != pthread_mutex_unlock(&mutex_lock))
			{
				sprintf(logMsg, "[%s():%d][Mutex unlock unlock(mutex_lock): TID[%d]]",
				        __func__, __LINE__, mynum);
				mnt(logMsg, 0, 0);
				return NULL;
			}

			if (0 != pthread_mutex_lock(&setFlag_lock)){
				sprintf(logMsg, "[%s():%d][Mutex lock(setFlag_lock): TID[%d]]",
				        __func__, __LINE__, mynum);
				mnt(logMsg, 0, 0);
				return NULL;
			}

			//////////////////////////////////////////////////
			// check data
			//----- check overlap
			mapSendMsg = s_info.mapSendMsg;
			s_info.setFlag = true;

			if (0 != pthread_mutex_unlock(&setFlag_lock)){
				sprintf(logMsg, "[%s():%d][Mutex unlock(setFlag_lock): TID[%d]]",
				        __func__, __LINE__, mynum);
				mnt(logMsg, 0, 0);
				return NULL;
			}
			
            #if (DEBUG >= 5)
			//***** DEBUG
			// Log 1: Basic message info
			snprintf(logMsg, sizeof(logMsg),"[DEB]TID[%d] mms_id[%s] sender_key[%s] dst_addr[%s] "
			                "res_method[%s] timeout[%s] encoding_type[%s] chat_bubble_type[%s] "
			                "targeting[%s] tmpl_cd[%s] app_user_id[%s] push_alarm[%s]",
			        mynum, mapSendMsg["mms_id"].c_str(), mapSendMsg["sender_key"].c_str(),
			        mapSendMsg["dst_addr"].c_str(), mapSendMsg["res_method"].c_str(), mapSendMsg["timeout"].c_str(),
			        mapSendMsg["encoding_type"].c_str(),mapSendMsg["chat_bubble_type"].c_str(),
			        mapSendMsg["targeting"].c_str(),mapSendMsg["tmpl_cd"].c_str(),
			        mapSendMsg["app_user_id"].c_str(),mapSendMsg["push_alarm"].c_str()
			        );
			log(logMsg, 0, 0);

			// Log 2: Message variables
			snprintf(logMsg, sizeof(logMsg),"[DEB]TID[%d] message_variable[%s] button_variable[%s] "
			                "coupon_variable[%s] image_variable[%s] video_variable[%s]",
			        mynum, mapSendMsg["message_variable"].c_str(),mapSendMsg["button_variable"].c_str(),
			        mapSendMsg["coupon_variable"].c_str(),mapSendMsg["image_variable"].c_str(),
			        mapSendMsg["video_variable"].c_str()
			        );
			log(logMsg, 0, 0);

			// Log 3: Commerce and other variables
			snprintf(logMsg, sizeof(logMsg),"[DEB]TID[%d] commerce_variable[%s] carousel_variable[%s] reserve[%s]",
			        mynum, mapSendMsg["commerce_variable"].c_str(),
			        mapSendMsg["carousel_variable"].c_str(),mapSendMsg["reserve"].c_str()
			        );
			log(logMsg, 0, 0);
			//*****
            #endif
			//pthread_mutex_unlock(&mutex_lock);
	
			long long mms_id = atoll(mapSendMsg["mms_id"].c_str());

			map<string, string> mapReport;
			CCurl curl;

			//----- kko image check

			//////////////////////////////////////////////////
			// prepare for transmit data
			//make msg
			string parameter;
			CAlimtalkApi ata;

			sprintf(logMsg, "makeFtalkRequestMsg_V4 begin mms_id[%lld]",
					mms_id);
			log(logMsg, 0, 0);
			// get data from Queue and convert it into string type data(mapSendMsg -> parameter)
			//ata.makeFtalkRequestMsg_V3(mapSendMsg, parameter, mms_id);
			ata.makeFtalkRequestMsg_V4(mapSendMsg, parameter, mms_id);

			#if (DEBUG >= 5)
			//*****  DEBUG // segment fault
			snprintf(logMsg, sizeof(logMsg), "[DEB] parameter[%s]", parameter.c_str());
			log(logMsg, 0, 0);
			//*****
			#endif

			snprintf(logMsg, sizeof(logMsg), "tnum[%d]mms_id[%lld]", bi.inum, mms_id);
			log(logMsg, 0, 0);

			//////////////////////////////////////////////////
			// send data to kakao
			curl.init();
			curl.setHeaderPost("Accept: application/json");
			curl.setHeaderPost("Content-type: application/json");
			//X-Serial-Number: 8-digit sending request date-serial number(mms_id)

			// generate current date in 8-digit format (YYYYMMDD)
			time_t tm_time;
			struct tm *st_time;
			char date_buff[16] = {0x00,};
			time(&tm_time);
			st_time = localtime(&tm_time);
			strftime(date_buff, sizeof(date_buff), "%Y%m%d", st_time);

			// generate X-Serial-Number header: 8-digit date-mms_id
			char serial_number[128] = {0x00,};
			snprintf(serial_number, sizeof(serial_number), "X-Serial-Number: %s-%lld", date_buff, mms_id);
			curl.setHeaderPost(serial_number);

#if (DEBUG >= 5)
            snprintf(logMsg, sizeof(logMsg), "tnum[%d]mms_id[%lld] serial_number[%s]",
                bi.inum, mms_id, serial_number);
            log(logMsg, 0, 0);
#endif

			curl.setOptPost(target_url, parameter);
			curl.response.clear();

			CURLcode rVal;
#ifndef NOSEND
			rVal = curl.perform();
#else
			// NOSEND mode: Set success result and log transmitted message
			rVal = CURLE_OK;
			snprintf(logMsg, sizeof(logMsg), "tnum[%d]mms_id[%lld] NOSEND mode - transmitted message: %.*s",
					bi.inum, mms_id, 500, parameter.c_str());
			log(logMsg, 0, 0);

			// Set mock successful response for NOSEND mode
			curl.response = "{\"status\": 200, \"result\": \"N\"}";
#endif

			sprintf(logMsg, "tnum[%d]mms_id[%lld]rVal[%d]", bi.inum, mms_id, rVal);
			log(logMsg, 0, 0);

			if (CURLE_OK == rVal) {
				snprintf(logMsg, sizeof(logMsg), "tnum[%d]mms_id[%lld]res[%s]",
						bi.inum, mms_id, curl.response.c_str());
				log(logMsg, 0, 0);

				//response Json data parsing
				ST_TALK_RES res;
				int ret = ata.parsingResponse(curl.response.c_str(), res);

				if (ret < 0) {
					//Json parsing error
					sprintf(logMsg, "mms_id[%lld] response parsing error", mms_id);
					log(logMsg, 0, 0);

					char cmsg_id[32] = {0x00,};
					sprintf(cmsg_id, "%lld", mms_id);
					//					mapReport["msg_id"] = cmsg_id;

					string cTime;
					makeCurrentTime(cTime);

					mapReport["msg_id"] = cmsg_id;
					mapReport["dlv_date"] = cTime;
					mapReport["res_code"] = "8001";
					mapReport["res_text"] = "fail";
					mapReport["end_telco"] = "KKO";
					mapReport["rcv_numb"] = mapSendMsg["dst_addr"];
				} else {
					char cmsg_id[32] = {0x00,};
					sprintf(cmsg_id, "%lld", mms_id);
					mapReport["msg_id"] = cmsg_id;

					//received_at time when mobile phone received the message (not read time)
					if (res.received_at.size() > 0) {
						string report_dt;
						ata.makeDateString(res.received_at, report_dt);
						mapReport["dlv_date"] = report_dt;
					} else //if no time information, set to current time
					{
						string cTime;
						makeCurrentTime(cTime);
						mapReport["dlv_date"] = cTime;
					}

					if (res.code == "0000") {
						mapReport["res_code"] = "1000";
						mapReport["res_text"] = "success";
					} else {
						// error code - use new format fields
						if (!res.error_code.empty()) {
							mapReport["res_code"] = res.error_code;
						} else {
							mapReport["res_code"] = res.code;  // fallback to old format
						}

						if (!res.error_detail.empty()) {
							mapReport["res_text"] = res.error_detail;
						} else {
							mapReport["res_text"] = "fail";
						}
					}
					mapReport["end_telco"] = "KKO";
					mapReport["rcv_numb"] = mapSendMsg["dst_addr"];
				}
			}
			else {
				//////////////////////////////////////////////////
				// Kakao error(unknown error)
				sprintf(logMsg, "no response msgid[%lld]", mms_id);
				log(logMsg, 0, 0);

				char cmsg_id[32] = {0x00,};
				sprintf(cmsg_id, "%lld", mms_id);

				string cTime;
				makeCurrentTime(cTime);

				mapReport["msg_id"] = cmsg_id;
				mapReport["dlv_date"] = cTime;
				mapReport["res_code"] = "8002";
				mapReport["res_text"] = "fail";
				mapReport["end_telco"] = "KKO";
				mapReport["rcv_numb"] = mapSendMsg["dst_addr"];
			}

			if (0 != pthread_mutex_lock(&mutex_db_lock)) {
				sprintf(logMsg, "[%s():%d][Mutex lock error(mutex_db_lock): imgFaultFlag]", __func__, __LINE__);
				mnt(logMsg, 0, 0);
				return NULL;
				//exit(0);
			}

			if (g_oracle.setReportData((int) atoi(quid), ctx, mapReport) < 0) {
				if (0 != pthread_mutex_unlock(&mutex_db_lock)) {
					sprintf(logMsg, "[%s():%d][setReportData error Mutex unlock (mutex_db_lock): mms_id[%lld] TID[%d]]",
							__func__, __LINE__, mms_id, mynum);
					mnt(logMsg, 0, 0);
					return NULL;
				}
				sprintf(logMsg, "[%s():%d][setReportData error: TID[%d]]", __func__, __LINE__, mynum);
				mnt(logMsg, 0, 0);
				return NULL;
			}

			if (0 != pthread_mutex_unlock(&mutex_db_lock)) {
				sprintf(logMsg, "[%s():%d][Mutex unlock error(mutex_db_lock): imgFaultFlag]", __func__, __LINE__);
				mnt(logMsg, 0, 0);
				return NULL;
				//exit(0);
			}
			//}
        			
			//////////////////////////////////////////////////
			// release resources
			mapSendMsg.clear();
			mapReport.clear();
			curl.cleanAll();

			ph tmpph;
			tmpph.index_num = mynum;
			
			multimap<int, ph>::iterator mi;
			if( 0 != pthread_mutex_lock(&schedule_lock))
			{
				sprintf(logMsg, "[%s():%d][Mutex lock (schedule_lock): procSendRept]", __func__, __LINE__);
				mnt(logMsg, 0, 0);
				return NULL;
			}
				
			mi = s_info.phinfo.begin();
			
			while(mi != s_info.phinfo.end())
			{	
				if(mi->second.index_num == tmpph.index_num)
				{	
					s_info.phinfo.erase(mi);
					s_info.phinfo.insert(pair<int, ph>(0, tmpph));
					break;	
				}
				mi++;	
			}
			
			if(0 != pthread_mutex_unlock(&schedule_lock))
			{
				sprintf(logMsg, "[%s():%d][Mutex unlock (schedule_lock): procSendRept]", __func__, __LINE__);
				mnt(logMsg, 0, 0);
				return NULL;
			}
		} //while(activeProcess)
	}
	catch (myException* excp) {
		sprintf(logMsg, "[%s():%d][%s]", __func__, __LINE__, (char*)(excp->getErrMsg().c_str()));
		mnt(logMsg, 0, 0);
		sprintf(logMsg, "[%s():%d][catch (myException* excp) return -1;]", __func__, __LINE__);
		mnt(logMsg, 0, 0);
		delete excp;
	}
	catch (...) {
	}

	sprintf(logMsg, "procSendRept Thread[%d] End", mynum);
	mnt(logMsg, 0, 0);

	return NULL;
}

void Init_Server()
{
	setpgrp();
	printf("Process Running.. Please wait 2 seconds.\n");
	sleep(2);
	signal(SIGHUP,  CloseProcess);
	signal(SIGCLD,  SIG_IGN);
	signal(SIGPIPE, SIG_IGN);
	signal(SIGTERM, CloseProcess);
	signal(SIGINT,  CloseProcess);
	signal(SIGQUIT, CloseProcess);
	signal(SIGKILL, CloseProcess);
	signal(SIGSTOP, CloseProcess);
	signal(SIGUSR1, CloseProcess);
	
	//	signal(SIGABRT, CloseProcess); //10
	signal(SIGSEGV, CloseProcess); //11
}

void CloseProcess(int sig)
{
	//activeProcess = false;
	bFlagClosed = true;
	/*
	for(int i = 0; i < nThreadCnt; i++)
	{
		int ret = pthread_cond_signal(&mycond[i]);
	}
	*/
	char logMsg[256];
	sprintf(logMsg,"CloseProcess Start & Exit [SIG:%d]\n", sig);
	mnt(logMsg, 0, 0);
	
	if(sig == 11)
	exit(0);
//	curl_global_cleanup();
}

void makeCurrentTime(string &cTime)
{
	time_t tm_time;
	struct tm *st_time;
	char buff[1024] = {0x00,};
	time(&tm_time);
	st_time = localtime(&tm_time);
	strftime(buff, 1024, "%Y%m%d%H%M%S", st_time);
	cTime = buff;
}

void log(char *buf, int st, int err)
{
	char log[1024] = {0x00,};
	snprintf(log, sizeof(log)-1, "[%s]%s", PROCESS_NAME, buf);

	if (ml_sub_send_log((char*)log, sizeof(log), 3, st, err) <= 0) {
		printf("%s ml_sub_send_log ERROR. %d %s\n", PROCESS_NAME, errno, strerror(errno));
	}	
}

void mnt(char *buf, int st, int err)
{
	char log[1024] = {0x00,};
	snprintf(log, sizeof(log)-1, "[%s]%s", PROCESS_NAME, buf);

	if (ml_sub_send_moni((char*)log, sizeof(log), 3, st, err) <= 0) {
		printf("%s ml_sub_send_moni ERROR. %d %s\n", PROCESS_NAME, errno, strerror(errno));
	}
}

int getImgUrl(char* _target_url, char* _file_path, ST_IMG_RES& _res, int inum)
{
  CCurl curl_img;
  curl_img.init();
  curl_img.setHeaderPost("Content-type: multipart/form-data");
  curl_img.setHeaderPost("Expect:");
	char logMsg[1024] = {0x00,};

  //any operator account registered in the notification talk center
	curl_img.setHeaderPost("account: <EMAIL>");

	sprintf(logMsg, "tnum[%d]file_path[%s]", inum, _file_path);
	log(logMsg, 0, 0);

	curl_img.makeMultiFormData("image", _file_path);
	
	curl_img.setOptHttpPost(_target_url);
	curl_img.response = "";

	CURLcode rVal = curl_img.perform();
	
	if(CURLE_OK == rVal)
  {
    CAlimtalkApi ata;
    int ret = ata.parsingImgResponse(curl_img.response.c_str(), _res);
    
    if(ret < 0)
    {
      sprintf(logMsg, "tnum[%d]imgUrlResponse Parsing Error[%s]",
              inum, curl_img.response.c_str());
      log(logMsg, 0, 0);
    }

    if(strncmp(_res.code.c_str(), "200", 3) != 0)
    {
    	//////////////////////////////////////////////////
    	// Error code table
    	//************************************************
    	// 200: Success
    	// 403: No authorization
    	// 405: Parameter error
    	// 601: InvalidImageMaxLengthException(over capacity)
    	// 602: InvalidImageSizeException(not permitted image size)
    	// 603: inavlidImageFormatException(not supported image format)
    	//////////////////////////////////////////////////
	  	sprintf(logMsg, "tnum[%d]img_url code Fail[%s]",
	  	        inum, curl_img.response.c_str());
	    log(logMsg, 0, 0);
    }
    else //success
    {
    	//20170407 blocked due to ORA-24550 error
    	curl_img.cleanAll_img();
    	
    	return 0;
    }
  }
  else
  {
    sprintf(logMsg, "tnum[%d]no res getImgUrl[%d]", inum, rVal);
    log(logMsg, 0, 0);
  }
  curl_img.cleanAll_img();
//curl_img.response.clear();
  return -1;
}

int makeImgErrReport(map<string, string> &_mapReport, ST_IMG_RES &_img_res, long long _msgid)
{
	char logMsg[1024] = {0x00,};
  char cmsg_id[32]  = {0x00,};

  time_t tm_time;
  struct tm *st_time;
  char buff[1024] = {0x00,};
  time(&tm_time);
  st_time = localtime(&tm_time);
  strftime(buff, 1024, "%Y%m%d%H%M%S", st_time);

  sprintf(cmsg_id, "%lld", _msgid);

  _mapReport["msg_id"] = cmsg_id;
  _mapReport["dlv_date"] = buff;
  
  if(atoi(_img_res.code.c_str()) > 0)
  {
  	_mapReport["res_code"] = _img_res.code;
  }
  else
  {
  	_mapReport["res_code"] = "8010";	
  }

  _mapReport["res_text"] = "fail";
  _mapReport["end_telco"] = "KKO";
}

