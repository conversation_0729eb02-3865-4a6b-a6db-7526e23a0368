# CMake Database Configuration

이 프로젝트는 CMake 빌드 시 Pro*C 컴파일을 위해 데이터베이스 연결 정보가 필요합니다. 보안을 위해 데이터베이스 정보를 CMakeLists.txt에 하드코딩하지 않고 다음 두 가지 방법 중 하나를 사용할 수 있습니다.

## 방법 1: 환경변수 사용 (권장 - CI/CD 환경)

빌드 전에 다음 환경변수를 설정하세요:

```bash
export SMS_DBSTRING=your_sms_database_string
export SMS_DBID=your_sms_database_id
export SMS_DBPASS=your_sms_database_password
export MMS_DBSTRING=your_mms_database_string
export MMS_DBID=your_mms_database_id
export MMS_DBPASS=your_mms_database_password

# CMake 빌드
mkdir -p build
cd build
cmake ..
make
```

## 방법 2: 설정 파일 사용 (권장 - 로컬 개발)

1. 템플릿 파일을 복사하여 설정 파일을 생성:
```bash
cp db_config.cmake.template db_config.cmake
```

2. `db_config.cmake` 파일을 편집하여 실제 데이터베이스 정보를 입력:
```cmake
set(SMS_DBSTRING "actual_sms_database_string")
set(SMS_DBID "actual_sms_database_id")
set(SMS_DBPASS "actual_sms_database_password")
set(MMS_DBSTRING "actual_mms_database_string")
set(MMS_DBID "actual_mms_database_id")
set(MMS_DBPASS "actual_mms_database_password")
```

3. CMake 빌드 실행:
```bash
mkdir -p build
cd build
cmake ..
make
```

## 우선순위

환경변수가 설정 파일보다 우선순위가 높습니다:
1. 환경변수 (최우선)
2. db_config.cmake 파일
3. 둘 다 없으면 빌드 오류

## 주의사항

- `db_config.cmake` 파일은 `.gitignore`에 포함되어 있어 버전 관리에서 제외됩니다.
- 실제 데이터베이스 정보를 포함한 파일을 절대 커밋하지 마세요.
- 환경변수나 설정 파일 중 어느 것도 설정되지 않으면 CMake 구성 시 오류가 발생합니다.

## 빌드 타겟

- `make ftalk_send_v3`: ftalk_send_v3 빌드 (기본)
- 추가 타겟들은 CMakeLists.txt에서 주석 해제하여 활성화 가능

## Oracle 환경 요구사항

- Oracle Instant Client 21c
- Pro*C 컴파일러
- 적절한 ORACLE_HOME 환경변수 설정
