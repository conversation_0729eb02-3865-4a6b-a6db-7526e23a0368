#include <cstdio>
#include <iostream>
#include <string>
#include <sstream>
using namespace std;

#include <curl/curl.h>


enum {
	ERROR_ARGS = 1,
	ERROR_CURL_INIT = 2
};

enum {
	OPTION_FALSE = 0,
	OPTION_TRUE = 1
};

enum {
	FLAG_DEFAULT = 0
};

const char *targetUrl;



int main(int argc, char *argv[])
{
	targetUrl = "https://dev-alimtalk-api.kakao.com/v1/d83fdfdf/sendMessage";
	
	curl_global_init(CURL_GLOBAL_ALL);

	CURL *ctx = curl_easy_init();

	if(NULL == ctx) 
	{
		cerr<<"Unable to initialize cURL interface" << endl;
		return(ERROR_CURL_INIT);
	}
	
	curl_easy_setopt(ctx, CURLOPT_URL, targetUrl);
	curl_easy_setopt(ctx, CURLOPT_NOPROGRESS, OPTION_TRUE);

	curl_easy_setopt(ctx, CURLOPT_WRITEDATA, stdout);
	
	curl_slist *responseHeaders = NULL;
	responseHeaders = curl_slist_append(responseHeaders, "Accept:application/json");
	responseHeaders = curl_slist_append(responseHeaders, "Content-type:application/json");
	curl_easy_setopt(ctx, CURLOPT_HTTPHEADER, responseHeaders);

	const string postData = "{\"serial_number\":\"test_00001\", \"sender_key\":\"2662e99eb7a1f21abb3955278e9955f5a9a99b62\", \"phone_number\":\"01046928454\", \"template_code\":\"A001_01\", \"message\":\"이세종님이 보낸 등기 123456을 아이유님께 배달 완료 1588-1300\", \"response_method\":\"realtime\"}";

	cout<<"post data:"<<postData<<endl;

	curl_easy_setopt(ctx, CURLOPT_POSTFIELDS, postData.c_str());
	curl_easy_setopt(ctx, CURLOPT_POSTFIELDSIZE, postData.size());
	curl_easy_setopt(ctx, CURLOPT_POST, OPTION_TRUE);
	curl_easy_setopt(ctx, CURLOPT_TIMEOUT, 4);

	cout<<"BEGIN:response"<<endl;
	CURLcode rc = curl_easy_perform(ctx);
	
	if(CURLE_OK != rc)
	{
		cerr<<"Error from cURL: "<< curl_easy_strerror(rc)<<std::endl;
	}

	curl_slist_free_all(responseHeaders);
	curl_easy_cleanup(ctx);
	curl_global_cleanup();
	exit(0);


}
