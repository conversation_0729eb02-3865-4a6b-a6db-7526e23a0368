#ifndef _KSKYB_SIGNAL_H_
#define _KSKYB_SIGNAL_H_
#include <signal.h>
#include <errno.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>

#ifdef __cplusplus
extern "C" {
#endif

// Signal handler function declaration
void _closeProcessCommon(int sig);

#ifdef __cplusplus
}

class CKSSignal
{
    public :
        CKSSignal();
        virtual ~CKSSignal();
        int initNomalServer();
};

#endif /* __cplusplus */

#endif /* _KSKYB_SIGNAL_H_ */
