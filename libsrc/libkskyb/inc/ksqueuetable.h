#ifndef _KSKYB_QUEUE_TABLE_H_
#define _KSKYB_QUEUE_TABLE_H_


#include <iostream>
#include "ksqueue.h"
#include <cstring>


using namespace std;

typedef struct _tag_QueueList { 
        CKSQueue *pqueue;
} QueueList;


class CKSQueueTable
{
    public:
        CKSQueueTable();
        virtual ~CKSQueueTable();
        int getQueue(char *buff);
    private:
//        map<string,QueueMapData> QueueMap;
//        map<string,QueueMapData>::iterator k;
};





#endif


