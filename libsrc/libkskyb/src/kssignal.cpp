#include "kssignal.h"
#include <stdlib.h>

// Signal handler function implementation
extern "C" {
void _closeProcessCommon(int sig)
{
    printf("Signal %d received, terminating process...\n", sig);
    exit(0);
}
}

CKSSignal::CKSSignal()
{
}

CKSSignal::~CKSSignal()
{
}

int CKSSignal::initNomalServer()
{
    int errno ;
    struct sigaction ignAct;  
    struct sigaction closeAct;
    int ret;

    ignAct.sa_flags = 0;      
    ignAct.sa_handler = SIG_IGN;   

    closeAct.sa_flags = 0;    
    closeAct.sa_handler = _closeProcessCommon;

    setpgrp();                

    if(sigemptyset(&ignAct.sa_mask) != 0 )  
    {
        printf("시그널 초기화 실패 sigemptyset:%s",strerror(errno));
        return -1;
    }


    if(sigaction(SIGHUP  ,&ignAct ,NULL) != 0 )
    {
        printf("시그널 초기화 실패 SIGHUP:%s",strerror(errno));
        return -1;
    }


    if(sigaction(SIGCLD  ,&ignAct ,NULL) != 0 )
    {
        printf("시그널 초기화 실패 SIGCLD:%s",strerror(errno));
        return -1;
    }

    if(sigaction(SIGCHLD  ,&ignAct ,NULL) != 0 )
    {
        printf("시그널 초기화 실패 SIGCLD:%s",strerror(errno));
        return -1;
    }



    if(sigaction(SIGTERM  ,&ignAct ,NULL) != 0 )
    {
        printf("시그널 초기화 실패 SIGTERM:%s",strerror(errno));
        return -1;
    }


    if(sigaction(SIGINT  ,&closeAct ,NULL) != 0 )
    {
        printf("시그널 초기화 실패 SIGINT:%s",strerror(errno));
        return -1;
    }








    return 0;
}


