cmake_minimum_required(VERSION 3.16)
project(libsrc)

# 하위 디렉토리 추가
add_subdirectory(libkskyb)
add_subdirectory(orapp)
add_subdirectory(occi)

# 모든 라이브러리를 빌드하는 타겟
add_custom_target(build_all_libsrc
    DEPENDS all_libkskyb liborapp occi
    COMMENT "Building all libsrc libraries"
)

# 모든 라이브러리를 설치하는 타겟
add_custom_target(install_all_libsrc
    DEPENDS install_libkskyb install_orapp
    COMMENT "Installing all libsrc libraries to $HOME/library"
)

# 모든 라이브러리를 정리하는 타겟
add_custom_target(clean_all_libsrc
    DEPENDS clean_libkskyb clean_orapp
    COMMENT "Cleaning all libsrc build artifacts"
)
