#include "query.hh"
#include "conn.hh"
#include "log.hh"

namespace ORAPP {

Query::Query(Connection* conn) 
    : _connection(conn), _stmt(nullptr), _sql(""), _state(QUERY_STATE_INIT),
      _rows_affected(0), _autocommit(true), _has_results(false) {
    
    if (!_connection) {
        set_error("Invalid connection provided to Query constructor");
        _state = QUERY_STATE_ERROR;
    }
}

Query::~Query() {
    clear();
}

Query& Query::operator<<(const std::string& sql) {
    _sql_stream << sql;
    return *this;
}

Query& Query::operator<<(const char* sql) {
    if (sql) {
        _sql_stream << sql;
    }
    return *this;
}

Query& Query::operator<<(int value) {
    _sql_stream << value;
    return *this;
}

Query& Query::operator<<(long value) {
    _sql_stream << value;
    return *this;
}

Query& Query::operator<<(double value) {
    _sql_stream << value;
    return *this;
}

Query& Query::operator<<(float value) {
    _sql_stream << value;
    return *this;
}

bool Query::execute() {
    // Get SQL from stream (updated after bind operations)
    std::string current_sql = _sql_stream.str();
    if (!current_sql.empty()) {
        _sql = current_sql;
    }

    if (_sql.empty()) {
        set_error("No SQL statement provided");
        return false;
    }
    
    log_query_info(_sql.c_str(), "EXECUTING");
    
    clear_error();
    _state = QUERY_STATE_INIT;
    
    // Prepare statement
    if (!prepare_statement()) {
        _state = QUERY_STATE_ERROR;
        return false;
    }
    
    _state = QUERY_STATE_PREPARED;
    
    // Execute statement
    if (!execute_statement()) {
        _state = QUERY_STATE_ERROR;
        return false;
    }
    
    _state = QUERY_STATE_EXECUTED;
    
    // Setup fetch variables for SELECT statements
    if (_has_results) {
        if (!setup_fetch_variables()) {
            _state = QUERY_STATE_ERROR;
            return false;
        }
        _state = QUERY_STATE_FETCHING;
    } else {
        _state = QUERY_STATE_FINISHED;
    }
    
    log_query_info(_sql.c_str(), "SUCCESS");
    return true;
}

bool Query::execute(const std::string& sql) {
    clear();
    _sql_stream.str("");
    _sql_stream << sql;
    return execute();
}

bool Query::next() {
    if (_state != QUERY_STATE_FETCHING) {
        set_error("Query not in fetching state");
        return false;
    }
    
    if (!_stmt) {
        set_error("No statement available for fetching");
        return false;
    }
    
    clear_error();
    
    // Fetch next row
    if (!_current_row.fetch_from_stmt(_stmt)) {
        _state = QUERY_STATE_FINISHED;
        return false;
    }
    
    return true;
}

Row* Query::fetch() {
    if (next()) {
        return &_current_row;
    }
    return nullptr;
}

void Query::bind(const std::string& name, const std::string& value, int size) {
    // For compatibility - replace placeholder in SQL
    // This is a simplified implementation
    std::string placeholder = name;
    if (placeholder[0] != ':') {
        placeholder = ":" + placeholder;
    }

    std::string sql_str = _sql_stream.str();
    size_t pos = sql_str.find(placeholder);
    if (pos != std::string::npos) {
        log_debug_step("Bind found placeholder", (placeholder + " at position " + std::to_string(pos)).c_str());
        sql_str.replace(pos, placeholder.length(), "'" + value + "'");
        _sql_stream.str("");
        _sql_stream.clear();
        _sql_stream << sql_str;
        // Update _sql as well for immediate access via get_sql()
        _sql = sql_str;
        log_debug_step("Bind SQL updated", ("New length: " + std::to_string(sql_str.length())).c_str());
    } else {
        log_debug_step("Bind placeholder NOT found", (placeholder + " in SQL length " + std::to_string(sql_str.length())).c_str());
    }

    log_debug_step("Bind variable", (placeholder + " = " + value).c_str());
}



void Query::bind(const std::string& name, char* value, int size) {
    std::string placeholder = name;
    if (placeholder[0] != ':') {
        placeholder = ":" + placeholder;
    }

    std::string sql_str = _sql_stream.str();
    size_t pos = sql_str.find(placeholder);
    if (pos != std::string::npos) {
        std::string str_value = value ? std::string(value) : "";
        sql_str.replace(pos, placeholder.length(), "'" + str_value + "'");
        _sql_stream.str("");
        _sql_stream.clear();
        _sql_stream << sql_str;
        // Update _sql as well for immediate access via get_sql()
        _sql = sql_str;
    }

    log_debug_step("Bind variable", (placeholder + " = " + (value ? std::string(value) : "NULL")).c_str());
}

void Query::bind(const std::string& name, int value) {
    std::string placeholder = name;
    if (placeholder[0] != ':') {
        placeholder = ":" + placeholder;
    }

    std::string sql_str = _sql_stream.str();
    size_t pos = sql_str.find(placeholder);
    if (pos != std::string::npos) {
        sql_str.replace(pos, placeholder.length(), std::to_string(value));
        _sql_stream.str("");
        _sql_stream.clear();
        _sql_stream << sql_str;
        // Update _sql as well for immediate access via get_sql()
        _sql = sql_str;
    }

    log_debug_step("Bind variable", (placeholder + " = " + std::to_string(value)).c_str());
}

void Query::bind(const std::string& name, long value) {
    std::string placeholder = name;
    if (placeholder[0] != ':') {
        placeholder = ":" + placeholder;
    }

    std::string sql_str = _sql_stream.str();
    size_t pos = sql_str.find(placeholder);
    if (pos != std::string::npos) {
        sql_str.replace(pos, placeholder.length(), std::to_string(value));
        _sql_stream.str("");
        _sql_stream.clear();
        _sql_stream << sql_str;
        // Update _sql as well for immediate access via get_sql()
        _sql = sql_str;
    }

    log_debug_step("Bind variable", (placeholder + " = " + std::to_string(value)).c_str());
}

void Query::bind(const std::string& name, double value) {
    std::string placeholder = name;
    if (placeholder[0] != ':') {
        placeholder = ":" + placeholder;
    }

    std::string sql_str = _sql_stream.str();
    size_t pos = sql_str.find(placeholder);
    if (pos != std::string::npos) {
        sql_str.replace(pos, placeholder.length(), std::to_string(value));
        _sql_stream.str("");
        _sql_stream.clear();
        _sql_stream << sql_str;
        // Update _sql as well for immediate access via get_sql()
        _sql = sql_str;
    }

    log_debug_step("Bind variable", (placeholder + " = " + std::to_string(value)).c_str());
}

bool Query::commit() {
    if (!_connection) {
        set_error("No connection available");
        return false;
    }
    
    return _connection->commit();
}

bool Query::rollback() {
    if (!_connection) {
        set_error("No connection available");
        return false;
    }
    
    return _connection->rollback();
}

bool Query::prepare() {
    return prepare_statement();
}

bool Query::reset() {
    clear_error();
    _state = QUERY_STATE_INIT;
    _rows_affected = 0;
    _has_results = false;
    _current_row.clear();
    
    // Keep the SQL and statement, just reset state
    return true;
}

void Query::clear() {
    if (_stmt) {
        dpiStmt_release(_stmt);
        _stmt = nullptr;
    }
    
    _sql_stream.str("");
    _sql_stream.clear();
    _sql.clear();
    _current_row.clear();
    _state = QUERY_STATE_INIT;
    _rows_affected = 0;
    _has_results = false;
    clear_error();
}

bool Query::prepare_statement() {
    if (!_connection || !_connection->is_connected()) {
        set_error("Connection not available");
        return false;
    }
    
    dpiConn* conn = get_dpi_connection();
    if (!conn) {
        set_error("Failed to get DPI connection");
        return false;
    }
    
    // Release existing statement
    if (_stmt) {
        dpiStmt_release(_stmt);
        _stmt = nullptr;
    }
    
    // Prepare new statement
    if (dpiConn_prepareStmt(conn, 0, _sql.c_str(), _sql.length(), nullptr, 0, &_stmt) != DPI_SUCCESS) {
        set_error_from_dpi("dpiConn_prepareStmt");
        return false;
    }
    
    log_debug_step("Statement prepared", _sql.c_str());
    return true;
}

bool Query::execute_statement() {
    if (!_stmt) {
        set_error("Statement not prepared");
        return false;
    }
    
    // Execute the statement
    uint32_t numQueryColumns = 0;
    if (dpiStmt_execute(_stmt, DPI_MODE_EXEC_DEFAULT, &numQueryColumns) != DPI_SUCCESS) {
        set_error_from_dpi("dpiStmt_execute");
        return false;
    }
    
    // Check if this is a query (has result columns)
    _has_results = (numQueryColumns > 0);
    
    // Get number of affected rows for DML statements
    if (!_has_results) {
        uint64_t rowCount;
        if (dpiStmt_getRowCount(_stmt, &rowCount) == DPI_SUCCESS) {
            _rows_affected = static_cast<unsigned>(rowCount);
        }
    }
    
    log_debug_step("Statement executed", 
                   (_has_results ? "Query with results" : ("DML, rows affected: " + std::to_string(_rows_affected))).c_str());
    
    return true;
}

bool Query::setup_fetch_variables() {
    if (!_stmt || !_has_results) {
        return true; // Nothing to setup
    }
    
    // Initialize row for fetching
    if (!_current_row.init_from_stmt(_stmt)) {
        set_error("Failed to initialize row for fetching");
        return false;
    }
    
    log_debug_step("Fetch variables setup", ("Columns: " + std::to_string(_current_row.field_count())).c_str());
    return true;
}

void Query::set_error(const std::string& message) {
    _error.set_error(-1, message);
    log_error(message.c_str());
}

void Query::set_error_from_dpi(const char* operation) {
    dpiContext* context = get_dpi_context();
    if (context) {
        dpiErrorInfo errorInfo;
        dpiContext_getError(context, &errorInfo);
        _error.set_from_dpi_error(&errorInfo);
    } else {
        _error.set_error(-1, std::string("ODPI-C operation failed: ") + (operation ? operation : "unknown"));
    }
}

void Query::clear_error() {
    _error.clear();
}

dpiContext* Query::get_dpi_context() {
    return _connection ? _connection->get_dpi_context() : nullptr;
}

dpiConn* Query::get_dpi_connection() {
    return _connection ? _connection->get_dpi_connection() : nullptr;
}

}
