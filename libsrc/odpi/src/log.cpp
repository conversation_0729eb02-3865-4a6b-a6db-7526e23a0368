#include "log.hh"
#include <cstdio>
#include <cstring>
#include <ctime>

namespace ORAPP {

// Global log function pointer
static log_func_t g_log_func = nullptr;

void log_to(log_func_t func) {
    g_log_func = func;
}

log_func_t get_log_func() {
    return g_log_func;
}

void log_message(const char *message) {
    if (g_log_func && message) {
        char buffer[1024];
        snprintf(buffer, sizeof(buffer), "[ODPI-INFO] %s", message);
        g_log_func(buffer);
    }
}

void log_error(const char *error) {
    if (g_log_func && error) {
        char buffer[1024];
        snprintf(buffer, sizeof(buffer), "[ODPI-ERROR] %s", error);
        g_log_func(buffer);
    }
}

void log_debug(const char *debug) {
    if (g_log_func && debug) {
        char buffer[1024];
        snprintf(buffer, sizeof(buffer), "[ODPI-DEBUG] %s", debug);
        g_log_func(buffer);
    }
}

void log_debug_step(const char* step, const char* details) {
    if (g_log_func && step) {
        char buffer[1024];
        if (details) {
            snprintf(buffer, sizeof(buffer), "[ODPI-STEP] %s: %s", step, details);
        } else {
            snprintf(buffer, sizeof(buffer), "[ODPI-STEP] %s", step);
        }
        g_log_func(buffer);
    }
}

void log_memory_info(const char* operation, void* ptr) {
    if (g_log_func && operation) {
        char buffer[1024];
        if (ptr) {
            snprintf(buffer, sizeof(buffer), "[ODPI-MEMORY] %s: ptr=%p", operation, ptr);
        } else {
            snprintf(buffer, sizeof(buffer), "[ODPI-MEMORY] %s", operation);
        }
        g_log_func(buffer);
    }
}

void log_odpi_operation(const char* operation, const char* status) {
    if (g_log_func && operation && status) {
        char buffer[1024];
        snprintf(buffer, sizeof(buffer), "[ODPI-OP] %s: %s", operation, status);
        g_log_func(buffer);
    }
}

void log_odpi_error(const char* operation, int error_code, const char* error_message) {
    if (g_log_func && operation) {
        char buffer[1024];
        if (error_message) {
            snprintf(buffer, sizeof(buffer), "[ODPI-ERROR] %s failed: code=%d, message=%s", 
                    operation, error_code, error_message);
        } else {
            snprintf(buffer, sizeof(buffer), "[ODPI-ERROR] %s failed: code=%d", 
                    operation, error_code);
        }
        g_log_func(buffer);
    }
}

void log_connection_info(const char* user, const char* tns, const char* status) {
    if (g_log_func && user && tns && status) {
        char buffer[1024];
        snprintf(buffer, sizeof(buffer), "[ODPI-CONN] %s@%s: %s", user, tns, status);
        g_log_func(buffer);
    }
}

void log_query_info(const char* sql, const char* status) {
    if (g_log_func && sql && status) {
        char buffer[2048];
        // Truncate SQL if too long
        if (strlen(sql) > 500) {
            snprintf(buffer, sizeof(buffer), "[ODPI-QUERY] %.500s... : %s", sql, status);
        } else {
            snprintf(buffer, sizeof(buffer), "[ODPI-QUERY] %s : %s", sql, status);
        }
        g_log_func(buffer);
    }
}

}
