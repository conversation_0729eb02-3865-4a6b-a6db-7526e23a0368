#include "error.hh"
#include "log.hh"
#include <cstring>
#include <sstream>

namespace ORAPP {

// Global error storage
static Error g_last_error;

Error::Error() : _code(0), _message("") {
}

Error::Error(int code, const std::string& message) : _code(code), _message(message) {
}

Error::Error(const dpiErrorInfo* errorInfo) : _code(0), _message("") {
    if (errorInfo) {
        set_from_dpi_error(errorInfo);
    }
}

Error::~Error() {
}

Error::Error(const Error& other) : _code(other._code), _message(other._message) {
}

Error& Error::operator=(const Error& other) {
    if (this != &other) {
        _code = other._code;
        _message = other._message;
    }
    return *this;
}

void Error::clear() {
    _code = 0;
    _message.clear();
}

void Error::set_from_dpi_error(const dpiErrorInfo* errorInfo) {
    if (errorInfo) {
        _code = errorInfo->code;
        _message = std::string(errorInfo->message, errorInfo->messageLength);
        
        // Log the error
        log_odpi_error("DPI Operation", _code, _message.c_str());
    } else {
        _code = -1;
        _message = "Unknown ODPI-C error";
    }
}

void Error::set_error(int code, const std::string& message) {
    _code = code;
    _message = message;
    
    // Log the error
    log_error(message.c_str());
}

std::string Error::to_string() const {
    if (_code == 0) {
        return "No error";
    }
    
    std::ostringstream oss;
    oss << "Error " << _code << ": " << _message;
    return oss.str();
}

// Global error handling functions
void set_last_error(const Error& error) {
    g_last_error = error;
}

Error get_last_error() {
    return g_last_error;
}

void clear_last_error() {
    g_last_error.clear();
}

// Helper functions for ODPI-C error handling
bool check_dpi_result(int result, dpiContext* context, const char* operation) {
    if (result == DPI_SUCCESS) {
        return true;
    }
    
    dpiErrorInfo errorInfo;
    if (context) {
        dpiContext_getError(context, &errorInfo);
        Error error(&errorInfo);
        set_last_error(error);

        if (operation) {
            log_odpi_error(operation, errorInfo.code, errorInfo.message);
        }
    } else {
        Error error(-1, operation ? std::string("ODPI-C operation failed: ") + operation : "ODPI-C operation failed");
        set_last_error(error);
    }
    
    return false;
}

std::string format_dpi_error(const dpiErrorInfo* errorInfo) {
    if (!errorInfo) {
        return "Unknown ODPI-C error";
    }
    
    std::ostringstream oss;
    oss << "DPI-" << errorInfo->code << ": ";
    oss << std::string(errorInfo->message, errorInfo->messageLength);
    
    if (errorInfo->fnName && strlen(errorInfo->fnName) > 0) {
        oss << " (in " << errorInfo->fnName << ")";
    }
    
    if (errorInfo->action && strlen(errorInfo->action) > 0) {
        oss << " [" << errorInfo->action << "]";
    }
    
    return oss.str();
}

}
