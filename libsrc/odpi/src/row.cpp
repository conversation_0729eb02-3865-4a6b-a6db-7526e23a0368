#include "row.hh"
#include "log.hh"
#include <stdexcept>
#include <algorithm>

namespace ORAPP {

Row::Row() : _initialized(false) {
}

Row::Row(dpiStmt* stmt) : _initialized(false) {
    init_from_stmt(stmt);
}

Row::~Row() {
}

Row::Row(const Row& other) 
    : _fields(other._fields), _field_names(other._field_names),
      _dpi_data(other._dpi_data), _query_info(other._query_info),
      _initialized(other._initialized) {
}

Row& Row::operator=(const Row& other) {
    if (this != &other) {
        _fields = other._fields;
        _field_names = other._field_names;
        _dpi_data = other._dpi_data;
        _query_info = other._query_info;
        _initialized = other._initialized;
    }
    return *this;
}

Field& Row::operator[](int index) {
    if (index < 0 || index >= static_cast<int>(_fields.size())) {
        throw std::out_of_range("Field index out of range");
    }
    return _fields[index];
}

const Field& Row::operator[](int index) const {
    if (index < 0 || index >= static_cast<int>(_fields.size())) {
        throw std::out_of_range("Field index out of range");
    }
    return _fields[index];
}

Field& Row::operator[](const std::string& name) {
    int index = find_field_index(name);
    if (index < 0) {
        throw std::out_of_range("Field name not found: " + name);
    }
    return _fields[index];
}

const Field& Row::operator[](const std::string& name) const {
    int index = find_field_index(name);
    if (index < 0) {
        throw std::out_of_range("Field name not found: " + name);
    }
    return _fields[index];
}

int Row::field_index(const std::string& name) const {
    return find_field_index(name);
}

std::string Row::field_name(int index) const {
    if (index < 0 || index >= static_cast<int>(_field_names.size())) {
        return "";
    }
    return _field_names[index];
}

const char* Row::name(int index) const {
    if (index < 0 || index >= static_cast<int>(_field_names.size())) {
        return "";
    }
    return _field_names[index].c_str();
}

bool Row::init_from_stmt(dpiStmt* stmt) {
    if (!stmt) {
        log_error("Row::init_from_stmt: null statement");
        return false;
    }
    
    clear();
    
    try {
        setup_field_definitions(stmt);
        _initialized = true;
        log_debug_step("Row initialized", ("Field count: " + std::to_string(_fields.size())).c_str());
        return true;
    } catch (const std::exception& e) {
        log_error(("Row::init_from_stmt failed: " + std::string(e.what())).c_str());
        return false;
    }
}

bool Row::fetch_from_stmt(dpiStmt* stmt) {
    if (!stmt || !_initialized) {
        log_error("Row::fetch_from_stmt: statement not initialized");
        return false;
    }

    try {
        // Fetch next row
        int found;
        uint32_t bufferRowIndex;

        if (dpiStmt_fetch(stmt, &found, &bufferRowIndex) != DPI_SUCCESS) {
            log_debug("Row::fetch_from_stmt: fetch failed or no more rows");
            return false;
        }

        if (!found) {
            log_debug("Row::fetch_from_stmt: no data found");
            return false;
        }

        // Get column values for this row
        for (uint32_t i = 0; i < _fields.size(); ++i) {
            uint32_t nativeTypeNum;
            dpiData* data;

            if (dpiStmt_getQueryValue(stmt, i + 1, &nativeTypeNum, &data) == DPI_SUCCESS) {
                _fields[i].set_from_dpi_data(data, &_query_info[i]);
            } else {
                log_error(("Failed to get value for column " + std::to_string(i + 1)).c_str());
                _fields[i].clear();
            }
        }

        log_debug_step("Row fetched", ("Fields updated: " + std::to_string(_fields.size())).c_str());
        return true;

    } catch (const std::exception& e) {
        log_error(("Row::fetch_from_stmt failed: " + std::string(e.what())).c_str());
        return false;
    }
}

void Row::clear() {
    _fields.clear();
    _field_names.clear();
    _dpi_data.clear();
    _query_info.clear();
    _initialized = false;
}

void Row::reset() {
    // Reset field values but keep structure
    for (auto& field : _fields) {
        field.clear();
    }
}

void Row::setup_field_definitions(dpiStmt* stmt) {
    uint32_t numQueryColumns;
    if (dpiStmt_getNumQueryColumns(stmt, &numQueryColumns) != DPI_SUCCESS) {
        throw std::runtime_error("Failed to get number of query columns");
    }
    
    if (numQueryColumns == 0) {
        log_debug("Row::setup_field_definitions: no columns in result set");
        return;
    }
    
    // Resize our containers
    _fields.resize(numQueryColumns);
    _field_names.resize(numQueryColumns);
    _dpi_data.resize(numQueryColumns);
    _query_info.resize(numQueryColumns);
    
    // Get column information and define variables
    for (uint32_t i = 0; i < numQueryColumns; ++i) {
        if (dpiStmt_getQueryInfo(stmt, i + 1, &_query_info[i]) != DPI_SUCCESS) {
            throw std::runtime_error("Failed to get query info for column " + std::to_string(i + 1));
        }
        
        // Store field name
        _field_names[i] = std::string(_query_info[i].name, _query_info[i].nameLength);
        
        // For now, we'll handle the variable creation differently
        // This will be implemented when we actually fetch data
    }
}

void Row::update_fields_from_dpi_data() {
    for (size_t i = 0; i < _fields.size(); ++i) {
        _fields[i].set_from_dpi_data(&_dpi_data[i], &_query_info[i]);
    }
}

int Row::find_field_index(const std::string& name) const {
    auto it = std::find(_field_names.begin(), _field_names.end(), name);
    if (it != _field_names.end()) {
        return static_cast<int>(it - _field_names.begin());
    }
    return -1;
}

}
