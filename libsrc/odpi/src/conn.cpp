#include "conn.hh"
#include "log.hh"
#include <cstring>

namespace ORAPP {

Connection::Connection() 
    : _context(nullptr), _connection(nullptr), _user(""), _password(""), 
      _tns(""), _version(""), _state(CONN_STATE_DISCONNECTED), 
      _autocommit(true), _cached_query(nullptr) {
    
    // Initialize parameters
    memset(&_common_params, 0, sizeof(_common_params));
    memset(&_conn_params, 0, sizeof(_conn_params));
}

Connection::~Connection() {
    disconnect();
    cleanup();
}

bool Connection::connect(const std::string& tns, const std::string& user, const std::string& password) {
    log_debug_step("Connection::connect", ("Connecting to " + user + "@" + tns).c_str());
    
    // Disconnect if already connected
    if (_state == CONN_STATE_CONNECTED) {
        disconnect();
    }
    
    _user = user;
    _password = password;
    _tns = tns;
    
    clear_error();
    
    // Initialize ODPI-C context
    if (!initialize_context()) {
        _state = CONN_STATE_ERROR;
        return false;
    }
    
    // Create connection
    if (!create_connection()) {
        _state = CONN_STATE_ERROR;
        return false;
    }
    
    // Update version information
    if (!update_version()) {
        log_error("Failed to get database version, but connection succeeded");
    }
    
    _state = CONN_STATE_CONNECTED;
    log_connection_info(_user.c_str(), _tns.c_str(), "CONNECTED");
    log_message(("Successfully connected to Oracle database, version: " + _version).c_str());
    
    return true;
}

bool Connection::disconnect() {
    if (_state != CONN_STATE_CONNECTED) {
        return true;
    }
    
    log_debug_step("Connection::disconnect", "Disconnecting from database");
    
    clear_error();
    
    // Clear cached query
    _cached_query.reset();
    
    // Close connection
    if (_connection) {
        if (dpiConn_close(_connection, DPI_MODE_CONN_CLOSE_DEFAULT, nullptr, 0) != DPI_SUCCESS) {
            set_error_from_dpi("dpiConn_close");
            // Continue with cleanup even if close failed
        }
        
        if (dpiConn_release(_connection) != DPI_SUCCESS) {
            set_error_from_dpi("dpiConn_release");
        }
        
        _connection = nullptr;
    }
    
    _state = CONN_STATE_DISCONNECTED;
    log_connection_info(_user.c_str(), _tns.c_str(), "DISCONNECTED");
    
    return !has_error();
}

Query* Connection::query() {
    if (_state != CONN_STATE_CONNECTED) {
        set_error("Connection not established");
        return nullptr;
    }
    
    // Return cached query if available
    if (_cached_query) {
        _cached_query->clear();
        return _cached_query.get();
    }
    
    // Create new query (C++11 compatible)
    _cached_query.reset(new Query(this));
    return _cached_query.get();
}

Query* Connection::query(const std::string& sql) {
    Query* q = query();
    if (q) {
        *q << sql;
    }
    return q;
}

bool Connection::execute(const std::string& sql) {
    Query* q = query(sql);
    return q ? q->execute() : false;
}

bool Connection::commit() {
    if (_state != CONN_STATE_CONNECTED) {
        set_error("Connection not established");
        return false;
    }
    
    clear_error();
    
    if (dpiConn_commit(_connection) != DPI_SUCCESS) {
        set_error_from_dpi("dpiConn_commit");
        return false;
    }
    
    log_debug_step("Transaction committed", nullptr);
    return true;
}

bool Connection::rollback() {
    if (_state != CONN_STATE_CONNECTED) {
        set_error("Connection not established");
        return false;
    }
    
    clear_error();
    
    if (dpiConn_rollback(_connection) != DPI_SUCCESS) {
        set_error_from_dpi("dpiConn_rollback");
        return false;
    }
    
    log_debug_step("Transaction rolled back", nullptr);
    return true;
}

bool Connection::autocommit(bool enable) {
    _autocommit = enable;
    
    // Note: ODPI-C doesn't have a direct setAutocommit function
    // Autocommit is controlled during connection creation and transaction operations
    if (_state == CONN_STATE_CONNECTED) {
        log_debug_step("Autocommit", enable ? "enabled" : "disabled");
    }
    
    return true;
}

bool Connection::set_client_identifier(const std::string& identifier) {
    if (_state != CONN_STATE_CONNECTED) {
        set_error("Connection not established");
        return false;
    }
    
    clear_error();
    
    if (dpiConn_setClientIdentifier(_connection, identifier.c_str(), identifier.length()) != DPI_SUCCESS) {
        set_error_from_dpi("dpiConn_setClientIdentifier");
        return false;
    }
    
    log_debug_step("Client identifier set", identifier.c_str());
    return true;
}

bool Connection::set_module(const std::string& module) {
    if (_state != CONN_STATE_CONNECTED) {
        set_error("Connection not established");
        return false;
    }
    
    clear_error();
    
    if (dpiConn_setModule(_connection, module.c_str(), module.length()) != DPI_SUCCESS) {
        set_error_from_dpi("dpiConn_setModule");
        return false;
    }
    
    log_debug_step("Module set", module.c_str());
    return true;
}

bool Connection::set_action(const std::string& action) {
    if (_state != CONN_STATE_CONNECTED) {
        set_error("Connection not established");
        return false;
    }
    
    clear_error();
    
    if (dpiConn_setAction(_connection, action.c_str(), action.length()) != DPI_SUCCESS) {
        set_error_from_dpi("dpiConn_setAction");
        return false;
    }
    
    log_debug_step("Action set", action.c_str());
    return true;
}

bool Connection::initialize_context() {
    log_debug_step("Initializing ODPI-C context", nullptr);

    dpiErrorInfo errorInfo;

    // Create context first
    log_debug_step("Creating ODPI-C context", ("Version: " + std::to_string(DPI_MAJOR_VERSION) + "." + std::to_string(DPI_MINOR_VERSION)).c_str());
    if (dpiContext_create(DPI_MAJOR_VERSION, DPI_MINOR_VERSION, &_context, &errorInfo) != DPI_SUCCESS) {
        log_error(("dpiContext_create failed: " + format_dpi_error(&errorInfo)).c_str());
        _error.set_from_dpi_error(&errorInfo);
        return false;
    }

    // Initialize common parameters with valid context
    log_debug_step("Initializing common create params", nullptr);
    if (dpiContext_initCommonCreateParams(_context, &_common_params) != DPI_SUCCESS) {
        set_error_from_dpi("dpiContext_initCommonCreateParams");
        return false;
    }
    
    log_memory_info("ODPI-C context created", _context);
    return true;
}

bool Connection::create_connection() {
    log_debug_step("Creating ODPI-C connection", nullptr);
    
    // Initialize connection parameters
    if (dpiContext_initConnCreateParams(_context, &_conn_params) != DPI_SUCCESS) {
        set_error_from_dpi("dpiContext_initConnCreateParams");
        return false;
    }
    
    // Note: Autocommit is handled differently in ODPI-C
    // It's controlled through transaction operations
    
    // Create connection
    if (dpiConn_create(_context, _user.c_str(), _user.length(),
                      _password.c_str(), _password.length(),
                      _tns.c_str(), _tns.length(),
                      &_common_params, &_conn_params, &_connection) != DPI_SUCCESS) {
        set_error_from_dpi("dpiConn_create");
        return false;
    }
    
    log_memory_info("ODPI-C connection created", _connection);
    return true;
}

bool Connection::update_version() {
    if (!_connection) {
        return false;
    }
    
    dpiVersionInfo versionInfo;
    if (dpiConn_getServerVersion(_connection, nullptr, 0, &versionInfo) != DPI_SUCCESS) {
        set_error_from_dpi("dpiConn_getServerVersion");
        return false;
    }
    
    char version_buffer[64];
    snprintf(version_buffer, sizeof(version_buffer), "%d.%d.%d.%d.%d",
             versionInfo.versionNum, versionInfo.releaseNum,
             versionInfo.updateNum, versionInfo.portReleaseNum,
             versionInfo.portUpdateNum);
    
    _version = std::string(version_buffer);
    log_debug_step("Database version retrieved", _version.c_str());
    
    return true;
}

void Connection::cleanup() {
    if (_context) {
        dpiContext_destroy(_context);
        _context = nullptr;
        log_memory_info("ODPI-C context destroyed", nullptr);
    }
}

void Connection::set_error(const std::string& message) {
    _error.set_error(-1, message);
}

void Connection::set_error_from_dpi(const char* operation) {
    if (_context) {
        dpiErrorInfo errorInfo;
        dpiContext_getError(_context, &errorInfo);
        _error.set_from_dpi_error(&errorInfo);
    } else {
        _error.set_error(-1, std::string("ODPI-C operation failed: ") + (operation ? operation : "unknown"));
    }
}

void Connection::clear_error() {
    _error.clear();
}

}
