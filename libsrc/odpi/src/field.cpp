#include "field.hh"
#include "log.hh"
#include <cstdio>
#include <cstring>
#include <sstream>
#include <iomanip>
#include <cstdlib>

namespace ORAPP {

Field::Field() 
    : _type(FIELD_TYPE_UNKNOWN), _name(""), _value(""), _is_null(true),
      _raw_data(nullptr), _raw_data_length(0) {
}

Field::Field(const dpiData* data, const dpiQueryInfo* queryInfo) 
    : _type(FIELD_TYPE_UNKNOWN), _name(""), _value(""), _is_null(true),
      _raw_data(nullptr), _raw_data_length(0) {
    set_from_dpi_data(data, queryInfo);
}

Field::~Field() {
}

Field::Field(const Field& other) 
    : _type(other._type), _name(other._name), _value(other._value), 
      _is_null(other._is_null), _raw_data(other._raw_data), 
      _raw_data_length(other._raw_data_length) {
}

Field& Field::operator=(const Field& other) {
    if (this != &other) {
        _type = other._type;
        _name = other._name;
        _value = other._value;
        _is_null = other._is_null;
        _raw_data = other._raw_data;
        _raw_data_length = other._raw_data_length;
    }
    return *this;
}

int Field::as_int() const {
    if (_is_null) return 0;
    return std::atoi(_value.c_str());
}

long Field::as_long() const {
    if (_is_null) return 0L;
    return std::atol(_value.c_str());
}

double Field::as_double() const {
    if (_is_null) return 0.0;
    return std::atof(_value.c_str());
}

float Field::as_float() const {
    if (_is_null) return 0.0f;
    return static_cast<float>(std::atof(_value.c_str()));
}

bool Field::as_bool() const {
    if (_is_null) return false;
    return (_value == "1" || _value == "true" || _value == "TRUE" || _value == "Y");
}

std::string Field::as_date_string() const {
    return _value; // Already formatted as string
}

std::string Field::as_timestamp_string() const {
    return _value; // Already formatted as string
}

void Field::set_from_dpi_data(const dpiData* data, const dpiQueryInfo* queryInfo) {
    clear();
    
    if (!data || !queryInfo) {
        return;
    }
    
    // Set field name
    _name = std::string(queryInfo->name, queryInfo->nameLength);
    
    // Set field type
    _type = convert_dpi_type_to_field_type(queryInfo->typeInfo.oracleTypeNum);
    
    // Check if null
    _is_null = data->isNull;
    
    if (!_is_null) {
        convert_dpi_data_to_string(data, queryInfo->typeInfo.defaultNativeTypeNum);
    }
}

void Field::clear() {
    _type = FIELD_TYPE_UNKNOWN;
    _name.clear();
    _value.clear();
    _is_null = true;
    _raw_data = nullptr;
    _raw_data_length = 0;
}

void Field::convert_dpi_data_to_string(const dpiData* data, dpiNativeTypeNum nativeType) {
    std::ostringstream oss;
    
    switch (nativeType) {
        case DPI_NATIVE_TYPE_INT64:
            oss << data->value.asInt64;
            break;
            
        case DPI_NATIVE_TYPE_UINT64:
            oss << data->value.asUint64;
            break;
            
        case DPI_NATIVE_TYPE_FLOAT:
            oss << std::fixed << std::setprecision(6) << data->value.asFloat;
            break;
            
        case DPI_NATIVE_TYPE_DOUBLE:
            oss << std::fixed << std::setprecision(15) << data->value.asDouble;
            break;
            
        case DPI_NATIVE_TYPE_BYTES:
            if (data->value.asBytes.ptr && data->value.asBytes.length > 0) {
                _value = std::string(data->value.asBytes.ptr, data->value.asBytes.length);
                _raw_data = data->value.asBytes.ptr;
                _raw_data_length = data->value.asBytes.length;
                return; // Don't use oss for bytes
            }
            break;
            
        case DPI_NATIVE_TYPE_TIMESTAMP:
            _value = format_timestamp(&data->value.asTimestamp);
            return;
            
        case DPI_NATIVE_TYPE_INTERVAL_DS:
            _value = format_interval_ds(&data->value.asIntervalDS);
            return;
            
        case DPI_NATIVE_TYPE_INTERVAL_YM:
            _value = format_interval_ym(&data->value.asIntervalYM);
            return;
            
        case DPI_NATIVE_TYPE_BOOLEAN:
            oss << (data->value.asBoolean ? "1" : "0");
            break;
            
        default:
            oss << "[Unsupported type: " << nativeType << "]";
            break;
    }
    
    _value = oss.str();
}

FieldType Field::convert_dpi_type_to_field_type(dpiOracleTypeNum oracleType) {
    switch (oracleType) {
        case DPI_ORACLE_TYPE_VARCHAR:
        case DPI_ORACLE_TYPE_NVARCHAR:
        case DPI_ORACLE_TYPE_CHAR:
        case DPI_ORACLE_TYPE_NCHAR:
            return FIELD_TYPE_VARCHAR;
            
        case DPI_ORACLE_TYPE_NUMBER:
            return FIELD_TYPE_NUMBER;
            
        case DPI_ORACLE_TYPE_DATE:
            return FIELD_TYPE_DATE;
            
        case DPI_ORACLE_TYPE_TIMESTAMP:
        case DPI_ORACLE_TYPE_TIMESTAMP_TZ:
        case DPI_ORACLE_TYPE_TIMESTAMP_LTZ:
            return FIELD_TYPE_TIMESTAMP;
            
        case DPI_ORACLE_TYPE_INTERVAL_DS:
        case DPI_ORACLE_TYPE_INTERVAL_YM:
            return FIELD_TYPE_INTERVAL;
            
        case DPI_ORACLE_TYPE_CLOB:
        case DPI_ORACLE_TYPE_NCLOB:
            return FIELD_TYPE_CLOB;
            
        case DPI_ORACLE_TYPE_BLOB:
            return FIELD_TYPE_BLOB;
            
        case DPI_ORACLE_TYPE_RAW:
            return FIELD_TYPE_RAW;
            
        case DPI_ORACLE_TYPE_BOOLEAN:
            return FIELD_TYPE_BOOLEAN;
            
        default:
            return FIELD_TYPE_UNKNOWN;
    }
}

std::string Field::format_timestamp(const dpiTimestamp* timestamp) {
    if (!timestamp) return "";
    
    char buffer[64];
    snprintf(buffer, sizeof(buffer), "%04d-%02d-%02d %02d:%02d:%02d.%06d",
             timestamp->year, timestamp->month, timestamp->day,
             timestamp->hour, timestamp->minute, timestamp->second,
             timestamp->fsecond);
    return std::string(buffer);
}

std::string Field::format_interval_ds(const dpiIntervalDS* interval) {
    if (!interval) return "";
    
    char buffer[64];
    snprintf(buffer, sizeof(buffer), "%d %02d:%02d:%02d.%06d",
             interval->days, interval->hours, interval->minutes,
             interval->seconds, interval->fseconds);
    return std::string(buffer);
}

std::string Field::format_interval_ym(const dpiIntervalYM* interval) {
    if (!interval) return "";
    
    char buffer[32];
    snprintf(buffer, sizeof(buffer), "%d-%02d", interval->years, interval->months);
    return std::string(buffer);
}

}
