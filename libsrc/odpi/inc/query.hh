#ifndef ODPI_QUERY_HH
#define ODPI_QUERY_HH

/*
 * ODPI-C Wrapper Query - Compatible with ORAPP interface
 */

#include <string>
#include <sstream>
#include "constants.hh"
#include "error.hh"
#include "row.hh"
#include "dpi.h"

namespace ORAPP {

class Connection; // Forward declaration

class Query {
public:
    Query(Connection* conn);
    ~Query();

    // Disable copy constructor and assignment operator
    Query(const Query&) = delete;
    Query& operator=(const Query&) = delete;

    // SQL building
    Query& operator<<(const std::string& sql);
    Query& operator<<(const char* sql);
    Query& operator<<(int value);
    Query& operator<<(long value);
    Query& operator<<(double value);
    Query& operator<<(float value);

    // Query execution
    bool execute();
    bool execute(const std::string& sql);
    
    // Result fetching
    bool next();
    Row& row() { return _current_row; }
    const Row& row() const { return _current_row; }
    
    // Query information
    unsigned rows() const { return _rows_affected; }
    const std::string& statement() const { return _sql; }
    std::string get_sql() const {
        // Return the most recent SQL (either from _sql or _sql_stream)
        std::string current_sql = _sql_stream.str();
        if (!current_sql.empty()) {
            return current_sql;
        }
        return _sql;
    }
    QueryState state() const { return _state; }

    // Fetch methods for compatibility
    Row* fetch();

    // Bind methods for compatibility (simplified implementation)
    void bind(const std::string& name, const std::string& value, int size = 0);
    void bind(const std::string& name, int value);
    void bind(const std::string& name, long value);
    void bind(const std::string& name, double value);
    void bind(const std::string& name, char* value, int size);
    
    // Error handling
    std::string error() const { return _error.message(); }
    bool has_error() const { return _error.is_error(); }
    
    // Transaction control
    bool commit();
    bool rollback();
    
    // Query control
    bool prepare();
    bool reset();
    void clear();
    
    // Autocommit control
    void autocommit(bool enable = true) { _autocommit = enable; }
    bool is_autocommit() const { return _autocommit; }

private:
    Connection* _connection;
    dpiStmt* _stmt;
    std::ostringstream _sql_stream;
    std::string _sql;
    Row _current_row;
    QueryState _state;
    Error _error;
    unsigned _rows_affected;
    bool _autocommit;
    bool _has_results;
    
    // Helper methods
    bool prepare_statement();
    bool execute_statement();
    bool setup_fetch_variables();
    void set_error(const std::string& message);
    void set_error_from_dpi(const char* operation);
    void clear_error();
    
    // Get ODPI-C context and connection from parent
    dpiContext* get_dpi_context();
    dpiConn* get_dpi_connection();
};

}

#endif
