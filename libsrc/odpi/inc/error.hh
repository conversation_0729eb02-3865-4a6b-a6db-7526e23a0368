#ifndef ODPI_ERROR_HH
#define ODPI_ERROR_HH

/*
 * ODPI-C Wrapper <PERSON><PERSON><PERSON> Handling - Compatible with ORAPP interface
 */

#include <string>
#include "dpi.h"

namespace ORAPP {

class Error {
public:
    Error();
    Error(int code, const std::string& message);
    Error(const dpiErrorInfo* errorInfo);
    ~Error();

    // Copy constructor and assignment operator
    <PERSON><PERSON>r(const Error& other);
    Error& operator=(const Error& other);

    // Accessors
    int code() const { return _code; }
    const std::string& message() const { return _message; }
    bool is_error() const { return _code != 0; }
    
    // Clear error
    void clear();
    
    // Set error from ODPI-C error info
    void set_from_dpi_error(const dpiErrorInfo* errorInfo);
    
    // Set custom error
    void set_error(int code, const std::string& message);
    
    // Convert to string
    std::string to_string() const;

private:
    int _code;
    std::string _message;
};

// Global error handling functions
void set_last_error(const Error& error);
Error get_last_error();
void clear_last_error();

// Helper functions for ODPI-C error handling
bool check_dpi_result(int result, dpiContext* context, const char* operation);
std::string format_dpi_error(const dpiErrorInfo* errorInfo);

}

#endif
