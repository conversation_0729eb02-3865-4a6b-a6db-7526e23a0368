#ifndef ODPI_ROW_HH
#define ODPI_ROW_HH

/*
 * ODPI-C Wrapper Row - Compatible with ORAPP interface
 */

#include <vector>
#include <string>
#include "field.hh"
#include "dpi.h"

namespace ORAPP {

class Row {
public:
    Row();
    Row(dpiStmt* stmt);
    ~Row();

    // Copy constructor and assignment operator
    Row(const Row& other);
    Row& operator=(const Row& other);

    // Field access
    Field& operator[](int index);
    const Field& operator[](int index) const;
    Field& operator[](const std::string& name);
    const Field& operator[](const std::string& name) const;
    
    // Row information
    int field_count() const { return static_cast<int>(_fields.size()); }
    int width() const { return static_cast<int>(_fields.size()); }
    bool is_empty() const { return _fields.empty(); }
    
    // Field name lookup
    int field_index(const std::string& name) const;
    std::string field_name(int index) const;
    const char* name(int index) const;
    
    // Initialize row from ODPI-C statement
    bool init_from_stmt(dpiStmt* stmt);
    
    // Fetch data from ODPI-C statement
    bool fetch_from_stmt(dpiStmt* stmt);
    
    // Clear row data
    void clear();
    
    // Reset for reuse
    void reset();

private:
    std::vector<Field> _fields;
    std::vector<std::string> _field_names;
    std::vector<dpiData> _dpi_data;
    std::vector<dpiQueryInfo> _query_info;
    bool _initialized;
    
    // Helper methods
    void setup_field_definitions(dpiStmt* stmt);
    void update_fields_from_dpi_data();
    int find_field_index(const std::string& name) const;
};

}

#endif
