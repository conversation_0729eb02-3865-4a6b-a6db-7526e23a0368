#ifndef ODPI_CONN_HH
#define ODPI_CONN_HH

/*
 * ODPI-C Wrapper Connection - Compatible with ORAPP interface
 */

#include <string>
#include <memory>
#include "constants.hh"
#include "error.hh"
#include "query.hh"
#include "dpi.h"

namespace ORAPP {

class Connection {
public:
    Connection();
    ~Connection();

    // Disable copy constructor and assignment operator
    Connection(const Connection&) = delete;
    Connection& operator=(const Connection&) = delete;

    // Connection management
    bool connect(const std::string& tns, const std::string& user, const std::string& password);
    bool disconnect();
    bool is_connected() const { return _state == CONN_STATE_CONNECTED; }
    
    // Connection information
    ConnectionState state() const { return _state; }
    const std::string& user() const { return _user; }
    const std::string& tns() const { return _tns; }
    const std::string& version() const { return _version; }
    
    // Error handling
    std::string error() const { return _error.message(); }
    bool has_error() const { return _error.is_error(); }
    
    // Query creation
    Query* query();
    Query* query(const std::string& sql);
    
    // Direct execution
    bool execute(const std::string& sql);
    
    // Transaction control
    bool commit();
    bool rollback();
    bool autocommit(bool enable = true);
    bool is_autocommit() const { return _autocommit; }
    
    // Connection properties
    bool set_client_identifier(const std::string& identifier);
    bool set_module(const std::string& module);
    bool set_action(const std::string& action);
    
    // Internal access for Query class
    dpiContext* get_dpi_context() { return _context; }
    dpiConn* get_dpi_connection() { return _connection; }

private:
    dpiContext* _context;
    dpiConn* _connection;
    std::string _user;
    std::string _password;
    std::string _tns;
    std::string _version;
    ConnectionState _state;
    Error _error;
    bool _autocommit;
    std::unique_ptr<Query> _cached_query;
    
    // Helper methods
    bool initialize_context();
    bool create_connection();
    bool update_version();
    void cleanup();
    void set_error(const std::string& message);
    void set_error_from_dpi(const char* operation);
    void clear_error();
    
    // Connection parameters
    dpiCommonCreateParams _common_params;
    dpiConnCreateParams _conn_params;
};

}

#endif
