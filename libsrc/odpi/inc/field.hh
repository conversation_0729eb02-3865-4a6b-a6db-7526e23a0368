#ifndef ODPI_FIELD_HH
#define ODPI_FIELD_HH

/*
 * ODPI-C Wrapper Field - Compatible with ORAPP interface
 */

#include <string>
#include "constants.hh"
#include "dpi.h"

namespace ORAPP {

class Field {
public:
    Field();
    Field(const dpiData* data, const dpiQueryInfo* queryInfo);
    ~Field();

    // Copy constructor and assignment operator
    Field(const Field& other);
    Field& operator=(const Field& other);

    // Type information
    FieldType type() const { return _type; }
    const std::string& name() const { return _name; }
    bool is_null() const { return _is_null; }
    
    // Value accessors
    const std::string& value() const { return _value; }
    const char* c_str() const { return _value.c_str(); }
    
    // Type-specific accessors
    int as_int() const;
    long as_long() const;
    double as_double() const;
    float as_float() const;
    bool as_bool() const;

    // Type conversion operators for compatibility
    operator int() const { return as_int(); }
    operator long() const { return as_long(); }
    operator double() const { return as_double(); }
    operator float() const { return as_float(); }
    operator const char*() const { return c_str(); }
    operator char*() const { return const_cast<char*>(c_str()); }
    
    // Date/time accessors
    std::string as_date_string() const;
    std::string as_timestamp_string() const;
    
    // Binary data accessors
    const void* as_raw_data() const { return _raw_data; }
    size_t raw_data_length() const { return _raw_data_length; }
    
    // Set field data from ODPI-C
    void set_from_dpi_data(const dpiData* data, const dpiQueryInfo* queryInfo);
    
    // Clear field
    void clear();

private:
    FieldType _type;
    std::string _name;
    std::string _value;
    bool _is_null;
    
    // For binary data
    const void* _raw_data;
    size_t _raw_data_length;
    
    // Helper methods
    void convert_dpi_data_to_string(const dpiData* data, dpiNativeTypeNum nativeType);
    FieldType convert_dpi_type_to_field_type(dpiOracleTypeNum oracleType);
    std::string format_timestamp(const dpiTimestamp* timestamp);
    std::string format_interval_ds(const dpiIntervalDS* interval);
    std::string format_interval_ym(const dpiIntervalYM* interval);
};

}

#endif
