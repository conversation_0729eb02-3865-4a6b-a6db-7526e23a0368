#ifndef ODPI_LOG_HH
#define ODPI_LOG_HH

/*
 * ODPI-C Wrapper Logging - Compatible with ORAPP interface
 */

#include <string>

namespace ORAPP {

// Log function pointer type
typedef void (*log_func_t)(const char *);

// Set log function
void log_to(log_func_t func);

// Internal logging functions
void log_message(const char *message);
void log_error(const char *error);
void log_debug(const char *debug);

// Enhanced debugging functions for ODPI-C operations
void log_debug_step(const char* step, const char* details = nullptr);
void log_memory_info(const char* operation, void* ptr = nullptr);
void log_odpi_operation(const char* operation, const char* status);

// Get current log function
log_func_t get_log_func();

// ODPI-C specific logging
void log_odpi_error(const char* operation, int error_code, const char* error_message);
void log_connection_info(const char* user, const char* tns, const char* status);
void log_query_info(const char* sql, const char* status);

}

#endif
