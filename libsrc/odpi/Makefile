# ODPI-C Wrapper Library Makefile

# Compiler settings
CXX = g++
CC = gcc
CXXFLAGS = -std=c++11 -Wall -Wextra -O2 -fPIC
CFLAGS = -Wall -Wextra -O2 -fPIC

# Directories
SRCDIR = src
INCDIR = inc
LIBDIR = lib
TESTDIR = test
OBJDIR = obj
ODPI_DIR = external/odpi

# Include paths
INCLUDES = -I$(INCDIR) -I$(ODPI_DIR)/include

# Oracle Client settings (adjust paths as needed)
ORACLE_HOME ?= /usr/lib/oracle/21/client64
ORACLE_INCLUDES = -I/usr/include/oracle/21/client64
ORACLE_LIBS = -L$(ORACLE_HOME)/lib -lclntsh

# Source files
WRAPPER_SOURCES = $(wildcard $(SRCDIR)/*.cpp)
ODPI_SOURCES = $(wildcard $(ODPI_DIR)/src/*.c)
TEST_SOURCES = $(wildcard $(TESTDIR)/*.cpp)

# Object files
WRAPPER_OBJECTS = $(WRAPPER_SOURCES:$(SRCDIR)/%.cpp=$(OBJDIR)/%.o)
ODPI_OBJECTS = $(ODPI_SOURCES:$(ODPI_DIR)/src/%.c=$(OBJDIR)/%.o)
TEST_OBJECTS = $(TEST_SOURCES:$(TESTDIR)/%.cpp=$(OBJDIR)/test_%.o)

# Library names
STATIC_LIB = $(LIBDIR)/libodpiwrapper.a
SHARED_LIB = $(LIBDIR)/libodpiwrapper.so

# Test executables
TEST_EXES = $(TEST_SOURCES:$(TESTDIR)/%.cpp=%)

# Default target
all: directories $(STATIC_LIB) $(SHARED_LIB)

# Create directories
directories:
	@mkdir -p $(OBJDIR) $(LIBDIR)

# Build ODPI-C objects
$(OBJDIR)/%.o: $(ODPI_DIR)/src/%.c
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# Build wrapper objects
$(OBJDIR)/%.o: $(SRCDIR)/%.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) $(ORACLE_INCLUDES) -c $< -o $@

# Build test objects
$(OBJDIR)/test_%.o: $(TESTDIR)/%.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) $(ORACLE_INCLUDES) -c $< -o $@

# Build static library
$(STATIC_LIB): $(WRAPPER_OBJECTS) $(ODPI_OBJECTS)
	ar rcs $@ $^

# Build shared library
$(SHARED_LIB): $(WRAPPER_OBJECTS) $(ODPI_OBJECTS)
	$(CXX) -shared -o $@ $^ $(ORACLE_LIBS)

# Build tests
test: $(TEST_EXES)

$(TEST_EXES): %: $(OBJDIR)/test_%.o $(STATIC_LIB)
	$(CXX) -o $@ $< $(STATIC_LIB) $(ORACLE_LIBS)

# Install (adjust paths as needed)
install: all
	cp $(INCDIR)/*.hh /usr/local/include/
	cp $(STATIC_LIB) $(SHARED_LIB) /usr/local/lib/

# Clean
clean:
	rm -rf $(OBJDIR) $(LIBDIR) $(TEST_EXES)

# Clean everything including ODPI-C
distclean: clean
	rm -rf $(ODPI_DIR)

# Rebuild ODPI-C from source
rebuild-odpi:
	cd $(ODPI_DIR) && make clean && make

# Help
help:
	@echo "Available targets:"
	@echo "  all        - Build static and shared libraries"
	@echo "  test       - Build test programs"
	@echo "  install    - Install headers and libraries"
	@echo "  clean      - Remove built files"
	@echo "  distclean  - Remove all generated files"
	@echo "  help       - Show this help"

.PHONY: all directories test install clean distclean rebuild-odpi help
