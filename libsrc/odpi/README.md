# ODPI-C Wrapper Library

Oracle Database Programming Interface for C (ODPI-C) wrapper library that provides compatibility with the existing ORAPP interface.

## Overview

This library provides a modern C++ interface to Oracle databases using ODPI-C while maintaining compatibility with the existing ORAPP library interface. It allows for easy migration from OCCI to ODPI-C without changing existing application code.

## Features

- **Compatible Interface**: Drop-in replacement for ORAPP library
- **Modern ODPI-C Backend**: Uses Oracle's official C interface
- **Error Handling**: Comprehensive error handling and logging
- **Memory Management**: Automatic resource management
- **Thread Safe**: Designed for multi-threaded applications
- **Performance**: Lightweight and efficient compared to OCCI

## Requirements

- Oracle Instant Client 11.2 or later
- Oracle Client libraries
- C++11 compatible compiler
- CMake 3.10 or later (for CMake build)

## Directory Structure

```
libsrc/odpi/
├── external/odpi/    # ODPI-C source code (git submodule)
├── src/              # Wrapper implementation
├── inc/              # Header files
├── lib/              # Built libraries
├── test/             # Test programs
├── CMakeLists.txt    # CMake build configuration
├── Makefile          # Traditional make build
└── README.md         # This file
```

## Installation

### Using CMake

```bash
cd libsrc/odpi
mkdir build
cd build
cmake ..
make
```

### Using Makefile

```bash
cd libsrc/odpi
make
```

## Usage

The library provides the same interface as the original ORAPP library:

```cpp
#include "odpi_wrapper.hh"

// Set up logging
ORAPP::log_to(your_log_function);

// Create connection
ORAPP::Connection db;
if (!db.connect("your_tns", "username", "password")) {
    std::cerr << "Connection failed: " << db.error() << std::endl;
    return -1;
}

// Execute query
ORAPP::Query* query = db.query();
*query << "SELECT SYSDATE FROM DUAL";
if (!query->execute()) {
    std::cerr << "Query failed: " << query->error() << std::endl;
    return -1;
}

// Process results
if (query->next()) {
    ORAPP::Row& row = query->row();
    std::cout << "Date: " << row[0].value() << std::endl;
}

// Cleanup
db.disconnect();
```

## Migration from OCCI

This library is designed as a drop-in replacement for the OCCI wrapper. Simply:

1. Replace `#include "occi.hh"` with `#include "odpi_wrapper.hh"`
2. Recompile and link with the new library
3. Test thoroughly

## Building ODPI-C

The ODPI-C library is included as source code in the `external/odpi` directory. It will be built automatically when building this wrapper.

## Testing

```bash
cd libsrc/odpi
make test
./test/test_odpi_wrapper <tns> <username> <password>
```

## License

This wrapper library follows the same license as the original ORAPP library.
ODPI-C itself is licensed under the Universal Permissive License v 1.0 and Apache License v 2.0.
