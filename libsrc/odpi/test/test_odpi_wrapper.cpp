#include "odpi_wrapper.hh"
#include <iostream>
#include <cstdlib>

// Test log function
void test_log_func(const char* message) {
    std::cout << "[ODPI_TEST] " << message << std::endl;
}

int main(int argc, char* argv[]) {
    if (argc != 4) {
        std::cerr << "Usage: " << argv[0] << " <tns> <user> <password>" << std::endl;
        return 1;
    }
    
    const char* tns = argv[1];
    const char* user = argv[2];
    const char* password = argv[3];
    
    // Set up logging
    ORAPP::log_to(test_log_func);
    
    std::cout << "=== ODPI-C Wrapper Library Test ===" << std::endl;
    std::cout << "Connecting to: " << user << "@" << tns << std::endl;
    
    // Test connection
    ORAPP::Connection db;
    
    if (!db.connect(tns, user, password)) {
        std::cerr << "Connection failed: " << db.error() << std::endl;
        return 1;
    }
    
    std::cout << "Connected successfully!" << std::endl;
    std::cout << "Database version: " << db.version() << std::endl;
    
    // Test simple query
    std::cout << "\n=== Testing Simple Query ===" << std::endl;
    ORAPP::Query* query = db.query();
    if (!query) {
        std::cerr << "Failed to create query: " << db.error() << std::endl;
        return 1;
    }
    
    *query << "SELECT SYSDATE FROM DUAL";
    
    if (!query->execute()) {
        std::cerr << "Query execution failed: " << query->error() << std::endl;
        return 1;
    }
    
    if (query->next()) {
        ORAPP::Row& row = query->row();
        std::cout << "Current date: " << row[0].value() << std::endl;
    }
    
    // Test query with parameters (similar to logonDB usage)
    std::cout << "\n=== Testing Parameterized Query ===" << std::endl;
    query->clear();
    *query << "SELECT 'TEST_USER' as USER_ID, 'ACTIVE' as STATUS FROM DUAL WHERE 1 = " << 1;
    
    if (!query->execute()) {
        std::cerr << "Parameterized query failed: " << query->error() << std::endl;
        return 1;
    }
    
    if (query->next()) {
        ORAPP::Row& row = query->row();
        std::cout << "User ID: " << row[0].value() << std::endl;
        std::cout << "Status: " << row[1].value() << std::endl;
    }
    
    // Test multiple rows
    std::cout << "\n=== Testing Multiple Rows ===" << std::endl;
    query->clear();
    *query << "SELECT LEVEL as NUM, 'Row ' || LEVEL as DESCRIPTION FROM DUAL CONNECT BY LEVEL <= 3";
    
    if (!query->execute()) {
        std::cerr << "Multiple rows query failed: " << query->error() << std::endl;
        return 1;
    }
    
    int row_count = 0;
    while (query->next()) {
        ORAPP::Row& row = query->row();
        std::cout << "Row " << ++row_count << ": NUM=" << row[0].value() 
                  << ", DESC=" << row[1].value() << std::endl;
    }
    
    // Test error handling
    std::cout << "\n=== Testing Error Handling ===" << std::endl;
    query->clear();
    *query << "SELECT * FROM NON_EXISTENT_TABLE";
    
    if (!query->execute()) {
        std::cout << "Expected error caught: " << query->error() << std::endl;
    } else {
        std::cerr << "ERROR: Query should have failed!" << std::endl;
    }
    
    // Test transaction
    std::cout << "\n=== Testing Transaction ===" << std::endl;
    if (db.commit()) {
        std::cout << "Commit successful" << std::endl;
    } else {
        std::cout << "Commit failed: " << db.error() << std::endl;
    }
    
    // Test disconnect
    std::cout << "\n=== Testing Disconnect ===" << std::endl;
    if (db.disconnect()) {
        std::cout << "Disconnected successfully" << std::endl;
    } else {
        std::cout << "Disconnect failed: " << db.error() << std::endl;
    }
    
    std::cout << "\n=== Test Completed ===" << std::endl;
    return 0;
}
