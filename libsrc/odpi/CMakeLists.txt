cmake_minimum_required(VERSION 3.10)
project(ODPIWrapper VERSION 1.0.0 LANGUAGES C CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set C standard
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler flags
set(CMAKE_CXX_FLAGS_DEBUG "-g -Wall -Wextra")
set(CMAKE_CXX_FLAGS_RELEASE "-O2 -Wall -Wextra")
set(CMAKE_C_FLAGS_DEBUG "-g -Wall -Wextra")
set(CMAKE_C_FLAGS_RELEASE "-O2 -Wall -Wextra")

# Find Oracle Client
find_path(ORACLE_INCLUDE_DIR
    NAMES oci.h
    PATHS
        $ENV{ORACLE_HOME}/sdk/include
        $ENV{ORACLE_HOME}/rdbms/public
        /opt/oracle/instantclient/sdk/include
        /usr/include/oracle
        /usr/local/include/oracle
)

find_library(ORACLE_LIBRARY
    NAMES clntsh
    PATHS
        $ENV{ORACLE_HOME}/lib
        $ENV{ORACLE_HOME}
        /opt/oracle/instantclient
        /usr/lib/oracle
        /usr/local/lib/oracle
)

if(NOT ORACLE_INCLUDE_DIR OR NOT ORACLE_LIBRARY)
    message(FATAL_ERROR "Oracle Client libraries not found. Please set ORACLE_HOME environment variable.")
endif()

message(STATUS "Oracle include directory: ${ORACLE_INCLUDE_DIR}")
message(STATUS "Oracle library: ${ORACLE_LIBRARY}")

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/inc
    ${CMAKE_CURRENT_SOURCE_DIR}/external/odpi/include
    ${ORACLE_INCLUDE_DIR}
)

# ODPI-C source files
file(GLOB ODPI_SOURCES "external/odpi/src/*.c")

# Wrapper source files
file(GLOB WRAPPER_SOURCES "src/*.cpp")

# Create ODPI-C static library
add_library(odpi STATIC ${ODPI_SOURCES})
target_compile_definitions(odpi PRIVATE DPI_EXPORTS)

# Create wrapper library
add_library(odpiwrapper STATIC ${WRAPPER_SOURCES})
target_link_libraries(odpiwrapper odpi ${ORACLE_LIBRARY})

# Create shared library
add_library(odpiwrapper_shared SHARED ${WRAPPER_SOURCES})
target_link_libraries(odpiwrapper_shared odpi ${ORACLE_LIBRARY})
set_target_properties(odpiwrapper_shared PROPERTIES OUTPUT_NAME odpiwrapper)

# Test programs
option(BUILD_TESTS "Build test programs" ON)
if(BUILD_TESTS)
    file(GLOB TEST_SOURCES "test/*.cpp")
    foreach(test_source ${TEST_SOURCES})
        get_filename_component(test_name ${test_source} NAME_WE)
        add_executable(${test_name} ${test_source})
        target_link_libraries(${test_name} odpiwrapper ${ORACLE_LIBRARY})
    endforeach()
endif()

# Installation
install(TARGETS odpiwrapper odpiwrapper_shared
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
)

install(DIRECTORY inc/
    DESTINATION include
    FILES_MATCHING PATTERN "*.hh"
)

# Package configuration
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/ODPIWrapperConfigVersion.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY AnyNewerVersion
)

configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/ODPIWrapperConfig.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/ODPIWrapperConfig.cmake"
    INSTALL_DESTINATION lib/cmake/ODPIWrapper
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/ODPIWrapperConfig.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/ODPIWrapperConfigVersion.cmake"
    DESTINATION lib/cmake/ODPIWrapper
)
