#include "error.hh"
#include "log.hh"

namespace ORAPP {

// OCCIException implementation
OCCIException::OCCIException(const std::string& message, int error_code)
    : _message(message), _error_code(error_code) {
}

OCCIException::~OCCIException() throw() {
}

const char* OCCIException::what() const throw() {
    return _message.c_str();
}

int OCCIException::error_code() const {
    return _error_code;
}

const std::string& OCCIException::message() const {
    return _message;
}

// ErrorHandler implementation
std::string ErrorHandler::_last_error = "";
int ErrorHandler::_last_error_code = 0;

void ErrorHandler::set_error(const std::string& error, int code) {
    _last_error = error;
    _last_error_code = code;
    log_error(error.c_str());
}

const std::string& ErrorHandler::get_last_error() {
    return _last_error;
}

int ErrorHandler::get_last_error_code() {
    return _last_error_code;
}

void ErrorHandler::clear_error() {
    _last_error.clear();
    _last_error_code = 0;
}

}
