#include "occi.hh"
#include <iostream>
#include <cstdlib>

// Test log function
void test_log_func(const char* message) {
    std::cout << "[OCCI_TEST] " << message << std::endl;
}

int main(int argc, char* argv[]) {
    if (argc != 4) {
        std::cerr << "Usage: " << argv[0] << " <tns> <user> <password>" << std::endl;
        return 1;
    }
    
    const char* tns = argv[1];
    const char* user = argv[2];
    const char* password = argv[3];
    
    // Set up logging
    ORAPP::log_to(test_log_func);
    
    std::cout << "=== OCCI Library Test ===" << std::endl;
    std::cout << "Connecting to: " << user << "@" << tns << std::endl;
    
    // Test connection
    ORAPP::Connection db;
    
    if (!db.connect(tns, user, password)) {
        std::cerr << "Connection failed: " << db.error() << std::endl;
        return 1;
    }
    
    std::cout << "Connected successfully!" << std::endl;
    std::cout << "Database version: " << db.version() << std::endl;
    
    // Test simple query
    std::cout << "\n=== Testing Simple Query ===" << std::endl;
    ORAPP::Query* query = db.query();
    if (!query) {
        std::cerr << "Failed to create query: " << db.error() << std::endl;
        return 1;
    }
    
    *query << "SELECT SYSDATE FROM DUAL";
    
    if (!query->execute()) {
        std::cerr << "Query execution failed: " << query->error() << std::endl;
        delete query;
        return 1;
    }
    
    if (query->next()) {
        ORAPP::Row& row = query->row();
        std::cout << "Current date: " << row[0].value() << std::endl;
    }
    
    delete query;
    
    // Test parameterized query
    std::cout << "\n=== Testing Parameterized Query ===" << std::endl;
    query = db.query();
    *query << "SELECT " << 42 << " AS test_number, '" << "test_string" << "' AS test_string FROM DUAL";
    
    if (!query->execute()) {
        std::cerr << "Parameterized query failed: " << query->error() << std::endl;
        delete query;
        return 1;
    }
    
    if (query->next()) {
        ORAPP::Row& row = query->row();
        std::cout << "Test number: " << row[0].value() << std::endl;
        std::cout << "Test string: " << row[1].value() << std::endl;
        
        // Test field access by name (if column names are available)
        if (row.has_field("TEST_NUMBER")) {
            std::cout << "Test number (by name): " << row["TEST_NUMBER"].value() << std::endl;
        }
        if (row.has_field("TEST_STRING")) {
            std::cout << "Test string (by name): " << row["TEST_STRING"].value() << std::endl;
        }
    }
    
    delete query;
    
    // Test multiple rows
    std::cout << "\n=== Testing Multiple Rows ===" << std::endl;
    query = db.query();
    *query << "SELECT LEVEL AS row_num, 'Row ' || LEVEL AS row_text FROM DUAL CONNECT BY LEVEL <= 3";
    
    if (!query->execute()) {
        std::cerr << "Multiple rows query failed: " << query->error() << std::endl;
        delete query;
        return 1;
    }
    
    int row_count = 0;
    while (query->next()) {
        ORAPP::Row& row = query->row();
        std::cout << "Row " << (++row_count) << ": " 
                  << "num=" << row[0].value() 
                  << ", text=" << row[1].value() << std::endl;
    }
    
    delete query;
    
    // Test error handling
    std::cout << "\n=== Testing Error Handling ===" << std::endl;
    query = db.query();
    *query << "SELECT * FROM non_existent_table";
    
    if (!query->execute()) {
        std::cout << "Expected error caught: " << query->error() << std::endl;
    } else {
        std::cout << "ERROR: Query should have failed!" << std::endl;
    }
    
    delete query;
    
    // Test transaction operations
    std::cout << "\n=== Testing Transaction Operations ===" << std::endl;
    if (db.auto_commit(false)) {
        std::cout << "Auto-commit disabled" << std::endl;
    }
    
    if (db.commit()) {
        std::cout << "Commit successful" << std::endl;
    }
    
    if (db.auto_commit(true)) {
        std::cout << "Auto-commit re-enabled" << std::endl;
    }
    
    // Disconnect
    std::cout << "\n=== Disconnecting ===" << std::endl;
    if (db.disconnect()) {
        std::cout << "Disconnected successfully" << std::endl;
    } else {
        std::cerr << "Disconnect failed: " << db.error() << std::endl;
    }
    
    std::cout << "\n=== Test Completed ===" << std::endl;
    return 0;
}
