#ifndef OCCI_QUERY_HH
#define OCCI_QUERY_HH

/*
 * OCCI Query - Compatible with orapp interface
 */

#include <string>
#include <vector>
#include <map>
#include <occi.h>
#include "row.hh"
#include "constants.hh"

namespace ORAPP {

// Forward declaration
class Connection;

class Query {
private:
    oracle::occi::Connection* _occi_conn;
    oracle::occi::Statement* _stmt;
    oracle::occi::ResultSet* _rs;
    
    std::string _sql;
    QueryState _state;
    bool _has_result_set;
    int _affected_rows;
    
    // Current row data
    Row _current_row;
    bool _row_available;
    int _rows_fetched;  // Track number of rows fetched

    // Parameter binding storage
    std::map<std::string, std::string> _bind_params;
    
    // Error handling
    std::string _last_error;

public:
    Query(oracle::occi::Connection* conn);
    ~Query();

    // SQL operations
    Query& operator<<(const std::string& sql);
    Query& operator<<(const char* sql);
    Query& operator<<(int value);
    Query& operator<<(long value);
    Query& operator<<(double value);
    Query& operator<<(float value);

    // Parameter binding (orapp compatibility)
    void bind(const std::string& param, const std::string& value);
    void bind(const std::string& param, const char* value, int size);
    void bind(const std::string& param, int value);
    void bind(const std::string& param, long value);
    void bind(const std::string& param, double value);

    // Execution
    bool execute();
    bool execute(const std::string& sql);

    // Result handling
    bool next();
    Row& row();
    const Row& row() const;
    Row* fetch();  // orapp compatibility - returns pointer to row or nullptr
    
    // Result set information
    bool has_result_set() const;
    int affected_rows() const;
    int column_count() const;
    int rows() const;  // orapp compatibility - returns number of rows fetched so far
    
    // Column information
    std::string column_name(int index) const;
    FieldType column_type(int index) const;
    int column_size(int index) const;
    
    // State management
    QueryState state() const;
    void reset();
    void close();
    
    // Error handling
    std::string error() const;
    bool has_error() const;
    
    // Utility methods
    std::string get_sql() const;
    void clear_sql();

private:
    // Internal methods
    void clear_result_set();
    void prepare_metadata();
    void fetch_row_data();
    FieldType occi_type_to_field_type(oracle::occi::Type occi_type) const;
    void set_error(const std::string& error);
    void clear_error();
};

}

#endif
