#include "row.hh"
#include "error.hh"
#include <iostream>
#include <sstream>

namespace ORAPP {

// Constructor
Row::Row() : _current_field(-1) {
}

Row::~Row() {
}

// Field access by index
Field& Row::operator[](int index) {
    if (index < 0 || index >= static_cast<int>(_fields.size())) {
        ErrorHandler::set_error("Row field index out of range");
        static Field empty_field;
        return empty_field;
    }
    return _fields[index];
}

const Field& Row::operator[](int index) const {
    if (index < 0 || index >= static_cast<int>(_fields.size())) {
        ErrorHandler::set_error("Row field index out of range");
        static Field empty_field;
        return empty_field;
    }
    return _fields[index];
}

// Field access by name
Field& Row::operator[](const std::string& name) {
    return get_field(name);
}

const Field& Row::operator[](const std::string& name) const {
    return get_field(name);
}

// Field management
void Row::add_field(const Field& field) {
    _field_map[field.name()] = _fields.size();
    _fields.push_back(field);
}

void Row::add_field(const std::string& name, const std::string& value, FieldType type) {
    Field field(name, value, type);
    add_field(field);
}

// Field count
int Row::field_count() const {
    return _fields.size();
}

int Row::size() const {
    return field_count();
}

int Row::width() const {
    return field_count();
}

// Field iteration
void Row::reset() {
    _current_field = -1;
}

bool Row::next_field() {
    _current_field++;
    return _current_field < static_cast<int>(_fields.size());
}

Field& Row::current_field() {
    if (_current_field < 0 || _current_field >= static_cast<int>(_fields.size())) {
        ErrorHandler::set_error("No current field available");
        static Field empty_field;
        return empty_field;
    }
    return _fields[_current_field];
}

const Field& Row::current_field() const {
    if (_current_field < 0 || _current_field >= static_cast<int>(_fields.size())) {
        ErrorHandler::set_error("No current field available");
        static Field empty_field;
        return empty_field;
    }
    return _fields[_current_field];
}

// Field lookup
bool Row::has_field(const std::string& name) const {
    return _field_map.find(name) != _field_map.end();
}

int Row::get_field_index(const std::string& name) const {
    std::map<std::string, int>::const_iterator it = _field_map.find(name);
    if (it != _field_map.end()) {
        return it->second;
    }
    return -1;
}

Field& Row::get_field(const std::string& name) {
    int index = get_field_index(name);
    if (index >= 0) {
        return _fields[index];
    }
    ErrorHandler::set_error("Field not found: " + name);
    static Field empty_field;
    return empty_field;
}

const Field& Row::get_field(const std::string& name) const {
    int index = get_field_index(name);
    if (index >= 0) {
        return _fields[index];
    }
    ErrorHandler::set_error("Field not found: " + name);
    static Field empty_field;
    return empty_field;
}

// Row operations
void Row::clear() {
    _fields.clear();
    _field_map.clear();
    _current_field = -1;
}

bool Row::empty() const {
    return _fields.empty();
}

// Utility methods
std::string Row::to_string() const {
    std::ostringstream oss;
    oss << "Row[" << _fields.size() << " fields]: ";
    for (size_t i = 0; i < _fields.size(); ++i) {
        if (i > 0) oss << ", ";
        oss << _fields[i].name() << "=" << _fields[i].value();
    }
    return oss.str();
}

void Row::print() const {
    std::cout << to_string() << std::endl;
}

// Iterator support
Row::iterator Row::begin() {
    return _fields.begin();
}

Row::iterator Row::end() {
    return _fields.end();
}

Row::const_iterator Row::begin() const {
    return _fields.begin();
}

Row::const_iterator Row::end() const {
    return _fields.end();
}

// orapp compatibility methods
const char* Row::name(int index) const {
    if (index >= 0 && index < static_cast<int>(_fields.size())) {
        return _fields[index].name().c_str();
    }
    return "";
}

}
