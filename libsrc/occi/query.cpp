#include "query.hh"
#include "error.hh"
#include "log.hh"
#include <sstream>
#include <iostream>
#include <map>
#include <algorithm>
#include <cctype>

namespace ORAPP {

// Constructor
Query::Query(oracle::occi::Connection* conn)
    : _occi_conn(conn), _stmt(nullptr), _rs(nullptr), _sql(""),
      _state(QUERY_STATE_INIT), _has_result_set(false), _affected_rows(0),
      _row_available(false), _rows_fetched(0) {
}

Query::~Query() {
    close();
}

// SQL operations
Query& Query::operator<<(const std::string& sql) {
    _sql += sql;
    return *this;
}

Query& Query::operator<<(const char* sql) {
    if (sql) {
        _sql += std::string(sql);
    }
    return *this;
}

Query& Query::operator<<(int value) {
    std::ostringstream oss;
    oss << value;
    _sql += oss.str();
    return *this;
}

Query& Query::operator<<(long value) {
    std::ostringstream oss;
    oss << value;
    _sql += oss.str();
    return *this;
}

Query& Query::operator<<(double value) {
    std::ostringstream oss;
    oss << value;
    _sql += oss.str();
    return *this;
}

Query& Query::operator<<(float value) {
    std::ostringstream oss;
    oss << value;
    _sql += oss.str();
    return *this;
}

// Parameter binding (orapp compatibility)
void Query::bind(const std::string& param, const std::string& value) {
    _bind_params[param] = "'" + value + "'";
}

void Query::bind(const std::string& param, const char* value, int size) {
    if (value) {
        std::string str_value(value, size);
        _bind_params[param] = "'" + str_value + "'";
    } else {
        _bind_params[param] = "NULL";
    }
}

void Query::bind(const std::string& param, int value) {
    std::ostringstream oss;
    oss << value;
    _bind_params[param] = oss.str();
}

void Query::bind(const std::string& param, long value) {
    std::ostringstream oss;
    oss << value;
    _bind_params[param] = oss.str();
}

void Query::bind(const std::string& param, double value) {
    std::ostringstream oss;
    oss << value;
    _bind_params[param] = oss.str();
}

// Execution
bool Query::execute() {
    return execute(_sql);
}

bool Query::execute(const std::string& sql) {
    log_debug_step("Query execution", "Starting SQL execution");

    try {
        clear_error();
        close(); // Close any previous statement/resultset

        if (!_occi_conn) {
            set_error("No database connection available");
            log_error("No database connection available for query execution");
            return false;
        }

        std::string final_sql = sql.empty() ? _sql : sql;
        if (final_sql.empty()) {
            set_error("Empty SQL statement");
            log_error("Empty SQL statement provided");
            return false;
        }

        // Debug: Log original SQL before bind replacement
        FILE* debug_file = fopen("/tmp/occi_bind_debug.log", "a");
        if (debug_file) {
            fprintf(debug_file, "=== BIND REPLACEMENT DEBUG ===\n");
            fprintf(debug_file, "Original SQL length: %zu\n", final_sql.length());
            fprintf(debug_file, "Original SQL: [%s]\n", final_sql.c_str());
            fprintf(debug_file, "Bind parameters count: %zu\n", _bind_params.size());
        }

        // Replace bind parameters
        for (std::map<std::string, std::string>::const_iterator it = _bind_params.begin();
             it != _bind_params.end(); ++it) {
            if (debug_file) {
                fprintf(debug_file, "Replacing [%s] with [%s]\n", it->first.c_str(), it->second.c_str());
            }
            size_t pos = 0;
            while ((pos = final_sql.find(it->first, pos)) != std::string::npos) {
                final_sql.replace(pos, it->first.length(), it->second);
                pos += it->second.length();
                if (debug_file) {
                    fprintf(debug_file, "After replacement, SQL length: %zu\n", final_sql.length());
                }
            }
        }

        if (debug_file) {
            fprintf(debug_file, "Final SQL length: %zu\n", final_sql.length());
            // Write SQL safely without format string interpretation
            fprintf(debug_file, "Final SQL: [");
            fwrite(final_sql.c_str(), 1, final_sql.length(), debug_file);
            fprintf(debug_file, "]\n");
            fprintf(debug_file, "=== END BIND REPLACEMENT ===\n\n");
            fclose(debug_file);
        }

        // Log SQL with length information for debugging
        std::ostringstream sql_info;
        sql_info << "SQL length: " << final_sql.length() << " characters";
        log_debug_step("SQL info", sql_info.str().c_str());

        // Debug: Log original SQL and first/last parts
        std::ostringstream debug_info;
        debug_info << "Original SQL length: " << _sql.length() << ", Final SQL length: " << final_sql.length();
        log_debug_step("SQL Debug", debug_info.str().c_str());

        if (final_sql.length() > 200) {
            std::string first_part = final_sql.substr(0, 100);
            std::string last_part = final_sql.substr(final_sql.length() - 100);
            log_debug_step("SQL First 100", first_part.c_str());
            log_debug_step("SQL Last 100", last_part.c_str());
        }

        log_debug_step("Executing SQL", final_sql.c_str());
        log_debug_step("SQL prepared", final_sql.c_str());
        log_memory_info("Before createStatement", _occi_conn);

        // Create statement with detailed error handling
        log_occi_operation("createStatement", "STARTING");
        try {
            _stmt = _occi_conn->createStatement(final_sql);
            log_occi_operation("createStatement", "SUCCESS");
            log_memory_info("After createStatement", _stmt);
        } catch (oracle::occi::SQLException& stmt_ex) {
            std::ostringstream oss;
            oss << "Failed to create statement - Oracle error " << stmt_ex.getErrorCode() << ": " << stmt_ex.getMessage();
            set_error(oss.str());
            log_error(oss.str().c_str());
            log_occi_operation("createStatement", "FAILED_SQL_EXCEPTION");
            _state = QUERY_STATE_ERROR;
            return false;
        } catch (std::exception& ex) {
            std::string error_msg = "Standard exception during createStatement: " + std::string(ex.what());
            set_error(error_msg);
            log_error(error_msg.c_str());
            log_occi_operation("createStatement", "FAILED_STD_EXCEPTION");
            _state = QUERY_STATE_ERROR;
            return false;
        } catch (...) {
            set_error("Unknown exception during createStatement");
            log_error("Unknown exception during createStatement");
            log_occi_operation("createStatement", "FAILED_UNKNOWN_EXCEPTION");
            _state = QUERY_STATE_ERROR;
            return false;
        }

        if (!_stmt) {
            set_error("Failed to create statement - null statement returned");
            log_error("createStatement returned null pointer");
            log_occi_operation("createStatement", "FAILED_NULL_RETURN");
            _state = QUERY_STATE_ERROR;
            return false;
        }

        _state = QUERY_STATE_PREPARED;
        log_debug_step("Statement prepared", "Executing statement");

        // Execute the statement with detailed error handling
        oracle::occi::Statement::Status status;
        log_occi_operation("execute", "STARTING");
        try {
            status = _stmt->execute();
            log_occi_operation("execute", "SUCCESS");
            log_debug_step("Statement executed", "Processing results");
        } catch (oracle::occi::SQLException& exec_ex) {
            std::ostringstream oss;
            oss << "SQL execution failed - Oracle error " << exec_ex.getErrorCode() << ": " << exec_ex.getMessage();
            set_error(oss.str());
            log_error(oss.str().c_str());
            log_occi_operation("execute", "FAILED_SQL_EXCEPTION");
            _state = QUERY_STATE_ERROR;
            return false;
        } catch (std::exception& ex) {
            std::string error_msg = "Standard exception during execute: " + std::string(ex.what());
            set_error(error_msg);
            log_error(error_msg.c_str());
            log_occi_operation("execute", "FAILED_STD_EXCEPTION");
            _state = QUERY_STATE_ERROR;
            return false;
        } catch (...) {
            set_error("Unknown exception during execute");
            log_error("Unknown exception during execute");
            log_occi_operation("execute", "FAILED_UNKNOWN_EXCEPTION");
            _state = QUERY_STATE_ERROR;
            return false;
        }

        // Process execution results
        if (status == oracle::occi::Statement::RESULT_SET_AVAILABLE) {
            log_debug_step("Result processing", "Getting result set");
            log_occi_operation("getResultSet", "STARTING");
            try {
                _rs = _stmt->getResultSet();
                log_occi_operation("getResultSet", "SUCCESS");
                log_memory_info("After getResultSet", _rs);
                _has_result_set = true;
                _state = QUERY_STATE_EXECUTED;
                prepare_metadata();
            } catch (oracle::occi::SQLException& rs_ex) {
                std::ostringstream oss;
                oss << "Failed to get result set - Oracle error " << rs_ex.getErrorCode() << ": " << rs_ex.getMessage();
                set_error(oss.str());
                log_error(oss.str().c_str());
                log_occi_operation("getResultSet", "FAILED_SQL_EXCEPTION");
                _state = QUERY_STATE_ERROR;
                return false;
            } catch (std::exception& ex) {
                std::string error_msg = "Standard exception during getResultSet: " + std::string(ex.what());
                set_error(error_msg);
                log_error(error_msg.c_str());
                log_occi_operation("getResultSet", "FAILED_STD_EXCEPTION");
                _state = QUERY_STATE_ERROR;
                return false;
            } catch (...) {
                set_error("Unknown exception during getResultSet");
                log_error("Unknown exception during getResultSet");
                log_occi_operation("getResultSet", "FAILED_UNKNOWN_EXCEPTION");
                _state = QUERY_STATE_ERROR;
                return false;
            }
        } else if (status == oracle::occi::Statement::UPDATE_COUNT_AVAILABLE) {
            log_debug_step("Result processing", "Getting update count");
            log_occi_operation("getUpdateCount", "STARTING");
            try {
                _affected_rows = _stmt->getUpdateCount();
                log_occi_operation("getUpdateCount", "SUCCESS");
                _has_result_set = false;
                _state = QUERY_STATE_FINISHED;
                log_debug_step("Update count", std::to_string(_affected_rows).c_str());
            } catch (oracle::occi::SQLException& uc_ex) {
                std::string error_msg = "Failed to get update count: " + std::string(uc_ex.getMessage());
                log_error(error_msg.c_str());
                log_occi_operation("getUpdateCount", "FAILED_SQL_EXCEPTION");
                _affected_rows = 0;
                _has_result_set = false;
                _state = QUERY_STATE_FINISHED;
            } catch (...) {
                log_error("Unknown exception during getUpdateCount");
                log_occi_operation("getUpdateCount", "FAILED_UNKNOWN_EXCEPTION");
                _affected_rows = 0;
                _has_result_set = false;
                _state = QUERY_STATE_FINISHED;
            }
        } else {
            log_debug_step("Result processing", "No specific result type");
            _has_result_set = false;
            _state = QUERY_STATE_FINISHED;
        }

        log_debug_step("Query execution", "Completed successfully");
        return true;

    } catch (std::bad_alloc& ex) {
        set_error("Memory allocation failed during query execution");
        log_error("Memory allocation failed during query execution");
        log_debug_step("Query execution", "Failed - bad allocation");
        _state = QUERY_STATE_ERROR;
        return false;
    } catch (std::exception& ex) {
        std::string error_msg = "Standard exception during query execution: " + std::string(ex.what());
        set_error(error_msg);
        log_error(error_msg.c_str());
        log_debug_step("Query execution", "Failed - standard exception");
        _state = QUERY_STATE_ERROR;
        return false;
    } catch (...) {
        set_error("Unknown exception occurred during query execution");
        log_error("Unknown exception occurred during query execution");
        log_debug_step("Query execution", "Failed - unknown exception");
        _state = QUERY_STATE_ERROR;
        return false;
    }
}

// Result handling
bool Query::next() {
    log_debug_step("Result fetch", "Attempting to fetch next row");

    try {
        if (!_rs) {
            log_debug_step("Result fetch", "No result set available");
            return false;
        }

        if (_state != QUERY_STATE_EXECUTED && _state != QUERY_STATE_FETCHING) {
            log_debug_step("Result fetch", "Invalid query state");
            return false;
        }

        log_occi_operation("ResultSet::next", "STARTING");
        if (_rs->next()) {
            log_occi_operation("ResultSet::next", "SUCCESS_ROW_AVAILABLE");
            _state = QUERY_STATE_FETCHING;

            log_debug_step("Row fetch", "Fetching row data");
            fetch_row_data();

            _row_available = true;
            _rows_fetched++;
            log_debug_step("Row fetched", ("Row " + std::to_string(_rows_fetched)).c_str());
            return true;
        } else {
            log_occi_operation("ResultSet::next", "SUCCESS_NO_MORE_ROWS");
            _state = QUERY_STATE_FINISHED;
            _row_available = false;
            log_debug_step("Result fetch", "No more rows available");
            return false;
        }

    } catch (oracle::occi::SQLException& ex) {
        std::ostringstream oss;
        oss << "Oracle error during fetch " << ex.getErrorCode() << ": " << ex.getMessage();
        set_error(oss.str());
        log_error(oss.str().c_str());
        log_occi_operation("ResultSet::next", "FAILED_SQL_EXCEPTION");
        _state = QUERY_STATE_ERROR;
        _row_available = false;
        return false;
    } catch (std::exception& ex) {
        std::string error_msg = "Standard exception during fetch: " + std::string(ex.what());
        set_error(error_msg);
        log_error(error_msg.c_str());
        log_occi_operation("ResultSet::next", "FAILED_STD_EXCEPTION");
        _state = QUERY_STATE_ERROR;
        _row_available = false;
        return false;
    } catch (...) {
        set_error("Unknown exception occurred during fetch");
        log_error("Unknown exception occurred during fetch");
        log_occi_operation("ResultSet::next", "FAILED_UNKNOWN_EXCEPTION");
        _state = QUERY_STATE_ERROR;
        _row_available = false;
        return false;
    }
}

Row& Query::row() {
    return _current_row;
}

const Row& Query::row() const {
    return _current_row;
}

// orapp compatibility - returns pointer to row or nullptr
Row* Query::fetch() {
    if (next()) {
        return &_current_row;
    }
    return nullptr;
}

// Result set information
bool Query::has_result_set() const {
    return _has_result_set;
}

int Query::affected_rows() const {
    return _affected_rows;
}

int Query::column_count() const {
    if (_rs) {
        try {
            return _rs->getColumnListMetaData().size();
        } catch (...) {
            return 0;
        }
    }
    return 0;
}

int Query::rows() const {
    return _rows_fetched;
}

// Column information
std::string Query::column_name(int index) const {
    // Use generic column names for compatibility
    std::ostringstream oss;
    oss << "COL" << (index + 1);
    return oss.str();
}

FieldType Query::column_type(int index) const {
    // Default to VARCHAR for compatibility
    return FIELD_TYPE_VARCHAR;
}

int Query::column_size(int index) const {
    // Default size for compatibility
    return 255;
}

// State management
QueryState Query::state() const {
    return _state;
}

void Query::reset() {
    close();
    _sql.clear();
    _bind_params.clear();
    _state = QUERY_STATE_INIT;
    _rows_fetched = 0;
    clear_error();
}

void Query::close() {
    clear_result_set();
    
    if (_stmt && _occi_conn) {
        try {
            _occi_conn->terminateStatement(_stmt);
        } catch (...) {
            // Ignore errors during cleanup
        }
        _stmt = nullptr;
    }
    
    _state = QUERY_STATE_INIT;
    _row_available = false;
    _rows_fetched = 0;
}

// Error handling
std::string Query::error() const {
    return _last_error;
}

bool Query::has_error() const {
    return !_last_error.empty();
}

// Utility methods
std::string Query::get_sql() const {
    return _sql;
}

void Query::clear_sql() {
    _sql.clear();
}

// Private methods
void Query::clear_result_set() {
    if (_rs && _stmt) {
        try {
            _stmt->closeResultSet(_rs);
        } catch (...) {
            // Ignore errors during cleanup
        }
        _rs = nullptr;
    }
    _has_result_set = false;
    _current_row.clear();
}

void Query::prepare_metadata() {
    if (!_rs) {
        return;
    }

    try {
        // Get column metadata from result set
        std::vector<oracle::occi::MetaData> metadata = _rs->getColumnListMetaData();
        log_debug(("Prepared metadata for " + std::to_string(metadata.size()) + " columns").c_str());
    } catch (oracle::occi::SQLException& ex) {
        std::ostringstream oss;
        oss << "Failed to get column metadata: " << ex.getMessage();
        set_error(oss.str());
    } catch (...) {
        log_debug("Exception during metadata preparation");
    }
}

void Query::fetch_row_data() {
    if (!_rs) {
        return;
    }

    _current_row.clear();

    try {
        // Get column count - use a simpler approach
        int col_count = 0;
        try {
            std::vector<oracle::occi::MetaData> metadata = _rs->getColumnListMetaData();
            col_count = metadata.size();
        } catch (...) {
            // If metadata fails, try to determine column count by trial
            col_count = 10; // Assume max 10 columns for safety
        }

        for (int i = 1; i <= col_count; ++i) {
            std::ostringstream col_name_oss;
            col_name_oss << "COL" << i;
            std::string column_name = col_name_oss.str();

            std::string value;
            bool is_null = false;

            try {
                if (_rs->isNull(i)) {
                    is_null = true;
                } else {
                    // Try to get as string first
                    try {
                        value = _rs->getString(i);
                    } catch (...) {
                        // If string conversion fails, try number
                        try {
                            oracle::occi::Number num = _rs->getNumber(i);
                            double d = (double)num;
                            std::ostringstream oss;
                            oss << d;
                            value = oss.str();
                        } catch (...) {
                            // If number fails, try date
                            try {
                                oracle::occi::Date date = _rs->getDate(i);
                                // Simple date conversion without toText
                                int year;
                                unsigned int month, day, hour, min, sec;
                                date.getDate(year, month, day, hour, min, sec);
                                std::ostringstream oss;
                                oss << year << "-" << month << "-" << day << " "
                                    << hour << ":" << min << ":" << sec;
                                value = oss.str();
                            } catch (...) {
                                value = ""; // Default to empty string
                            }
                        }
                    }
                }

                Field field(column_name, value, FIELD_TYPE_VARCHAR);
                if (is_null) {
                    field.set_null(true);
                }
                _current_row.add_field(field);

            } catch (...) {
                // If this column doesn't exist, we've reached the end
                break;
            }
        }
    } catch (oracle::occi::SQLException& ex) {
        std::ostringstream oss;
        oss << "Failed to fetch row data: " << ex.getMessage();
        set_error(oss.str());
    } catch (...) {
        set_error("Unknown exception during row data fetch");
    }
}

FieldType Query::occi_type_to_field_type(oracle::occi::Type occi_type) const {
    switch (occi_type) {
        case oracle::occi::OCCI_SQLT_CHR:
        case oracle::occi::OCCI_SQLT_STR:
        case oracle::occi::OCCI_SQLT_VCS:
        case oracle::occi::OCCI_SQLT_AFC:
            return FIELD_TYPE_VARCHAR;
        case oracle::occi::OCCI_SQLT_NUM:
            return FIELD_TYPE_NUMBER;
        case oracle::occi::OCCI_SQLT_DAT:
        case oracle::occi::OCCI_SQLT_TIMESTAMP:
            return FIELD_TYPE_DATE;
        case oracle::occi::OCCI_SQLT_LNG:
            return FIELD_TYPE_LONG;
        case oracle::occi::OCCI_SQLT_CLOB:
            return FIELD_TYPE_CLOB;
        case oracle::occi::OCCI_SQLT_BLOB:
            return FIELD_TYPE_BLOB;
        default:
            return FIELD_TYPE_UNKNOWN;
    }
}

void Query::set_error(const std::string& error) {
    _last_error = error;
    ErrorHandler::set_error(error);
}

void Query::clear_error() {
    _last_error.clear();
}

}
