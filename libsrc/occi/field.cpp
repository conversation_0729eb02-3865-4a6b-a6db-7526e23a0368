#include "field.hh"
#include "error.hh"
#include <sstream>
#include <cstdlib>
#include <cstring>

namespace ORAPP {

// Constructor
Field::Field() 
    : _name(""), _value(""), _type(FIELD_TYPE_UNKNOWN), _is_null(true), 
      _size(0), _precision(0), _scale(0) {
}

Field::Field(const std::string& name, const std::string& value, FieldType type)
    : _name(name), _value(value), _type(type), _is_null(value.empty()),
      _size(value.length()), _precision(0), _scale(0) {
}

Field::~Field() {
}

// Name operations
const std::string& Field::name() const {
    return _name;
}

void Field::set_name(const std::string& name) {
    _name = name;
}

// Value operations
const std::string& Field::value() const {
    return _value;
}

void Field::set_value(const std::string& value) {
    _value = value;
    _is_null = value.empty();
    _size = value.length();
}

// Type operations
FieldType Field::type() const {
    return _type;
}

void Field::set_type(FieldType type) {
    _type = type;
}

// Null operations
bool Field::is_null() const {
    return _is_null;
}

void Field::set_null(bool null) {
    _is_null = null;
    if (null) {
        _value.clear();
        _size = 0;
    }
}

// Size and precision
int Field::size() const {
    return _size;
}

void Field::set_size(int size) {
    _size = size;
}

int Field::precision() const {
    return _precision;
}

void Field::set_precision(int precision) {
    _precision = precision;
}

int Field::scale() const {
    return _scale;
}

void Field::set_scale(int scale) {
    _scale = scale;
}

// Conversion operators
Field::operator const char*() const {
    return _value.c_str();
}

Field::operator char*() const {
    // Note: This is not safe but needed for orapp compatibility
    // Use const_cast to avoid memory allocation issues
    // The caller should not modify or free this pointer
    return const_cast<char*>(_value.c_str());
}

Field::operator std::string() const {
    return _value;
}

Field::operator int() const {
    return to_int();
}

Field::operator long() const {
    return to_long();
}

Field::operator double() const {
    return to_double();
}

Field::operator float() const {
    return to_float();
}

// Assignment operators
Field& Field::operator=(const std::string& value) {
    set_value(value);
    return *this;
}

Field& Field::operator=(const char* value) {
    set_value(value ? std::string(value) : std::string(""));
    return *this;
}

Field& Field::operator=(int value) {
    std::ostringstream oss;
    oss << value;
    set_value(oss.str());
    _type = FIELD_TYPE_NUMBER;
    return *this;
}

Field& Field::operator=(long value) {
    std::ostringstream oss;
    oss << value;
    set_value(oss.str());
    _type = FIELD_TYPE_NUMBER;
    return *this;
}

Field& Field::operator=(double value) {
    std::ostringstream oss;
    oss << value;
    set_value(oss.str());
    _type = FIELD_TYPE_NUMBER;
    return *this;
}

Field& Field::operator=(float value) {
    std::ostringstream oss;
    oss << value;
    set_value(oss.str());
    _type = FIELD_TYPE_NUMBER;
    return *this;
}

// Comparison operators
bool Field::operator==(const Field& other) const {
    return _value == other._value && _is_null == other._is_null;
}

bool Field::operator!=(const Field& other) const {
    return !(*this == other);
}

// Utility methods
std::string Field::to_string() const {
    return _value;
}

int Field::to_int() const {
    if (_is_null || _value.empty()) {
        return 0;
    }
    return std::atoi(_value.c_str());
}

long Field::to_long() const {
    if (_is_null || _value.empty()) {
        return 0L;
    }
    return std::atol(_value.c_str());
}

double Field::to_double() const {
    if (_is_null || _value.empty()) {
        return 0.0;
    }
    return std::atof(_value.c_str());
}

float Field::to_float() const {
    if (_is_null || _value.empty()) {
        return 0.0f;
    }
    return static_cast<float>(std::atof(_value.c_str()));
}

}
