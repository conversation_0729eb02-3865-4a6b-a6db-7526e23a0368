#include "conn.hh"
#include "query.hh"
#include "error.hh"
#include "log.hh"
#include <sstream>

namespace ORAPP {

// Static members
oracle::occi::Environment* Connection::_global_env = nullptr;
int Connection::_connection_count = 0;

// Constructor
Connection::Connection()
    : _env(nullptr), _conn(nullptr), _tns(""), _user(""), _pass(""), _version(""),
      _state(CONN_STATE_DISCONNECTED) {
    clear_error();
    initialize_environment();
    if (has_error()) {
        _state = CONN_STATE_ERROR;
    }
}

Connection::~Connection() {
    try {
        disconnect();
        cleanup_environment();
    } catch (...) {
        // Never throw from destructor
        // Log error if possible, but don't throw
    }
}

// Connection operations
bool Connection::connect(const char* tns, const char* user, const char* pass) {
    return connect(
        tns ? std::string(tns) : std::string(""),
        user ? std::string(user) : std::string(""),
        pass ? std::string(pass) : std::string("")
    );
}

bool Connection::connect(const std::string& tns, const std::string& user, const std::string& pass) {
    log_debug_step("Connection start", ("Attempting to connect: " + user + "@" + tns).c_str());

    try {
        clear_error();

        if (_state == CONN_STATE_CONNECTED) {
            log_debug_step("Connection cleanup", "Disconnecting existing connection");
            disconnect();
        }

        if (!_global_env) {
            set_error("Oracle environment not initialized");
            log_error("Oracle environment not initialized");
            return false;
        }

        if (tns.empty() || user.empty()) {
            set_error("TNS, user, and password are required");
            log_error("Missing required connection parameters");
            return false;
        }

        log_message(("Connecting to Oracle database: " + user + "@" + tns).c_str());
        log_debug_step("Connection parameters", ("User: " + user + ", TNS: " + tns).c_str());
        log_memory_info("Before createConnection", _global_env);

        // Create Oracle connection with detailed error tracking
        log_occi_operation("createConnection", "STARTING");

        try {
            _conn = _global_env->createConnection(user, pass, tns);
            log_occi_operation("createConnection", "SUCCESS");
            log_memory_info("After createConnection", _conn);

        } catch (oracle::occi::SQLException& sql_ex) {
            std::ostringstream oss;
            oss << "Oracle connection failed - Error " << sql_ex.getErrorCode() << ": " << sql_ex.getMessage();
            std::string error_msg = oss.str();
            set_error(error_msg);
            log_error(error_msg.c_str());
            log_occi_operation("createConnection", "FAILED_SQL_EXCEPTION");
            _state = CONN_STATE_ERROR;
            return false;
        } catch (std::bad_alloc& alloc_ex) {
            std::string error_msg = "Memory allocation failed during createConnection";
            set_error(error_msg);
            log_error(error_msg.c_str());
            log_occi_operation("createConnection", "FAILED_BAD_ALLOC");
            _state = CONN_STATE_ERROR;
            return false;
        } catch (std::exception& std_ex) {
            std::string error_msg = "Standard exception during createConnection: " + std::string(std_ex.what());
            set_error(error_msg);
            log_error(error_msg.c_str());
            log_occi_operation("createConnection", "FAILED_STD_EXCEPTION");
            _state = CONN_STATE_ERROR;
            return false;
        } catch (...) {
            std::string error_msg = "Unknown exception during createConnection";
            set_error(error_msg);
            log_error(error_msg.c_str());
            log_occi_operation("createConnection", "FAILED_UNKNOWN_EXCEPTION");
            _state = CONN_STATE_ERROR;
            return false;
        }

        if (!_conn) {
            set_error("Failed to create Oracle connection - null connection returned");
            log_error("createConnection returned null pointer");
            log_occi_operation("createConnection", "FAILED_NULL_RETURN");
            _state = CONN_STATE_ERROR;
            return false;
        }

        log_debug_step("Connection established", "Setting connection state");
        _tns = tns;
        _user = user;
        _pass = pass;
        _state = CONN_STATE_CONNECTED;

        // Get Oracle version safely
        log_debug_step("Version retrieval", "Getting Oracle version");
        update_version();

        log_message(("Successfully connected to Oracle database, version: " + _version).c_str());
        log_debug_step("Connection complete", "All steps completed successfully");

        return true;

    } catch (std::bad_alloc& ex) {
        std::string error_msg = "Memory allocation failed during Oracle connection";
        set_error(error_msg);
        log_error(error_msg.c_str());
        log_debug_step("Connection failed", "Bad allocation exception");
        _state = CONN_STATE_ERROR;
        return false;
    } catch (std::exception& ex) {
        std::string error_msg = "Standard exception during Oracle connection: " + std::string(ex.what());
        set_error(error_msg);
        log_error(error_msg.c_str());
        log_debug_step("Connection failed", "Standard exception");
        _state = CONN_STATE_ERROR;
        return false;
    } catch (...) {
        std::string error_msg = "Unknown exception during Oracle connection";
        set_error(error_msg);
        log_error(error_msg.c_str());
        log_debug_step("Connection failed", "Unknown exception");
        _state = CONN_STATE_ERROR;
        return false;
    }
}

bool Connection::disconnect() {
    log_debug_step("Disconnect start", "Beginning disconnection process");

    try {
        clear_error();

        if (_conn && _global_env && _state == CONN_STATE_CONNECTED) {
            log_message("Disconnecting from Oracle database");
            log_memory_info("Before terminateConnection", _conn);
            log_occi_operation("terminateConnection", "STARTING");

            try {
                _global_env->terminateConnection(_conn);
                log_occi_operation("terminateConnection", "SUCCESS");
            } catch (oracle::occi::SQLException& ex) {
                std::string error_msg = "Error during connection termination: " + std::string(ex.getMessage());
                log_error(error_msg.c_str());
                log_occi_operation("terminateConnection", "FAILED_SQL_EXCEPTION");
                // Continue with cleanup even if termination fails
            } catch (std::exception& ex) {
                std::string error_msg = "Standard exception during connection termination: " + std::string(ex.what());
                log_error(error_msg.c_str());
                log_occi_operation("terminateConnection", "FAILED_STD_EXCEPTION");
                // Continue with cleanup
            } catch (...) {
                log_error("Unknown error during connection termination");
                log_occi_operation("terminateConnection", "FAILED_UNKNOWN_EXCEPTION");
                // Continue with cleanup
            }

            _conn = nullptr;
            log_memory_info("After terminateConnection cleanup");
        }

        log_debug_step("State cleanup", "Clearing connection state");
        _state = CONN_STATE_DISCONNECTED;
        _tns.clear();
        _user.clear();
        _pass.clear();
        _version.clear();

        log_message("Successfully disconnected from Oracle database");
        log_debug_step("Disconnect complete", "All cleanup completed");

        return true;

    } catch (std::exception& ex) {
        std::string error_msg = "Exception during disconnect: " + std::string(ex.what());
        set_error(error_msg);
        log_error(error_msg.c_str());
        log_debug_step("Disconnect failed", "Exception during cleanup");
        // Force cleanup even on error
        _conn = nullptr;
        _state = CONN_STATE_DISCONNECTED;
        return false;
    } catch (...) {
        log_error("Unknown exception during disconnect");
        log_debug_step("Disconnect failed", "Unknown exception");
        // Force cleanup
        _conn = nullptr;
        _state = CONN_STATE_DISCONNECTED;
        return false;
    }
}

// Connection state
bool Connection::is_connected() const {
    return _state == CONN_STATE_CONNECTED && _conn != nullptr;
}

ConnectionState Connection::state() const {
    return _state;
}

// Database information
const std::string& Connection::version() const {
    return _version;
}

const std::string& Connection::tns() const {
    return _tns;
}

const std::string& Connection::user() const {
    return _user;
}

// Query operations
Query* Connection::query() {
    if (!is_connected()) {
        set_error("Not connected to database");
        return nullptr;
    }
    
    try {
        return new Query(_conn);
    } catch (std::exception& ex) {
        set_error(std::string("Failed to create query: ") + ex.what());
        return nullptr;
    }
}

Query* Connection::query(const char* sql) {
    Query* q = query();
    if (q && sql) {
        *q << sql;
    }
    return q;
}

Query* Connection::query(const std::string& sql) {
    Query* q = query();
    if (q) {
        *q << sql;
    }
    return q;
}

// Direct execution
bool Connection::execute(const char* sql) {
    return execute(sql ? std::string(sql) : std::string(""));
}

bool Connection::execute(const std::string& sql) {
    Query* q = query(sql);
    if (!q) {
        return false;
    }
    
    bool result = q->execute();
    delete q;
    return result;
}

// Transaction operations
bool Connection::commit() {
    try {
        if (!is_connected()) {
            set_error("Not connected to database");
            return false;
        }
        
        _conn->commit();
        log_debug("Transaction committed");
        return true;
        
    } catch (oracle::occi::SQLException& ex) {
        std::ostringstream oss;
        oss << "Commit failed " << ex.getErrorCode() << ": " << ex.getMessage();
        set_error(oss.str());
        return false;
    }
}

bool Connection::rollback() {
    try {
        if (!is_connected()) {
            set_error("Not connected to database");
            return false;
        }
        
        _conn->rollback();
        log_debug("Transaction rolled back");
        return true;
        
    } catch (oracle::occi::SQLException& ex) {
        std::ostringstream oss;
        oss << "Rollback failed " << ex.getErrorCode() << ": " << ex.getMessage();
        set_error(oss.str());
        return false;
    }
}

bool Connection::auto_commit(bool enable) {
    // OCCI doesn't have setAutoCommit method
    // Auto-commit is controlled by calling commit() or rollback() explicitly
    if (!is_connected()) {
        set_error("Not connected to database");
        return false;
    }

    log_debug(enable ? "Auto-commit mode requested (manual commit/rollback required)" : "Manual commit mode");
    return true;
}

// Error handling
std::string Connection::error() const {
    return _last_error;
}

bool Connection::has_error() const {
    return !_last_error.empty();
}

// Internal access
oracle::occi::Connection* Connection::get_occi_connection() const {
    return _conn;
}

// Private methods
void Connection::initialize_environment() {
    if (!_global_env) {
        log_debug_step("Environment initialization", "Starting OCCI environment creation");
        log_memory_info("Before Environment::createEnvironment");

        try {
            log_occi_operation("Environment::createEnvironment", "STARTING");

            // Try the most basic environment creation first
            _global_env = oracle::occi::Environment::createEnvironment(
                oracle::occi::Environment::DEFAULT
            );

            log_occi_operation("Environment::createEnvironment", "SUCCESS");
            log_memory_info("After Environment::createEnvironment", _global_env);

            if (_global_env) {
                log_message("Oracle OCCI environment initialized successfully");
            } else {
                log_error("Environment::createEnvironment returned null pointer");
                return;
            }

        } catch (oracle::occi::SQLException& ex) {
            std::ostringstream oss;
            oss << "OCCI SQLException during environment init: " << ex.getErrorCode() << " - " << ex.getMessage();
            log_error(oss.str().c_str());
            log_occi_operation("Environment::createEnvironment", "FAILED_SQL_EXCEPTION");
            return;
        } catch (std::bad_alloc& ex) {
            log_error("Memory allocation failed during environment initialization");
            log_occi_operation("Environment::createEnvironment", "FAILED_BAD_ALLOC");
            return;
        } catch (std::exception& ex) {
            std::string error_msg = "Standard exception during environment init: " + std::string(ex.what());
            log_error(error_msg.c_str());
            log_occi_operation("Environment::createEnvironment", "FAILED_STD_EXCEPTION");
            return;
        } catch (...) {
            log_error("Unknown exception during environment initialization");
            log_occi_operation("Environment::createEnvironment", "FAILED_UNKNOWN_EXCEPTION");
            return;
        }

        log_debug_step("Environment initialization", "Completed successfully");
    }
    _connection_count++;
}

void Connection::cleanup_environment() {
    log_debug_step("Environment cleanup", "Starting environment termination");

    _connection_count--;
    if (_connection_count <= 0 && _global_env) {
        log_memory_info("Before Environment::terminateEnvironment", _global_env);
        log_occi_operation("Environment::terminateEnvironment", "STARTING");

        try {
            // Ensure all connections are closed before terminating environment
            if (_conn) {
                log_debug_step("Connection cleanup", "Terminating remaining connection");
                try {
                    _global_env->terminateConnection(_conn);
                    _conn = nullptr;
                    log_occi_operation("terminateConnection", "SUCCESS_IN_CLEANUP");
                } catch (...) {
                    // Ignore connection termination errors during cleanup
                    _conn = nullptr;
                    log_occi_operation("terminateConnection", "FAILED_IN_CLEANUP");
                }
            }

            // Terminate environment safely
            oracle::occi::Environment::terminateEnvironment(_global_env);
            _global_env = nullptr;
            log_occi_operation("Environment::terminateEnvironment", "SUCCESS");
            log_message("Oracle OCCI environment terminated");

        } catch (oracle::occi::SQLException& ex) {
            std::string error_msg = "OCCI SQLException during environment cleanup: " + std::string(ex.getMessage());
            log_error(error_msg.c_str());
            log_occi_operation("Environment::terminateEnvironment", "FAILED_SQL_EXCEPTION");
            _global_env = nullptr; // Force null to prevent double cleanup
        } catch (std::exception& ex) {
            std::string error_msg = "Standard exception during environment cleanup: " + std::string(ex.what());
            log_error(error_msg.c_str());
            log_occi_operation("Environment::terminateEnvironment", "FAILED_STD_EXCEPTION");
            _global_env = nullptr;
        } catch (...) {
            log_error("Unknown exception during environment cleanup");
            log_occi_operation("Environment::terminateEnvironment", "FAILED_UNKNOWN_EXCEPTION");
            _global_env = nullptr;
        }

        _connection_count = 0;
        log_debug_step("Environment cleanup", "Completed");
    }
}

void Connection::set_error(const std::string& error) {
    _last_error = error;
    ErrorHandler::set_error(error);
}

void Connection::clear_error() {
    _last_error.clear();
}

void Connection::update_version() {
    log_debug_step("Version query", "Starting Oracle version retrieval");

    oracle::occi::Statement* stmt = nullptr;
    oracle::occi::ResultSet* rs = nullptr;

    try {
        if (_conn) {
            log_occi_operation("createStatement", "STARTING");
            log_memory_info("Before createStatement", _conn);

            stmt = _conn->createStatement("SELECT BANNER FROM V$VERSION WHERE ROWNUM = 1");
            log_occi_operation("createStatement", "SUCCESS");
            log_memory_info("After createStatement", stmt);

            if (stmt) {
                log_occi_operation("executeQuery", "STARTING");
                rs = stmt->executeQuery();
                log_occi_operation("executeQuery", "SUCCESS");
                log_memory_info("After executeQuery", rs);

                if (rs && rs->next()) {
                    log_occi_operation("getString", "STARTING");
                    _version = rs->getString(1);
                    log_occi_operation("getString", "SUCCESS");
                    log_debug_step("Version retrieved", _version.c_str());
                } else {
                    _version = "Oracle Database (version query failed)";
                    log_debug_step("Version query", "No results returned");
                }

                // Clean up resources safely
                if (rs) {
                    log_occi_operation("closeResultSet", "STARTING");
                    stmt->closeResultSet(rs);
                    log_occi_operation("closeResultSet", "SUCCESS");
                    rs = nullptr;
                }
                if (stmt) {
                    log_occi_operation("terminateStatement", "STARTING");
                    _conn->terminateStatement(stmt);
                    log_occi_operation("terminateStatement", "SUCCESS");
                    stmt = nullptr;
                }
            } else {
                _version = "Oracle Database (statement creation failed)";
                log_debug_step("Version query", "Statement creation failed");
            }
        } else {
            _version = "Oracle Database (no connection)";
            log_debug_step("Version query", "No connection available");
        }

        log_debug_step("Version query", "Completed successfully");

    } catch (oracle::occi::SQLException& ex) {
        _version = "Oracle Database (version unknown - SQL error)";
        std::string error_msg = "Failed to get Oracle version: " + std::string(ex.getMessage());
        log_error(error_msg.c_str());
        log_occi_operation("version_query", "FAILED_SQL_EXCEPTION");

        // Clean up on error
        try {
            if (rs && stmt) {
                stmt->closeResultSet(rs);
                log_occi_operation("closeResultSet", "CLEANUP_SUCCESS");
            }
            if (stmt && _conn) {
                _conn->terminateStatement(stmt);
                log_occi_operation("terminateStatement", "CLEANUP_SUCCESS");
            }
        } catch (...) {
            log_occi_operation("version_cleanup", "FAILED");
        }
    } catch (std::exception& ex) {
        _version = "Oracle Database (version unknown - exception)";
        std::string error_msg = "Exception while getting Oracle version: " + std::string(ex.what());
        log_error(error_msg.c_str());
        log_occi_operation("version_query", "FAILED_STD_EXCEPTION");

        // Clean up on error
        try {
            if (rs && stmt) stmt->closeResultSet(rs);
            if (stmt && _conn) _conn->terminateStatement(stmt);
        } catch (...) {
            // Ignore cleanup errors
        }
    } catch (...) {
        _version = "Oracle Database (version unknown)";
        log_error("Unknown exception while getting Oracle version");
        log_occi_operation("version_query", "FAILED_UNKNOWN_EXCEPTION");

        // Clean up on error
        try {
            if (rs && stmt) stmt->closeResultSet(rs);
            if (stmt && _conn) _conn->terminateStatement(stmt);
        } catch (...) {
            // Ignore cleanup errors
        }
    }
}

}
