cmake_minimum_required(VERSION 3.10)

# OCCI Library Project
project(libocci VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Oracle OCCI
find_path(ORACLE_INCLUDE_DIR
    NAMES occi.h
    PATHS
        /usr/include/oracle/21/client64
        /usr/include/oracle/19/client64
        /usr/include/oracle/18/client64
        /usr/include/oracle/12.2/client64
        /usr/include/oracle/12.1/client64
        /usr/include/oracle/11.2/client64
        $ENV{ORACLE_HOME}/include
        $ENV{ORACLE_HOME}/rdbms/public
        $ENV{ORACLE_HOME}/rdbms/demo
    DOC "Oracle OCCI include directory"
)

find_library(ORACLE_OCCI_LIBRARY
    NAMES occi
    PATHS
        /usr/lib/oracle/21/client64/lib
        /usr/lib/oracle/19/client64/lib
        /usr/lib/oracle/18/client64/lib
        /usr/lib/oracle/12.2/client64/lib
        /usr/lib/oracle/12.1/client64/lib
        /usr/lib/oracle/11.2/client64/lib
        $ENV{ORACLE_HOME}/lib
    DOC "Oracle OCCI library"
)

find_library(ORACLE_CLNTSH_LIBRARY
    NAMES clntsh
    PATHS
        /usr/lib/oracle/21/client64/lib
        /usr/lib/oracle/19/client64/lib
        /usr/lib/oracle/18/client64/lib
        /usr/lib/oracle/12.2/client64/lib
        /usr/lib/oracle/12.1/client64/lib
        /usr/lib/oracle/11.2/client64/lib
        $ENV{ORACLE_HOME}/lib
    DOC "Oracle client library"
)

# Check if Oracle libraries were found
if(NOT ORACLE_INCLUDE_DIR)
    message(FATAL_ERROR "Oracle OCCI include directory not found")
endif()

if(NOT ORACLE_OCCI_LIBRARY)
    message(FATAL_ERROR "Oracle OCCI library not found")
endif()

if(NOT ORACLE_CLNTSH_LIBRARY)
    message(FATAL_ERROR "Oracle client library not found")
endif()

message(STATUS "Oracle include directory: ${ORACLE_INCLUDE_DIR}")
message(STATUS "Oracle OCCI library: ${ORACLE_OCCI_LIBRARY}")
message(STATUS "Oracle client library: ${ORACLE_CLNTSH_LIBRARY}")

# Source files
set(OCCI_SOURCES
    constants.cpp
    log.cpp
    error.cpp
    field.cpp
    row.cpp
    query.cpp
    conn.cpp
)

# Header files
set(OCCI_HEADERS
    constants.hh
    log.hh
    error.hh
    field.hh
    row.hh
    query.hh
    conn.hh
    occi.hh
)

# Create static library
add_library(occi STATIC ${OCCI_SOURCES})

# Include directories
target_include_directories(occi
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${ORACLE_INCLUDE_DIR}
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# Link libraries
target_link_libraries(occi
    PUBLIC
        ${ORACLE_OCCI_LIBRARY}
        ${ORACLE_CLNTSH_LIBRARY}
)

# Compiler flags
target_compile_options(occi PRIVATE
    -D_REENTRANT=1
)

# Set library properties
set_target_properties(occi PROPERTIES
    OUTPUT_NAME "occi"
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/lib"
    POSITION_INDEPENDENT_CODE ON
)

# Create lib directory if it doesn't exist
file(MAKE_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/lib")

# Install rules (optional)
install(TARGETS occi
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES ${OCCI_HEADERS}
    DESTINATION include/occi
)

# Print build information
message(STATUS "OCCI library will be built as: lib${CMAKE_STATIC_LIBRARY_PREFIX}occi${CMAKE_STATIC_LIBRARY_SUFFIX}")
message(STATUS "Output directory: ${CMAKE_CURRENT_SOURCE_DIR}/lib")
