#ifndef OCCI_ERROR_HH
#define OCCI_ERROR_HH

/*
 * OCCI Error handling - Compatible with orapp interface
 */

#include <string>
#include <exception>

namespace ORAPP {

class OCCIException : public std::exception {
private:
    std::string _message;
    int _error_code;

public:
    OCCIException(const std::string& message, int error_code = -1);
    virtual ~OCCIException() throw();
    
    virtual const char* what() const throw();
    int error_code() const;
    const std::string& message() const;
};

// Error handling utilities
class ErrorHandler {
private:
    static std::string _last_error;
    static int _last_error_code;

public:
    static void set_error(const std::string& error, int code = -1);
    static const std::string& get_last_error();
    static int get_last_error_code();
    static void clear_error();
};

}

#endif
