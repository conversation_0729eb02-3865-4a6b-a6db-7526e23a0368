#include "log.hh"
#include <iostream>
#include <cstdio>
#include <sstream>
#include <ctime>
#include <cstring>

namespace ORAPP {

// Global log function pointer
static log_func_t g_log_func = nullptr;

// Default log function
static void default_log_func(const char *message) {
    std::cerr << "[OCCI] " << message << std::endl;
}

void log_to(log_func_t func) {
    g_log_func = func;
}

void log_message(const char *message) {
    if (g_log_func) {
        g_log_func(message);
    } else {
        default_log_func(message);
    }
}

void log_error(const char *error) {
    char buffer[1024];
    snprintf(buffer, sizeof(buffer), "ERROR: %s", error);
    log_message(buffer);
}

void log_debug(const char *debug) {
    char buffer[4096];  // Increased buffer size for long SQL statements
    snprintf(buffer, sizeof(buffer), "DEBUG: %s", debug);
    log_message(buffer);
}

log_func_t get_log_func() {
    return g_log_func ? g_log_func : default_log_func;
}

// Enhanced debugging functions for OCCI operations
void log_debug_step(const char* step, const char* details) {
    if (step) {
        // For SQL debugging, write directly to file to avoid truncation
        if (details && (strstr(step, "Executing SQL") || strstr(step, "SQL prepared") || strstr(step, "SQL Debug") || strstr(step, "SQL First") || strstr(step, "SQL Last"))) {
            FILE* debug_file = fopen("/tmp/occi_sql_debug.log", "a");
            if (debug_file) {
                time_t now = time(NULL);
                struct tm* tm_info = localtime(&now);
                char timestamp[64];
                strftime(timestamp, sizeof(timestamp), "%Y%m%d,%H:%M:%S", tm_info);
                fprintf(debug_file, "[%s] - [DEBUG_STEP] %s - Length: %zu - [",
                       timestamp, step, strlen(details));
                fwrite(details, 1, strlen(details), debug_file);
                fprintf(debug_file, "]\n");
                fclose(debug_file);
            }
        }

        std::string msg = "[DEBUG_STEP] ";
        msg += step;
        if (details) {
            msg += " - ";
            msg += details;
        }
        log_message(msg.c_str());
    }
}

void log_memory_info(const char* operation, void* ptr) {
    if (operation) {
        std::ostringstream oss;
        oss << "[MEMORY] " << operation;
        if (ptr) {
            oss << " - ptr: " << ptr;
        }
        log_message(oss.str().c_str());
    }
}

void log_occi_operation(const char* operation, const char* status) {
    if (operation && status) {
        std::string msg = "[OCCI] ";
        msg += operation;
        msg += " - ";
        msg += status;
        log_message(msg.c_str());
    }
}

}
