#ifndef OCCI_LOG_HH
#define OCCI_LOG_HH

/*
 * OCCI Logging - Compatible with orapp interface
 */

#include <string>

namespace ORAPP {

// Log function pointer type
typedef void (*log_func_t)(const char *);

// Set log function
void log_to(log_func_t func);

// Internal logging functions
void log_message(const char *message);
void log_error(const char *error);
void log_debug(const char *debug);

// Enhanced debugging functions for OCCI operations
void log_debug_step(const char* step, const char* details = nullptr);
void log_memory_info(const char* operation, void* ptr = nullptr);
void log_occi_operation(const char* operation, const char* status);

// Get current log function
log_func_t get_log_func();

}

#endif
