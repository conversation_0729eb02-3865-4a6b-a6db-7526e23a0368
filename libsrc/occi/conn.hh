#ifndef OCCI_CONN_HH
#define OCCI_CONN_HH

/*
 * OCCI Connection - Compatible with orapp interface
 */

#include <string>
#include <occi.h>
#include "constants.hh"

namespace ORAPP {

// Forward declaration
class Query;

class Connection {
private:
    oracle::occi::Environment* _env;
    oracle::occi::Connection* _conn;
    
    std::string _tns;
    std::string _user;
    std::string _pass;
    std::string _version;
    
    ConnectionState _state;
    std::string _last_error;
    
    // Static environment management
    static oracle::occi::Environment* _global_env;
    static int _connection_count;

public:
    Connection();
    ~Connection();

    // Connection operations
    bool connect(const char* tns, const char* user, const char* pass);
    bool connect(const std::string& tns, const std::string& user, const std::string& pass);
    bool disconnect();
    
    // Connection state
    bool is_connected() const;
    ConnectionState state() const;
    
    // Database information
    const std::string& version() const;
    const std::string& tns() const;
    const std::string& user() const;
    
    // Query operations
    Query* query();
    Query* query(const char* sql);
    Query* query(const std::string& sql);
    
    // Direct execution
    bool execute(const char* sql);
    bool execute(const std::string& sql);
    
    // Transaction operations
    bool commit();
    bool rollback();
    bool auto_commit(bool enable);
    
    // Error handling
    std::string error() const;
    bool has_error() const;
    
    // Internal access (for Query class)
    oracle::occi::Connection* get_occi_connection() const;

private:
    // Internal methods
    void initialize_environment();
    void cleanup_environment();
    void set_error(const std::string& error);
    void clear_error();
    void update_version();
};

}

#endif
