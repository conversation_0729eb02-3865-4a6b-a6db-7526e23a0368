#ifndef OCCI_CONSTANTS_HH
#define OCCI_CONSTANTS_HH

/*
 * OCCI Constants - Compatible with orapp interface
 */

namespace ORAPP {

// Error codes
const int ORAPP_SUCCESS = 0;
const int ORAPP_ERROR = -1;
const int ORAPP_NO_DATA = 1;

// Field types
enum FieldType {
    FIELD_TYPE_UNKNOWN = 0,
    FIELD_TYPE_VARCHAR = 1,
    FIELD_TYPE_NUMBER = 2,
    FIELD_TYPE_DATE = 3,
    FIELD_TYPE_LONG = 4,
    FIELD_TYPE_CLOB = 5,
    FIELD_TYPE_BLOB = 6
};

// Connection states
enum ConnectionState {
    CONN_STATE_DISCONNECTED = 0,
    CONN_STATE_CONNECTED = 1,
    CONN_STATE_ERROR = 2
};

// Query states
enum QueryState {
    QUERY_STATE_INIT = 0,
    QUERY_STATE_PREPARED = 1,
    QUERY_STATE_EXECUTED = 2,
    QUERY_STATE_FETCHING = 3,
    QUERY_STATE_FINISHED = 4,
    QUERY_STATE_ERROR = 5
};

}

#endif
