#ifndef OCCI_ROW_HH
#define OCCI_ROW_HH

/*
 * OCCI Row - Compatible with orapp interface
 */

#include <vector>
#include <map>
#include <string>
#include "field.hh"

namespace ORAPP {

class Row {
private:
    std::vector<Field> _fields;
    std::map<std::string, int> _field_map;
    int _current_field;

public:
    Row();
    ~Row();

    // Field access by index
    Field& operator[](int index);
    const Field& operator[](int index) const;

    // Field access by name
    Field& operator[](const std::string& name);
    const Field& operator[](const std::string& name) const;

    // Field management
    void add_field(const Field& field);
    void add_field(const std::string& name, const std::string& value, FieldType type = FIELD_TYPE_VARCHAR);
    
    // Field count
    int field_count() const;
    int size() const;
    int width() const;  // orapp compatibility

    // Field iteration
    void reset();
    bool next_field();
    Field& current_field();
    const Field& current_field() const;

    // Field lookup
    bool has_field(const std::string& name) const;
    int get_field_index(const std::string& name) const;
    Field& get_field(const std::string& name);
    const Field& get_field(const std::string& name) const;

    // orapp compatibility methods
    const char* name(int index) const;  // Get field name by index

    // Row operations
    void clear();
    bool empty() const;

    // Utility methods
    std::string to_string() const;
    void print() const;

    // Iterator support
    typedef std::vector<Field>::iterator iterator;
    typedef std::vector<Field>::const_iterator const_iterator;
    
    iterator begin();
    iterator end();
    const_iterator begin() const;
    const_iterator end() const;
};

}

#endif
