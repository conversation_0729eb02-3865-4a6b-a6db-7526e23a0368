#ifndef OCCI_FIELD_HH
#define OCCI_FIELD_HH

/*
 * OCCI Field - Compatible with orapp interface
 */

#include <string>
#include <occi.h>
#include "constants.hh"

namespace ORAPP {

class Field {
private:
    std::string _name;
    std::string _value;
    FieldType _type;
    bool _is_null;
    int _size;
    int _precision;
    int _scale;

public:
    Field();
    Field(const std::string& name, const std::string& value, FieldType type);
    ~Field();

    // Name operations
    const std::string& name() const;
    void set_name(const std::string& name);

    // Value operations
    const std::string& value() const;
    void set_value(const std::string& value);
    
    // Type operations
    FieldType type() const;
    void set_type(FieldType type);

    // Null operations
    bool is_null() const;
    void set_null(bool null);

    // Size and precision
    int size() const;
    void set_size(int size);
    int precision() const;
    void set_precision(int precision);
    int scale() const;
    void set_scale(int scale);

    // Conversion operators
    operator const char*() const;
    operator char*() const;  // orapp compatibility
    operator std::string() const;
    operator int() const;
    operator long() const;
    operator double() const;
    operator float() const;

    // Assignment operators
    Field& operator=(const std::string& value);
    Field& operator=(const char* value);
    Field& operator=(int value);
    Field& operator=(long value);
    Field& operator=(double value);
    Field& operator=(float value);

    // Comparison operators
    bool operator==(const Field& other) const;
    bool operator!=(const Field& other) const;

    // Utility methods
    std::string to_string() const;
    int to_int() const;
    long to_long() const;
    double to_double() const;
    float to_float() const;
};

}

#endif
