# OCCI Library

Oracle C++ Call Interface (OCCI) wrapper library that provides compatibility with the existing orapp interface.

## Overview

This library provides a modern C++ interface to Oracle databases using OCCI while maintaining compatibility with the existing orapp library interface. It allows for easy migration from orapp to OCCI without changing existing application code.

## Features

- **Compatible Interface**: Drop-in replacement for orapp library
- **Modern OCCI Backend**: Uses Oracle's official C++ interface
- **Error Handling**: Comprehensive error handling and logging
- **Memory Management**: Automatic resource management
- **Thread Safe**: Designed for multi-threaded applications

## Requirements

- Oracle Instant Client 11.2 or later
- Oracle OCCI libraries
- C++11 compatible compiler
- CMake 3.10 or later (for CMake build)

## Installation

### Using CMake

```bash
cd libsrc/occi
mkdir build
cd build
cmake ..
make
```

### Using Makefile

```bash
cd libsrc/occi
make
```

## Usage

The library provides the same interface as the original orapp library:

```cpp
#include "occi.hh"

// Set up logging
ORAPP::log_to(your_log_function);

// Create connection
ORAPP::Connection db;
if (!db.connect("your_tns", "username", "password")) {
    std::cerr << "Connection failed: " << db.error() << std::endl;
    return -1;
}

// Execute query
ORAPP::Query* query = db.query();
*query << "SELECT * FROM your_table WHERE id = " << 123;

if (query->execute()) {
    while (query->next()) {
        ORAPP::Row& row = query->row();
        std::cout << "Column 1: " << row[0].value() << std::endl;
        std::cout << "Column 2: " << row[1].value() << std::endl;
    }
}

delete query;
db.disconnect();
```

## Migration from orapp

To migrate from orapp to occi:

1. Replace `#include "orapp.hh"` with `#include "occi.hh"`
2. Link with `-locci` instead of `-lorapp`
3. Ensure Oracle OCCI libraries are available
4. No code changes required - the interface is identical

## Configuration

### Environment Variables

- `ORACLE_HOME`: Oracle installation directory
- `LD_LIBRARY_PATH`: Should include Oracle library path

### Oracle Instant Client Setup

```bash
# Example for Oracle 21c Instant Client
export ORACLE_HOME=/usr/lib/oracle/21/client64
export LD_LIBRARY_PATH=$ORACLE_HOME/lib:$LD_LIBRARY_PATH
```

## Building Applications

### CMake

```cmake
find_library(OCCI_LIBRARY occi PATHS ${CMAKE_SOURCE_DIR}/../libsrc/occi/lib)
target_link_libraries(your_app ${OCCI_LIBRARY} occi clntsh)
```

### Makefile

```makefile
LIBS = -L../libsrc/occi/lib -locci -locci -lclntsh
```

## Error Handling

The library provides comprehensive error handling:

- Connection errors are reported through `Connection::error()`
- Query errors are reported through `Query::error()`
- Global error handling through `ErrorHandler` class
- Logging integration for debugging

## Thread Safety

The library is designed to be thread-safe:

- Each connection should be used by a single thread
- Multiple connections can be used simultaneously
- Global environment is managed automatically

## Performance Considerations

- Connection pooling is recommended for high-load applications
- Prepared statements are used internally for better performance
- Memory is managed automatically to prevent leaks

## Troubleshooting

### Common Issues

1. **Oracle libraries not found**
   - Ensure Oracle Instant Client is installed
   - Check `LD_LIBRARY_PATH` includes Oracle lib directory

2. **Compilation errors**
   - Verify Oracle include directory is correct
   - Check C++11 compiler support

3. **Runtime connection errors**
   - Verify TNS configuration
   - Check database connectivity
   - Validate credentials

### Debug Mode

Enable debug logging to troubleshoot issues:

```cpp
ORAPP::log_to([](const char* msg) {
    std::cerr << "[DEBUG] " << msg << std::endl;
});
```

## License

This library is provided as-is for internal use. See the main project license for details.

## Support

For issues and questions, please refer to the main project documentation or contact the development team.
