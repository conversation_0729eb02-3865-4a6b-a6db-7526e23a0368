cmake_minimum_required(VERSION 3.16)
project(orapp)

set(CMAKE_CXX_STANDARD 98)

# Oracle 환경 확인
if(NOT DEFINED ENV{ORACLE_HOME})
    message(FATAL_ERROR "ORACLE_HOME environment variable is not set")
endif()

set(ORACLE_HOME $ENV{ORACLE_HOME})
set(PROC_INCLUDE "/usr/include/oracle/21/client64")

# 컴파일 플래그 설정 (Makefile의 GEN_FLAGS 반영)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -m64 -fno-exceptions -fno-rtti -D_REENTRANT=1 -O2 -w")

# Include 디렉토리 설정
include_directories(
    ${ORACLE_HOME}/rdbms/demo
    ${ORACLE_HOME}/rdbms/public
    ${PROC_INCLUDE}
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# 라이브러리 디렉토리 설정
link_directories(
    ${ORACLE_HOME}/lib
)

# 소스 파일들 정의
set(ORAPP_SOURCES
    conn.cc
    constants.cc
    error.cc
    field.cc
    log.cc
    query.cc
    row.cc
)

# 헤더 파일들 정의
set(ORAPP_HEADERS
    conn.hh
    constants.hh
    error.hh
    field.hh
    log.hh
    query.hh
    row.hh
    orapp.hh
)

# orapp 정적 라이브러리 생성
add_library(liborapp STATIC ${ORAPP_SOURCES})
set_target_properties(liborapp PROPERTIES OUTPUT_NAME orapp)

# Oracle 라이브러리 링크
target_link_libraries(liborapp 
    clntsh
    dl
)

# 출력 디렉토리 설정 (현재 디렉토리로 설정)
set_target_properties(liborapp PROPERTIES
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
)

# 테스트 프로그램 (선택사항)
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/test.cc)
    add_executable(orapp_test test.cc)
    target_link_libraries(orapp_test 
        liborapp
        clntsh
        dl
        pthread
    )
    set_target_properties(orapp_test PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        OUTPUT_NAME test
    )
endif()

# $HOME/library에 설치하는 타겟
add_custom_target(install_orapp
    COMMAND ${CMAKE_COMMAND} -E make_directory $ENV{HOME}/library
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/liborapp.a $ENV{HOME}/library/
    # 헤더 파일들 복사
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/conn.hh $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/constants.hh $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/error.hh $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/field.hh $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/log.hh $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/query.hh $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/row.hh $ENV{HOME}/library/
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/orapp.hh $ENV{HOME}/library/
    DEPENDS liborapp
    COMMENT "Installing orapp library and headers to $HOME/library"
)

# clean 타겟
add_custom_target(clean_orapp
    COMMAND ${CMAKE_COMMAND} -E remove ${CMAKE_CURRENT_SOURCE_DIR}/*.o
    COMMAND ${CMAKE_COMMAND} -E remove ${CMAKE_CURRENT_SOURCE_DIR}/liborapp.a
    COMMAND ${CMAKE_COMMAND} -E remove ${CMAKE_CURRENT_SOURCE_DIR}/liborapp.so
    COMMAND ${CMAKE_COMMAND} -E remove ${CMAKE_CURRENT_SOURCE_DIR}/test
    COMMENT "Cleaning orapp build artifacts"
)
