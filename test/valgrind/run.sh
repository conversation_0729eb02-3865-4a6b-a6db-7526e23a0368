#------------------------------------------------------------------------------
# Copyright (c) 2017, 2022, Oracle and/or its affiliates.
#
# This software is dual-licensed to you under the Universal Permissive License
# (UPL) 1.0 as shown at https://oss.oracle.com/licenses/upl and Apache License
# 2.0 as shown at http://www.apache.org/licenses/LICENSE-2.0. You may choose
# either license.
#
# If you elect to accept the software under the Apache License, Version 2.0,
# the following applies:
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
#------------------------------------------------------------------------------
#
# Sample script for running valgrind.
#
#------------------------------------------------------------------------------

echo "Running valgrind on $1..."

valgrind \
    --num-callers=40 \
    --error-markers=-------------------------,------------------------- \
    --redzone-size=256 \
    --leak-check=yes \
    --track-origins=yes \
    --free-fill=0xaa \
    --leak-check=full \
    --error-limit=no \
    --trace-children=yes \
    --show-leak-kinds=definite,possible \
    --suppressions=$(dirname $0)/suppressions.txt \
    --gen-suppressions=all \
    $@
