#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <errno.h>
#include <time.h>

#define SOCKET_BUFF 8192

class CKSSocket {
private:
    int m_sockfd;
    char szErrorMsg[256];
    
public:
    CKSSocket() : m_sockfd(-1) {
        memset(szErrorMsg, 0x00, sizeof(szErrorMsg));
    }
    
    ~CKSSocket() {
        if (m_sockfd >= 0) {
            ::close(m_sockfd);
        }
    }
    
    int connectDomain(const char* domain_path) {
        struct sockaddr_un addr;
        
        m_sockfd = socket(AF_UNIX, SOCK_STREAM, 0);
        if (m_sockfd < 0) {
            sprintf(szErrorMsg, "socket create failed: %s", strerror(errno));
            return -1;
        }
        
        memset(&addr, 0, sizeof(addr));
        addr.sun_family = AF_UNIX;
        strncpy(addr.sun_path, domain_path, sizeof(addr.sun_path) - 1);
        
        if (connect(m_sockfd, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
            sprintf(szErrorMsg, "connect failed: %s", strerror(errno));
            ::close(m_sockfd);
            m_sockfd = -1;
            return -1;
        }
        
        return 0;
    }
    
    int send(const char* data, int len) {
        if (m_sockfd < 0) return -1;
        
        int sent = ::send(m_sockfd, data, len, 0);
        if (sent < 0) {
            sprintf(szErrorMsg, "send failed: %s", strerror(errno));
        }
        return sent;
    }
    
    int recv(char* buffer, int len) {
        if (m_sockfd < 0) return -1;
        
        int received = ::recv(m_sockfd, buffer, len, 0);
        if (received < 0) {
            sprintf(szErrorMsg, "recv failed: %s", strerror(errno));
        }
        return received;
    }
    
    int select(int sec = 5, int usec = 0) {
        if (m_sockfd < 0) return -1;
        
        fd_set readfds;
        struct timeval timeout;
        
        FD_ZERO(&readfds);
        FD_SET(m_sockfd, &readfds);
        
        timeout.tv_sec = sec;
        timeout.tv_usec = usec;
        
        return ::select(m_sockfd + 1, &readfds, NULL, NULL, &timeout);
    }
    
    int rcvmsg(char* buff) {
        int ret = this->select();
        if (ret == 0) {
            sprintf(szErrorMsg, "timeout");
            return 0;
        }
        if (ret < 0) {
            sprintf(szErrorMsg, "select failed: %s", strerror(errno));
            return -1;
        }
        
        ret = this->recv(buff, SOCKET_BUFF);
        if (ret == 0) {
            sprintf(szErrorMsg, "close by peer");
            return -1;
        }
        return ret;
    }
    
    void close() {
        if (m_sockfd >= 0) {
            ::close(m_sockfd);
            m_sockfd = -1;
        }
    }
    
    const char* error() {
        return szErrorMsg;
    }
};

// 로그인 요청 패킷 구조체 (간단한 버전)
struct LoginRequest {
    char szCID[16];
    char szPWD[16];
    char classify;
    char padding[7];
};

int main(int argc, char* argv[]) {
    if (argc < 2) {
        printf("Usage: %s <domain_socket_path>\n", argv[0]);
        printf("Example: %s /home/<USER>/CLionProjects/ftalk_up/cfg/kko_ftk/logonDB\n", argv[0]);
        return 1;
    }
    
    const char* domain_path = argv[1];
    CKSSocket client;
    char buffer[SOCKET_BUFF];
    int ret;
    
    printf("=== LogonDB Client Test ===\n");
    printf("Connecting to: %s\n", domain_path);
    
    // 연결 시도
    ret = client.connectDomain(domain_path);
    if (ret != 0) {
        printf("[ERR] Connection failed: %s\n", client.error());
        return 1;
    }
    
    printf("[INF] Connected successfully\n");
    
    // 간단한 텍스트 메시지 전송 (logonDB에서 처리하는 형식)
    const char* test_msg = "ID=testuser&PASSWORD=testpass&REPORT=S";
    
    printf("[INF] Sending test message: %s\n", test_msg);
    
    ret = client.send(test_msg, strlen(test_msg));
    if (ret < 0) {
        printf("[ERR] Send failed: %s\n", client.error());
        client.close();
        return 1;
    }
    
    printf("[INF] Sent %d bytes\n", ret);
    
    // 응답 대기 (5초 타임아웃)
    printf("[INF] Waiting for response (5 second timeout)...\n");
    
    memset(buffer, 0, sizeof(buffer));
    ret = client.rcvmsg(buffer);
    
    if (ret == 0) {
        printf("[WARN] Timeout occurred - no response within 5 seconds\n");
    } else if (ret < 0) {
        printf("[ERR] Receive failed: %s\n", client.error());
    } else {
        printf("[INF] Received %d bytes: %s\n", ret, buffer);
    }
    
    client.close();
    printf("[INF] Connection closed\n");
    
    return 0;
}
