/*
 * Project Name : MetaLand 통합 Shopping Mall System
 *
 * Program Name : ml_monitor.c
 * Comments     : 모든 Process의 상황을 Monitoring한다.
 * ----------------------------------------------------------------------------
 * History
 *    [ 1] Initial Coding 97.12.23 IM,Song
 * 
 */

#include    <stdio.h>
#include    <stdlib.h>
#include    <unistd.h>
#include    <string.h>
#include    <signal.h>
#include    <errno.h>
#include    <time.h>

#include    <code_info.h>
#include    <message_info.h>
#include    <ml_ctrlsub.h>

#define     MIN(a,b)            ((a < b) ? (a) : (b))
#define     MAX(a,b)            ((a > b) ? (a) : (b))

#define     MD  message_info.msg.s_buffer

char        PROCESS_NO[ 7], PROCESS_NAME[36];
FILE *      oFD;
char        current_date[9];
int         loop_sw = 1;

struct      _message_info   message_info;
struct _shm_info *              shm_info;

static void init_rtn(int argc, char *argv[]);
static int  main_rtn(void);
static void end_rtn(void);
static void sig_rtn(int signo);
static void get_timestring(char *fmt, long n, char *s);
static void monitoring(char *buf, int st, int err);
static int log_open();

/*
 * 작업 시작점
 */
int main (int argc, char *argv[])
{
    printf("sms_monitor start...\n");
    if (signal(SIGUSR1, sig_rtn) == SIG_ERR
     || signal(SIGTERM, sig_rtn) == SIG_ERR
     || signal(SIGPIPE, SIG_IGN) == SIG_ERR)
    {
        printf("signal error.<%d><%s>\n", errno, strerror(errno));
        exit(1);
    }

    init_rtn(argc, argv);
    while (loop_sw)
    {
        main_rtn();
    }
    end_rtn();
    return 0;
}

/*
 * 초기화 처리
 */
static void init_rtn(int argc, char *argv[])
{

	/* 자기의 Information을 구한다. */
	if (ml_sub_init(PROCESS_NO, PROCESS_NAME, (char*)0, (char*)0)<0) {
		printf("ml_sub_init Error.\n");
		exit(1);
	}
	if (log_open()) {
		ml_sub_end();
		exit(1);
	}
	printf("%s(%s) start up.\n", PROCESS_NAME, PROCESS_NO);
	monitoring("START UP.", 0, 0);
}

/*
 * 주요작업 처리
 */
char* trim( char* szOrg, int leng )
{
	int i = 0;
	for( i=leng-1 ; i>=0 ; i-- ) {
		if( (szOrg[i]==0x20)||(szOrg[i]==' ')||(szOrg[i]==0x00) ) {
			szOrg[i] = 0x00;
		}
		else break;
	}
	return szOrg;
}

static int main_rtn(void)
{
	char	date[32];

    if ( ml_sub_recv_all((char*)&message_info, sizeof(message_info),60)<=0 ) {
        if (errno == EINTR) {
            log_open();
            return 0;
        }
        printf("%s(%s) recv failed. %d %s.\n", PROCESS_NAME, PROCESS_NO, errno, strerror(errno));
        return 0;
    }

    if (log_open()) {
        end_rtn();
    }

	if(oFD) {
		get_timestring("%04d%02d%02d,%02d:%02d:%02d", time(NULL), date);
		fprintf(oFD,"[%s]:[%s]:[%s] - ",MD.process_no,MD.process_name,trim(date,32));
		MD.message[MD.msg_length] = '\0';
		fprintf(oFD,"[%s]\n",MD.message);
		fflush(oFD);
	}

    return 0;
}

/*
 * 종료작업 처리
 */
static void end_rtn(void)
{
    monitoring("FINISHED.", 0, 0);
	fclose(oFD);
    ml_sub_end();
    exit(0);    
}

static void sig_rtn(int signo)
{
    loop_sw = 0;
}

/* ========================================================================== */
/* Subroutines                                                                */
/* ========================================================================== */

/*
 * 시간 문자열을 출력한다.
 */
static void get_timestring(char *fmt, long n, char *s)
{
#if 0
    struct tm *localt;

    localt = localtime(&n);
    sprintf(s, fmt,
            localt->tm_year + 1900,
            localt->tm_mon + 1,
            localt->tm_mday,
            localt->tm_hour,
            localt->tm_min,
            localt->tm_sec);
//    s[strlen(s)] = ' ';
#endif
	struct timespec tmv;
	struct tm	tp;

	clock_gettime(CLOCK_REALTIME, &tmv);
	localtime_r(&tmv.tv_sec, &tp);
	sprintf(s, "%04d%02d%02d,%02d:%02d:%02d.%09d",
		tp.tm_year+1900, tp.tm_mon+1, tp.tm_mday,
		tp.tm_hour, tp.tm_min, tp.tm_sec,
		(int)tmv.tv_nsec
		);
}

/*
 * 로그화일을 OPEN한다
 */
static int log_open()
{
    char        file[256], date[32], buf[256];

    get_timestring("%04d%02d%02d,%2d:%2d:%2d", time(NULL), date);
    if (strncmp(date, current_date, 8) == 0) return 0;
    if (oFD != 0) fclose(oFD);
    strncpy(current_date, date, 8);
    current_date[8] = '\0';
    strcpy(file, FILEPATH_LOG);
    strcat(file, PROCESS_NAME);
    strcat(file, ".");
    strcat(file, current_date);
    if ((oFD = fopen(file, "a+b")) == NULL)
    {
        fclose(oFD);
        sprintf(buf, "%s fopen error. %d %s\n", file, errno, strerror(errno));
        monitoring(buf, 0, errno);
        return -1;
    }

    return 0;
}

static void monitoring(char *buf, int st, int err)
{
    if (ml_sub_send_moni(buf, strlen(buf), 3, st, err) <= 0)
    {
        printf("%s ml_sub_send_moni error. %d %s\n", PROCESS_NAME, errno, strerror(errno));
    }
}

/* END */
